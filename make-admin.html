<!DOCTYPE html>
<html>
<head>
    <title>Make User Admin - Fetchly</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .container { background: #f9f9f9; padding: 30px; border-radius: 10px; }
        input, button { padding: 10px; margin: 10px 0; width: 100%; box-sizing: border-box; }
        button { background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Make User Admin - Fetchly</h1>
        
        <div class="info">
            <h3>Instructions:</h3>
            <ol>
                <li>Go to Firebase Console → Authentication → Users</li>
                <li>Find your user and copy the UID</li>
                <li>Paste the UID below and click "Make Admin"</li>
                <li>Sign out and sign back in to see admin dashboard</li>
            </ol>
        </div>

        <div>
            <label for="userUid">User UID from Firebase Authentication:</label>
            <input type="text" id="userUid" placeholder="Enter user UID (e.g., abc123def456...)" />
            
            <button onclick="makeUserAdmin()">🛡️ Make Admin</button>
            <button onclick="checkUserRole()">👀 Check Current Role</button>
        </div>

        <div id="result"></div>
    </div>

    <script type="module">
        // Import Firebase
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, doc, updateDoc, getDoc, setDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Your Firebase config - UPDATE THIS WITH YOUR ACTUAL CONFIG
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make user admin
        window.makeUserAdmin = async function() {
            const userUid = document.getElementById('userUid').value.trim();
            const resultDiv = document.getElementById('result');

            if (!userUid) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter a user UID</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">⏳ Making user admin...</div>';

                // Update user document to make them admin
                const userRef = doc(db, 'users', userUid);
                
                // First check if user exists
                const userDoc = await getDoc(userRef);
                
                if (!userDoc.exists()) {
                    // Create user document if it doesn't exist
                    await setDoc(userRef, {
                        role: 'admin',
                        permissions: ['all'],
                        isAdmin: true,
                        superAdmin: true,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ User document created and admin role assigned!<br>
                            <strong>UID:</strong> ${userUid}<br>
                            <strong>Role:</strong> admin<br>
                            <strong>Permissions:</strong> all<br>
                            <br>
                            <strong>Next steps:</strong><br>
                            1. Sign out of your account<br>
                            2. Sign back in<br>
                            3. Go to /admin to access admin dashboard
                        </div>
                    `;
                } else {
                    // Update existing user
                    await updateDoc(userRef, {
                        role: 'admin',
                        permissions: ['all'],
                        isAdmin: true,
                        superAdmin: true,
                        updatedAt: new Date()
                    });
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ User updated to admin successfully!<br>
                            <strong>UID:</strong> ${userUid}<br>
                            <strong>Role:</strong> admin<br>
                            <strong>Permissions:</strong> all<br>
                            <br>
                            <strong>Next steps:</strong><br>
                            1. Sign out of your account<br>
                            2. Sign back in<br>
                            3. Go to /admin to access admin dashboard
                        </div>
                    `;
                }

            } catch (error) {
                console.error('Error making user admin:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Error making user admin:<br>
                        ${error.message}<br><br>
                        <strong>Common issues:</strong><br>
                        • Make sure Firebase config is correct<br>
                        • Check Firestore security rules<br>
                        • Verify the UID is correct
                    </div>
                `;
            }
        };

        // Check user role
        window.checkUserRole = async function() {
            const userUid = document.getElementById('userUid').value.trim();
            const resultDiv = document.getElementById('result');

            if (!userUid) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter a user UID</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">⏳ Checking user role...</div>';

                const userRef = doc(db, 'users', userUid);
                const userDoc = await getDoc(userRef);

                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    resultDiv.innerHTML = `
                        <div class="info">
                            👤 User found!<br>
                            <strong>UID:</strong> ${userUid}<br>
                            <strong>Role:</strong> ${userData.role || 'Not set'}<br>
                            <strong>Is Admin:</strong> ${userData.isAdmin ? 'Yes' : 'No'}<br>
                            <strong>Permissions:</strong> ${userData.permissions ? userData.permissions.join(', ') : 'None'}<br>
                            <strong>Email:</strong> ${userData.email || 'Not set'}<br>
                            <strong>Name:</strong> ${userData.name || 'Not set'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ User document not found<br>
                            <strong>UID:</strong> ${userUid}<br><br>
                            This user may not have signed up yet, or the UID is incorrect.
                        </div>
                    `;
                }

            } catch (error) {
                console.error('Error checking user role:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Error checking user role:<br>
                        ${error.message}
                    </div>
                `;
            }
        };
    </script>
</body>
</html>
