# Email Configuration
# -------------------
# Set to 'true' to enable email notifications
EMAIL_ENABLED=false

# Email service provider (options: 'sendgrid', 'smtp')
EMAIL_PROVIDER=sendgrid

# SendGrid Configuration (if using SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME="Fetchly"

# SMTP Configuration (if using SMTP)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME="Fetchly"

# Admin email for notifications
ADMIN_EMAIL=<EMAIL>

# Environment
NODE_ENV=development
