"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheck = exports.processEmailNotifications = exports.sendEmailVerification = exports.sendPasswordResetEmail = exports.sendCustomAuthEmails = void 0;
const functions = require("firebase-functions");
// Export email template functions
var email_templates_1 = require("./email-templates");
Object.defineProperty(exports, "sendCustomAuthEmails", { enumerable: true, get: function () { return email_templates_1.sendCustomAuthEmails; } });
Object.defineProperty(exports, "sendPasswordResetEmail", { enumerable: true, get: function () { return email_templates_1.sendPasswordResetEmail; } });
Object.defineProperty(exports, "sendEmailVerification", { enumerable: true, get: function () { return email_templates_1.sendEmailVerification; } });
Object.defineProperty(exports, "processEmailNotifications", { enumerable: true, get: function () { return email_templates_1.processEmailNotifications; } });
// Health check function
exports.healthCheck = functions.https.onRequest((req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        message: 'Fetchly Firebase Functions are running'
    });
});
//# sourceMappingURL=index.js.map