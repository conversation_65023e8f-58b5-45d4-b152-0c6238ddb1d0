# Stripe Connect Environment Variables

Add these environment variables to your `.env.local` file:

## Stripe API Keys
```bash
# Stripe Publishable Key (starts with pk_)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...

# Stripe Secret Key (starts with sk_)
STRIPE_SECRET_KEY=sk_test_...

# Stripe Webhook Secret (starts with whsec_)
STRIPE_WEBHOOK_SECRET=whsec_...
```

## How to Get These Keys

### 1. Stripe API Keys
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Developers** → **API keys**
3. Copy the **Publishable key** and **Secret key**
4. For production, use the live keys (toggle off "Test mode")

### 2. Webhook Secret
1. Go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL to: `https://yourdomain.com/api/webhooks/stripe`
4. Select these events:
   - `account.updated`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `charge.dispute.created`
   - `transfer.created`
   - `payout.paid`
   - `payout.failed`
5. Click **Add endpoint**
6. Copy the **Signing secret** (starts with `whsec_`)

## Stripe Connect Setup

### 1. Enable Connect in Stripe Dashboard
1. Go to **Connect** → **Settings**
2. Enable **Express accounts**
3. Set your platform name: "Fetchly"
4. Add your website URL
5. Set up your branding

### 2. Configure Connect Settings
1. **Business type**: Marketplace
2. **Integration type**: Express accounts
3. **Countries**: United States (add more as needed)
4. **Capabilities**: 
   - Card payments
   - Transfers

### 3. Platform Fee Configuration
The current setup uses an 8% platform fee. You can modify this in:
- `src/app/api/payments/charge/route.ts` (line 15)
- `src/components/stripe/StripeConnectPayment.tsx` (line 82)

## Testing

### Test Cards for Development
```bash
# Successful payment
****************

# Declined payment
****************

# Requires authentication
****************
```

### Test Connect Accounts
Stripe provides test Connect accounts for development. When testing:
1. Use test API keys
2. Create test Express accounts
3. Use test bank account: `************`
4. Use test routing number: `*********`

## Production Checklist

### Before Going Live:
- [ ] Switch to live Stripe API keys
- [ ] Update webhook endpoint to production URL
- [ ] Test with real bank accounts
- [ ] Verify tax settings
- [ ] Set up proper error monitoring
- [ ] Configure payout schedules
- [ ] Test dispute handling

### Security:
- [ ] Never expose secret keys in frontend
- [ ] Use HTTPS for all webhook endpoints
- [ ] Validate webhook signatures
- [ ] Implement proper error handling
- [ ] Log all transactions for audit

## Monitoring

### Important Metrics to Track:
- Platform fee revenue
- Failed payments
- Disputed charges
- Provider onboarding completion rates
- Payout failures

### Stripe Dashboard Sections:
- **Payments**: Track all transactions
- **Connect**: Monitor connected accounts
- **Disputes**: Handle chargebacks
- **Payouts**: Monitor transfers to providers

## Support

### Common Issues:
1. **Provider can't receive payments**: Check if Stripe account is fully onboarded
2. **Payments failing**: Verify API keys and webhook configuration
3. **Platform fees not working**: Check application_fee_amount in payment intent
4. **Webhooks not firing**: Verify endpoint URL and signature validation

### Stripe Resources:
- [Connect Documentation](https://stripe.com/docs/connect)
- [Express Accounts Guide](https://stripe.com/docs/connect/express-accounts)
- [Webhook Testing](https://stripe.com/docs/webhooks/test)
- [Connect Best Practices](https://stripe.com/docs/connect/best-practices)
