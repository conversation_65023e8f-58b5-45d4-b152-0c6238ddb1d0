# Production Environment Variables for Fetchly
# These will be set in Heroku Config Vars

# App Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://fetchlypr.herokuapp.com

# Firebase Configuration (Set these in Heroku Config Vars)
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=

# Firebase Admin SDK (Set these in Heroku Config Vars)
FIREBASE_PROJECT_ID=
FIREBASE_CLIENT_EMAIL=
FIREBASE_PRIVATE_KEY=

# Stripe Configuration (Set these in Heroku Config Vars)
STRIPE_SECRET_KEY=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_WEBHOOK_SECRET=

# PLAID Configuration (Set these in Heroku Config Vars)
PLAID_CLIENT_ID=
PLAID_SECRET=
PLAID_ENV=production

# Email Configuration (Set these in Heroku Config Vars)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=

# JWT Secret (Set this in Heroku Config Vars)
JWT_SECRET=

# Database URLs (if using external databases)
DATABASE_URL=
REDIS_URL=

# API Keys (Set these in Heroku Config Vars)
GOOGLE_MAPS_API_KEY=
SENDGRID_API_KEY=
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=P0l@r!$2025

# Monitoring and Analytics
SENTRY_DSN=
GOOGLE_ANALYTICS_ID=

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_PAYMENTS=true
ENABLE_CHAT=true
ENABLE_STORIES=true
