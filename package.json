{"name": "fetchly-pet-services", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:stable": "next dev", "dev:full": "node server.js", "build": "next build", "build:clean": "rm -rf .next && npm run build", "export": "CAPACITOR_BUILD=true next build", "start": "next start", "start:full": "NODE_ENV=production node server.js", "lint": "next lint", "heroku-postbuild": "npm run build", "firebase:emulators": "firebase emulators:start", "firebase:deploy": "firebase deploy", "firebase:init-db": "node scripts/init-firestore-database.js", "migrate:firebase": "node migrate-to-firebase.js", "cap:build": "npm run export && npx cap sync", "cap:ios": "npm run cap:build && npx cap open ios", "cap:android": "npm run cap:build && npx cap open android", "cap:sync": "npx cap sync", "cap:add:ios": "npx cap add ios", "cap:add:android": "npx cap add android"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@capacitor/geolocation": "^7.1.4", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.4.2", "@capacitor/keyboard": "^7.0.1", "@capacitor/local-notifications": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/share": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.0", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "firebase": "^12.0.0", "framer-motion": "^12.23.11", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.532.0", "multer": "^2.0.2", "next": "15.4.4", "nodemailer": "^7.0.5", "plaid": "^37.0.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-plaid-link": "^4.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "stripe": "^18.4.0", "uuid": "^11.1.0", "zod": "^4.0.10", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "firebase-admin": "^13.4.0", "tailwindcss": "^4", "typescript": "^5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}