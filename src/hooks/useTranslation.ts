import { useLanguage } from '@/contexts/LanguageContext';

/**
 * Enhanced translation hook with additional utilities
 */
export function useTranslation() {
  const { language, setLanguage, t } = useLanguage();

  /**
   * Translate with fallback
   */
  const translate = (key: string, fallback?: string): string => {
    const translated = t(key);
    // If translation key is returned as-is, use fallback
    if (translated === key && fallback) {
      return fallback;
    }
    return translated;
  };

  /**
   * Translate with interpolation
   */
  const translateWithParams = (key: string, params: Record<string, string | number>): string => {
    let translated = t(key);
    
    // Replace placeholders like {{name}} with actual values
    Object.entries(params).forEach(([param, value]) => {
      translated = translated.replace(new RegExp(`{{${param}}}`, 'g'), String(value));
    });
    
    return translated;
  };

  /**
   * Get current language info
   */
  const getCurrentLanguage = () => ({
    code: language,
    name: language === 'en' ? 'English' : 'Español',
    flag: language === 'en' ? '🇺🇸' : '🇪🇸',
    isRTL: false // Neither English nor Spanish are RTL
  });

  /**
   * Toggle between languages
   */
  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'es' : 'en');
  };

  /**
   * Format date according to current language
   */
  const formatDate = (date: Date, options?: Intl.DateTimeFormatOptions): string => {
    const locale = language === 'es' ? 'es-ES' : 'en-US';
    return new Intl.DateTimeFormat(locale, options).format(date);
  };

  /**
   * Format number according to current language
   */
  const formatNumber = (number: number, options?: Intl.NumberFormatOptions): string => {
    const locale = language === 'es' ? 'es-ES' : 'en-US';
    return new Intl.NumberFormat(locale, options).format(number);
  };

  /**
   * Format currency according to current language
   */
  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    const locale = language === 'es' ? 'es-ES' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  /**
   * Get common translations for forms
   */
  const getFormTranslations = () => ({
    save: t('common.save'),
    cancel: t('common.cancel'),
    delete: t('common.delete'),
    edit: t('common.edit'),
    submit: t('common.submit'),
    loading: t('common.loading'),
    search: t('common.search'),
    filter: t('common.filter'),
    close: t('common.close'),
    back: t('common.back'),
    next: t('common.next'),
    previous: t('common.previous'),
    confirm: t('common.confirm'),
    yes: t('common.yes'),
    no: t('common.no')
  });

  /**
   * Get navigation translations
   */
  const getNavTranslations = () => ({
    home: t('nav.home'),
    services: t('nav.services'),
    providers: t('nav.providers'),
    community: t('nav.community'),
    dashboard: t('nav.dashboard'),
    profile: t('nav.profile'),
    wallet: t('nav.wallet'),
    messages: t('nav.messages'),
    settings: t('nav.settings'),
    help: t('nav.help'),
    about: t('nav.about'),
    contact: t('nav.contact'),
    signin: t('nav.signin'),
    signup: t('nav.signup'),
    signout: t('nav.signout')
  });

  /**
   * Get service translations
   */
  const getServiceTranslations = () => ({
    veterinary: t('services.veterinary'),
    grooming: t('services.grooming'),
    training: t('services.training'),
    daycare: t('services.daycare'),
    hotels: t('services.hotels'),
    findServices: t('services.findServices'),
    bookNow: t('services.bookNow')
  });

  /**
   * Check if current language is Spanish
   */
  const isSpanish = () => language === 'es';

  /**
   * Check if current language is English
   */
  const isEnglish = () => language === 'en';

  return {
    // Core translation functions
    t,
    translate,
    translateWithParams,
    
    // Language management
    language,
    setLanguage,
    toggleLanguage,
    getCurrentLanguage,
    isSpanish,
    isEnglish,
    
    // Formatting utilities
    formatDate,
    formatNumber,
    formatCurrency,
    
    // Common translation groups
    getFormTranslations,
    getNavTranslations,
    getServiceTranslations
  };
}

export default useTranslation;
