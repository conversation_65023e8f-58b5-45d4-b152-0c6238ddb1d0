import { useLanguage } from '@/contexts/LanguageContext';

/**
 * Translation hook
 */
export function useTranslation() {
  const { t } = useLanguage();

  /**
   * Translate with fallback
   */
  const translate = (key: string, fallback?: string): string => {
    const translated = t(key);
    // If translation key is returned as-is, use fallback
    if (translated === key && fallback) {
      return fallback;
    }
    return translated;
  };

  /**
   * Translate with interpolation
   */
  const translateWithParams = (key: string, params: Record<string, string | number>): string => {
    let translated = t(key);
    
    // Replace placeholders like {{name}} with actual values
    Object.entries(params).forEach(([param, value]) => {
      translated = translated.replace(new RegExp(`{{${param}}}`, 'g'), String(value));
    });
    
    return translated;
  };

  /**
   * Get current language info (always English)
   */
  const getCurrentLanguage = () => ({
    code: 'en',
    name: 'English',
    flag: '🇺🇸',
    isRTL: false
  });

  // No-op function since we only support English
  const toggleLanguage = () => {};

  /**
   * Format date according to current language (always English)
   */
  const formatDate = (date: Date | string, options: Intl.DateTimeFormatOptions = {}) => {
    if (!date) return '';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Default options
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    
    return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(dateObj);
  };

  /**
   * Format number according to current language (always English)
   */
  const formatNumber = (number: number, options: Intl.NumberFormatOptions = {}) => {
    // Default options
    const defaultOptions: Intl.NumberFormatOptions = {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    };
    
    return new Intl.NumberFormat('en-US', { ...defaultOptions, ...options }).format(number);
  };

  /**
   * Format currency according to current language (always English)
   */
  const formatCurrency = (amount: number, currency = 'USD', options: Intl.NumberFormatOptions = {}) => {
    // Default options
    const defaultOptions: Intl.NumberFormatOptions = {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    };
    
    return new Intl.NumberFormat('en-US', { ...defaultOptions, ...options }).format(amount);
  };

  /**
   * Get plural form based on count
   */
  const pluralize = (count: number, singular: string, plural: string) => {
    return count === 1 ? singular : plural;
  };

  /**
   * Get ordinal suffix for numbers (1st, 2nd, 3rd, etc.)
   */
  const getOrdinalSuffix = (num: number) => {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) {
      return num + 'st';
    }
    if (j === 2 && k !== 12) {
      return num + 'nd';
    }
    if (j === 3 && k !== 13) {
      return num + 'rd';
    }
    return num + 'th';
  };

  /**
   * Get relative time (e.g., "2 days ago")
   */
  const getRelativeTime = (date: Date | string) => {
    if (!date) return '';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    
    // Less than a minute
    if (diffInSeconds < 60) {
      return 'just now';
    }
    
    // Less than an hour
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
    }
    
    // Less than a day
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
    }
    
    // Less than a month
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
    }
    
    // Less than a year
    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`;
    }
    
    // Years
    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`;
  };

  return {
    t,
    translate,
    translateWithParams,
    getCurrentLanguage,
    toggleLanguage,
    formatDate,
    formatNumber,
    formatCurrency,
    pluralize,
    getOrdinalSuffix,
    getRelativeTime,
    language: 'en',
    setLanguage: () => {},
  };
}

export default useTranslation;
