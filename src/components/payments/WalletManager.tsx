'use client';

import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import {
  Wallet,
  Plus,
  CreditCard,
  History,
  ArrowUpRight,
  ArrowDownLeft,
  TrendingUp,
  Shield,
  Zap,
  Star,
  CheckCircle,
  AlertCircle,
  DollarSign
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface WalletManagerProps {
  className?: string;
}

interface TopupFormProps {
  onSuccess: (amount: number) => void;
  onCancel: () => void;
}

const TopupForm: React.FC<TopupFormProps> = ({ onSuccess, onCancel }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { getIdToken } = useAuth();
  
  const [amount, setAmount] = useState(25);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const predefinedAmounts = [10, 25, 50, 100];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!stripe || !elements) return;

    setIsProcessing(true);
    setError(null);

    try {
      const token = await getIdToken();
      
      // Create wallet topup intent
      const response = await fetch('/api/wallet/topup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ amount }),
      });

      const { clientSecret, error: apiError } = await response.json();

      if (apiError) {
        setError(apiError);
        return;
      }

      // Confirm payment
      const cardElement = elements.getElement(CardElement);
      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(
        clientSecret,
        {
          payment_method: {
            card: cardElement!,
          },
        }
      );

      if (stripeError) {
        setError(stripeError.message || 'Payment failed');
      } else if (paymentIntent.status === 'succeeded') {
        onSuccess(amount);
      }
    } catch (err: any) {
      setError(err.message || 'Payment failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
    },
  };

  return (
    <div className="p-4 sm:p-6">
      <h3 className="text-xl font-bold text-gray-800 mb-6">Add Funds to Wallet</h3>
      
      <form onSubmit={handleSubmit}>
        {/* Amount Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Select Amount
          </label>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 mb-4">
            {predefinedAmounts.map((presetAmount) => (
              <button
                key={presetAmount}
                type="button"
                onClick={() => setAmount(presetAmount)}
                className={`p-2 sm:p-3 rounded-xl border-2 transition-all text-sm sm:text-base ${
                  amount === presetAmount
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-green-300'
                }`}
              >
                ${presetAmount}
              </button>
            ))}
          </div>
          
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
            <input
              type="number"
              min="5"
              max="500"
              value={amount}
              onChange={(e) => setAmount(Number(e.target.value))}
              className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="Custom amount"
            />
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Minimum: $5, Maximum: $500
          </p>
        </div>

        {/* Card Details */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Card Details
          </label>
          <div className="p-4 border border-gray-300 rounded-xl bg-white">
            <CardElement options={cardElementOptions} />
          </div>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <div className="flex gap-3">
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 py-3 px-4 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isProcessing || !stripe || amount < 5 || amount > 500}
            className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? 'Processing...' : `Add $${amount}`}
          </button>
        </div>
      </form>
    </div>
  );
};

const WalletManager: React.FC<WalletManagerProps> = ({ className = '' }) => {
  const { user } = useAuth();
  // All text is in English
  const [showTopup, setShowTopup] = useState(false);
  const [walletBalance, setWalletBalance] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalSpent: 0,
    totalAdded: 0,
    transactionCount: 0,
    averageTransaction: 0,
  });

  useEffect(() => {
    if (user) {
      setWalletBalance(user.fetchlyBalance || 0);
      // TODO: Fetch transaction history
    }
  }, [user]);

  const handleTopupSuccess = (amount: number) => {
    setWalletBalance(prev => prev + amount);
    setShowTopup(false);
    // TODO: Refresh transaction history
  };

  if (showTopup) {
    return (
      <div className={`bg-white rounded-2xl shadow-lg ${className}`}>
        <Elements stripe={stripePromise}>
          <TopupForm
            onSuccess={handleTopupSuccess}
            onCancel={() => setShowTopup(false)}
          />
        </Elements>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-2xl shadow-lg p-4 sm:p-6 ${className}`}>
      {/* Wallet Header - Mobile Optimized */}
      <div className="flex items-center justify-between mb-4 sm:mb-6">
        <div className="flex items-center gap-2 sm:gap-3">
          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
            <Wallet className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
          <div className="min-w-0">
            <h2 className="text-lg sm:text-xl font-bold text-gray-800 truncate">My Wallet</h2>
            <p className="text-sm sm:text-base text-gray-600 truncate">Manage your funds and transactions</p>
          </div>
        </div>
      </div>

      {/* Balance Display - Mobile Optimized */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4 sm:p-6 mb-4 sm:mb-6 border border-green-200">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Wallet Balance</h3>
          <p className="text-2xl sm:text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
            ${walletBalance.toFixed(2)}
          </p>
          <div className="flex items-center justify-center gap-2 mt-2">
            <Shield className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
            <span className="text-xs sm:text-sm text-gray-600">Secured by Fetchly</span>
          </div>
        </div>
      </div>

      {/* Wallet Stats - Mobile Optimized */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6">
        <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-gray-600 truncate">Total Added</p>
              <p className="text-sm sm:text-lg font-bold text-gray-800 truncate">
                ${stats.totalAdded.toFixed(2)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <Star className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm text-gray-600 truncate">Transactions</p>
              <p className="text-sm sm:text-lg font-bold text-gray-800">{stats.transactionCount}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons - Mobile Optimized */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-6">
        <button
          onClick={() => setShowTopup(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <Plus className="-ml-1 mr-2 h-5 w-5" />
          Add Funds
        </button>

        <button className="flex items-center justify-center gap-2 p-3 sm:p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all">
          <History className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
          <span className="font-medium text-gray-700 text-sm sm:text-base">Transaction History</span>
        </button>

        <button className="flex items-center justify-center gap-2 p-3 sm:p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all">
          <Zap className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
          <span className="font-medium text-gray-700 text-sm sm:text-base">Quick Pay</span>
        </button>
      </div>

      {/* Quick Add Amounts - Mobile Optimized */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Add</h3>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3">
          {[10, 25, 50, 100].map((amount) => (
            <button
              key={amount}
              onClick={() => {
                setShowTopup(true);
                // You can pass the amount to the TopupForm
              }}
              className="p-2 sm:p-3 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg hover:from-green-100 hover:to-blue-100 transition-all text-center"
            >
              <span className="text-xs sm:text-sm font-semibold text-green-700">
                ${amount}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Recent Transactions */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
        
        {transactions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Wallet className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>No transactions yet</p>
            <p className="text-sm">Add funds to get started</p>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Sample transaction items - replace with real data */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <ArrowUpRight className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-800">Wallet Top-up</p>
                  <p className="text-sm text-gray-500">Today, 2:30 PM</p>
                </div>
              </div>
              <p className="font-semibold text-green-600">+$25.00</p>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <ArrowDownLeft className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-800">Dog Walking Service</p>
                  <p className="text-sm text-gray-500">Yesterday, 10:15 AM</p>
                </div>
              </div>
              <p className="font-semibold text-gray-600">-$30.00</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WalletManager;
