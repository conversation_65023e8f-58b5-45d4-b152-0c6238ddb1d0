'use client';

import React, { useState, useEffect } from 'react';
import {
  CheckCircle,
  AlertCircle,
  ExternalLink,
  CreditCard,
  DollarSign,
  Shield,
  Zap,
  TrendingUp,
  Star,
  Award,
  Clock,
  Globe,
  ArrowRight,
  Building,
  User
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api-client';
import toast from 'react-hot-toast';

interface OnboardingStatus {
  isOnboarded: boolean;
  accountId?: string;
  status?: {
    detailsSubmitted: boolean;
    chargesEnabled: boolean;
    payoutsEnabled: boolean;
  };
}

const ProviderOnboarding: React.FC = () => {
  const { getIdToken } = useAuth();
  const [onboardingStatus, setOnboardingStatus] = useState<OnboardingStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkOnboardingStatus();
  }, []);

  const checkOnboardingStatus = async () => {
    try {
      const result = await apiClient.get('/api/providers/onboard');

      if (result.success) {
        setOnboardingStatus(result);
      } else {
        setError(result.error);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to check onboarding status');
    } finally {
      setIsLoading(false);
    }
  };

  const startOnboarding = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await apiClient.post('/api/providers/onboard');

      if (result.success) {
        if (result.onboardingUrl) {
          // Redirect to Stripe onboarding
          window.location.href = result.onboardingUrl;
        } else {
          // Already onboarded
          setOnboardingStatus(result);
        }
      } else {
        setError(result.error);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to start onboarding');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="ml-3 text-gray-600">Checking onboarding status...</span>
        </div>
      </div>
    );
  }

  if (onboardingStatus?.isOnboarded) {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Payment Setup Complete!</h2>
          <p className="text-gray-600 mb-6">
            Your Stripe account is fully configured and ready to receive payments.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 bg-green-50 rounded-xl border border-green-200">
              <CheckCircle className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-green-800">Details Submitted</p>
            </div>
            <div className="p-4 bg-green-50 rounded-xl border border-green-200">
              <CreditCard className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-green-800">Charges Enabled</p>
            </div>
            <div className="p-4 bg-green-50 rounded-xl border border-green-200">
              <DollarSign className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-green-800">Payouts Enabled</p>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200">
            <h3 className="font-semibold text-gray-800 mb-4">🎉 You're Ready to Earn!</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-gray-700">Instant payment processing</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-gray-700">
                    10% platform fee (you keep 90%)
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-purple-600" />
                  <span className="text-sm text-gray-700">Weekly automatic payouts</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-gray-700">Professional invoicing</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-gray-700">Secure payment protection</span>
                </div>
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-gray-700">Real-time earnings tracking</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg p-8">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <Shield className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Set Up Payments</h2>
        <p className="text-gray-600">
          Complete your payment setup to start receiving payments from pet owners
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        </div>
      )}

      <div className="space-y-6 mb-8">
        <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <span className="text-green-600 font-semibold text-sm">1</span>
          </div>
          <div>
            <h3 className="font-semibold text-gray-800 mb-1">Secure Account Setup</h3>
            <p className="text-gray-600 text-sm">
              We'll redirect you to Stripe to securely set up your payment account. 
              This ensures your financial information is protected with bank-level security.
            </p>
          </div>
        </div>

        <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <span className="text-blue-600 font-semibold text-sm">2</span>
          </div>
          <div>
            <h3 className="font-semibold text-gray-800 mb-1">Provide Business Information</h3>
            <p className="text-gray-600 text-sm">
              Enter your business details, tax information, and bank account for payouts. 
              This is required by law for payment processing.
            </p>
          </div>
        </div>

        <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl">
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <span className="text-purple-600 font-semibold text-sm">3</span>
          </div>
          <div>
            <h3 className="font-semibold text-gray-800 mb-1">Start Earning</h3>
            <p className="text-gray-600 text-sm">
              Once approved, you can immediately start accepting payments and receiving 
              automatic payouts to your bank account.
            </p>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200 mb-6">
        <h3 className="font-semibold text-gray-800 mb-3">Payment Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-gray-700 text-sm">Accept credit/debit cards</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-gray-700 text-sm">Fetchly Wallet payments</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-gray-700 text-sm">Automatic payouts</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-gray-700 text-sm">Invoice generation</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-gray-700 text-sm">Earnings tracking</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-gray-700 text-sm">Tax reporting (1099)</span>
          </div>
        </div>
      </div>

      <button
        onClick={startOnboarding}
        disabled={isLoading}
        className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
      >
        {isLoading ? (
          <>
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Setting up...
          </>
        ) : (
          <>
            <ExternalLink className="w-5 h-5" />
            Complete Payment Setup
          </>
        )}
      </button>

      <p className="text-center text-xs text-gray-500 mt-4">
        Powered by Stripe • Your information is secure and encrypted
      </p>
    </div>
  );
};

export default ProviderOnboarding;
