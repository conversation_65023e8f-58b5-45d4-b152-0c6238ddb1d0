'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight, Play, Pause, Heart, MessageCircle, Share } from 'lucide-react';
import { StoryViewerProps, STORY_CONFIG, getStoryDuration } from '@/types/stories';
import StoryMedia from './StoryMedia';

export default function StoryViewer({
  userStories,
  initialUserIndex,
  initialStoryIndex,
  onClose,
  onStoryView
}: StoryViewerProps) {
  const [currentUserIndex, setCurrentUserIndex] = useState(initialUserIndex);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(initialStoryIndex);
  const [progress, setProgress] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [isHolding, setIsHolding] = useState(false);
  
  const currentUser = userStories[currentUserIndex];
  const currentStory = currentUser?.stories[currentStoryIndex];
  const storyDuration = currentStory ? getStoryDuration(currentStory) : STORY_CONFIG.DEFAULT_DURATION;

  // Timer and refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  }, []);

  // Start story timer
  const startTimer = useCallback(() => {
    if (!currentStory || isPaused) return;
    
    clearTimers();
    startTimeRef.current = Date.now() - pausedTimeRef.current;
    
    // Progress update interval
    progressIntervalRef.current = setInterval(() => {
      if (isPaused) return;
      
      const elapsed = Date.now() - startTimeRef.current;
      const newProgress = Math.min((elapsed / storyDuration) * 100, 100);
      setProgress(newProgress);
      
      if (newProgress >= 100) {
        nextStory();
      }
    }, STORY_CONFIG.PROGRESS_UPDATE_INTERVAL);

    // Set timeout for the current story
    timerRef.current = setTimeout(() => {
      nextStory();
    }, storyDuration - (Date.now() - startTimeRef.current));

  }, [currentStory, isPaused, storyDuration]);

  // Pause timer
  const pauseTimer = useCallback(() => {
    setIsPaused(true);
    pausedTimeRef.current = Date.now() - startTimeRef.current;
    clearTimers();
  }, [clearTimers]);

  // Resume timer
  const resumeTimer = useCallback(() => {
    setIsPaused(false);
    startTimer();
  }, [startTimer]);

  // Toggle play/pause
  const togglePlayPause = useCallback(() => {
    if (isPaused) {
      resumeTimer();
    } else {
      pauseTimer();
    }
  }, [isPaused, pauseTimer, resumeTimer]);

  // Go to next story
  const nextStory = useCallback(() => {
    if (!currentUser) return;
    
    const nextStoryIndex = currentStoryIndex + 1;
    
    if (nextStoryIndex < currentUser.stories.length) {
      // Next story in current user
      setCurrentStoryIndex(nextStoryIndex);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else {
      // Next user
      const nextUserIndex = currentUserIndex + 1;
      if (nextUserIndex < userStories.length) {
        setCurrentUserIndex(nextUserIndex);
        setCurrentStoryIndex(0);
        setProgress(0);
        pausedTimeRef.current = 0;
      } else {
        // End of all stories
        onClose();
      }
    }
  }, [currentUser, currentStoryIndex, currentUserIndex, userStories.length, onClose]);

  // Go to previous story
  const previousStory = useCallback(() => {
    if (currentStoryIndex > 0) {
      // Previous story in current user
      setCurrentStoryIndex(prev => prev - 1);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else if (currentUserIndex > 0) {
      // Previous user's last story
      const prevUserIndex = currentUserIndex - 1;
      const prevUserStories = userStories[prevUserIndex]?.stories || [];
      setCurrentUserIndex(prevUserIndex);
      setCurrentStoryIndex(prevUserStories.length - 1);
      setProgress(0);
      pausedTimeRef.current = 0;
    }
  }, [currentStoryIndex, currentUserIndex, userStories]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          previousStory();
          break;
        case 'ArrowRight':
          nextStory();
          break;
        case 'Escape':
          onClose();
          break;
        case ' ':
        case 'Spacebar':
          togglePlayPause();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [nextStory, onClose, previousStory, togglePlayPause]);

  // Handle touch events for story navigation
  const handleTouchStart = (e: React.TouchEvent | React.MouseEvent) => {
    pauseTimer();
    if ('touches' in e) {
      setTouchStartX(e.touches[0].clientX);
    } else {
      setTouchStartX(e.clientX);
    }
    setIsHolding(true);
  };

  const handleTouchMove = (e: React.TouchEvent | React.MouseEvent) => {
    if ('touches' in e) {
      setTouchEndX(e.touches[0].clientX);
    } else if (isHolding) {
      setTouchEndX(e.clientX);
    }
  };

  const handleTouchEnd = () => {
    if (!isHolding) return;
    
    const diff = touchStartX - touchEndX;
    const threshold = 50;
    
    if (diff > threshold) {
      // Swipe left - next story
      nextStory();
    } else if (diff < -threshold) {
      // Swipe right - previous story
      previousStory();
    } else {
      // Tap - toggle play/pause
      togglePlayPause();
    }
    
    setIsHolding(false);
    if (!isPaused) {
      resumeTimer();
    }
  };

  // Start timer when story changes
  useEffect(() => {
    setProgress(0);
    pausedTimeRef.current = 0;
    setIsPaused(false);
    
    if (currentStory) {
      // Mark story as viewed
      onStoryView?.(currentStory.id, currentStory.userId);
      startTimer();
    }

    return () => clearTimers();
  }, [currentStory, startTimer, clearTimers, onStoryView]);

  // Cleanup on unmount
  useEffect(() => {
    return () => clearTimers();
  }, [clearTimers]);

  if (!currentUser || !currentStory) {
    return null;
  }
  const [currentUserIndex, setCurrentUserIndex] = useState(initialUserIndex);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(initialStoryIndex);
  const [progress, setProgress] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  
  const currentUser = userStories[currentUserIndex];
  const currentStory = currentUser?.stories[currentStoryIndex];
  const storyDuration = currentStory ? getStoryDuration(currentStory) : STORY_CONFIG.DEFAULT_DURATION;

  // Timer and refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  }, []);

  // Start story timer
  const startTimer = useCallback(() => {
    if (!currentStory || isPaused) return;
    
    clearTimers();
    startTimeRef.current = Date.now() - pausedTimeRef.current;
    
    // Progress update interval
    progressIntervalRef.current = setInterval(() => {
      if (isPaused) return;
      
      const elapsed = Date.now() - startTimeRef.current;
      const newProgress = Math.min((elapsed / storyDuration) * 100, 100);
      setProgress(newProgress);
      
      if (newProgress >= 100) {
        nextStory();
      }
    }, STORY_CONFIG.PROGRESS_UPDATE_INTERVAL);

    // Set timeout for the current story
    timerRef.current = setTimeout(() => {
      nextStory();
    }, storyDuration - (Date.now() - startTimeRef.current));

  }, [currentStory, isPaused, storyDuration]);

  // Pause timer
  const pauseTimer = useCallback(() => {
    setIsPaused(true);
    pausedTimeRef.current = Date.now() - startTimeRef.current;
    clearTimers();
  }, [clearTimers]);

  // Resume timer
  const resumeTimer = useCallback(() => {
    setIsPaused(false);
    startTimer();
  }, [startTimer]);

  // Toggle play/pause
  const togglePlayPause = useCallback(() => {
    if (isPaused) {
      resumeTimer();
    } else {
      pauseTimer();
    }
  }, [isPaused, pauseTimer, resumeTimer]);

  // Go to next story
  const nextStory = useCallback(() => {
    if (!currentUser) return;
    
    const nextStoryIndex = currentStoryIndex + 1;
    
    if (nextStoryIndex < currentUser.stories.length) {
      // Next story in current user
      setCurrentStoryIndex(nextStoryIndex);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else {
      // Next user
      const nextUserIndex = currentUserIndex + 1;
      if (nextUserIndex < userStories.length) {
        setCurrentUserIndex(nextUserIndex);
        setCurrentStoryIndex(0);
        setProgress(0);
        pausedTimeRef.current = 0;
      } else {
        // End of all stories
        onClose();
      }
    }
  }, [currentUser, currentStoryIndex, currentUserIndex, userStories.length, onClose]);

  // Go to previous story
  const previousStory = useCallback(() => {
    if (currentStoryIndex > 0) {
      // Previous story in current user
      setCurrentStoryIndex(prev => prev - 1);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else if (currentUserIndex > 0) {
      // Previous user's last story
      const prevUserIndex = currentUserIndex - 1;
      const prevUserStories = userStories[prevUserIndex]?.stories || [];
      setCurrentUserIndex(prevUserIndex);
      setCurrentStoryIndex(prevUserStories.length - 1);
      setProgress(0);
      pausedTimeRef.current = 0;
    }
  }, [currentStoryIndex, currentUserIndex, userStories]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          previousStory();
          break;
        case 'ArrowRight':
          nextStory();
          break;
        case 'Escape':
          onClose();
          break;
        case ' ':
        case 'Spacebar':
          togglePlayPause();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [nextStory, onClose, previousStory, togglePlayPause]);

  // Handle touch events for story navigation
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [isHolding, setIsHolding] = useState(false);

  const handleTouchStart = (e: React.TouchEvent | React.MouseEvent) => {
    pauseTimer();
    if ('touches' in e) {
      setTouchStartX(e.touches[0].clientX);
    } else {
      setTouchStartX(e.clientX);
    }
    setIsHolding(true);
  };

  const handleTouchMove = (e: React.TouchEvent | React.MouseEvent) => {
    if ('touches' in e) {
      setTouchEndX(e.touches[0].clientX);
    } else if (isHolding) {
      setTouchEndX(e.clientX);
    }
  };

  const handleTouchEnd = () => {
    if (!isHolding) return;
    
    const diff = touchStartX - touchEndX;
    const threshold = 50;
    
    if (diff > threshold) {
      // Swipe left - next story
      nextStory();
    } else if (diff < -threshold) {
      // Swipe right - previous story
      previousStory();
    } else {
      // Tap - toggle play/pause
      togglePlayPause();
    }
    
    setIsHolding(false);
    if (!isPaused) {
      resumeTimer();
    }
  };

  // Start timer when story changes
  useEffect(() => {
    setProgress(0);
    pausedTimeRef.current = 0;
    setIsPaused(false);
    
    if (currentStory) {
      // Mark story as viewed
      onStoryView?.(currentStory.id, currentStory.userId);
      startTimer();
    }

    return () => clearTimers();
  }, [currentStory, startTimer, clearTimers, onStoryView]);

  // Cleanup on unmount
  useEffect(() => {
    return () => clearTimers();
  }, [clearTimers]);

  if (!currentUser || !currentStory) {
    return null;
  }
  const [currentUserIndex, setCurrentUserIndex] = useState(initialUserIndex);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(initialStoryIndex);
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);
  const holdTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const currentUser = userStories[currentUserIndex];
  const currentStory = currentUser?.stories[currentStoryIndex];
  const storyDuration = currentStory ? getStoryDuration(currentStory) : STORY_CONFIG.DEFAULT_DURATION;

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    if (holdTimeoutRef.current) {
      clearTimeout(holdTimeoutRef.current);
      holdTimeoutRef.current = null;
    }
  }, []);

  // Start story timer
  const startTimer = useCallback(() => {
    if (!isPlaying || isPaused) return;

    clearTimers();
    startTimeRef.current = Date.now() - pausedTimeRef.current;
    
    // Progress update interval
    progressIntervalRef.current = setInterval(() => {
      if (!isPlaying || isPaused) return;
      
      const elapsed = Date.now() - startTimeRef.current;
      const newProgress = Math.min((elapsed / storyDuration) * 100, 100);
      setProgress(newProgress);
      
      if (newProgress >= 100) {
        nextStory();
      }
    }, STORY_CONFIG.PROGRESS_UPDATE_INTERVAL);

  }, [isPlaying, isPaused, storyDuration]);

  // Pause timer
  const pauseTimer = useCallback(() => {
    setIsPaused(true);
    pausedTimeRef.current = Date.now() - startTimeRef.current;
    clearTimers();
  }, [clearTimers]);

  // Resume timer
  const resumeTimer = useCallback(() => {
    setIsPaused(false);
    startTimer();
  }, [startTimer]);

  // Next story
  const nextStory = useCallback(() => {
    const nextStoryIdx = currentStoryIndex + 1;
    
    if (nextStoryIdx < currentUser.stories.length) {
      // Next story in current user
      setCurrentStoryIndex(nextStoryIdx);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else {
      // Next user
      const nextUserIdx = currentUserIndex + 1;
      if (nextUserIdx < userStories.length) {
        setCurrentUserIndex(nextUserIdx);
        setCurrentStoryIndex(0);
        setProgress(0);
        pausedTimeRef.current = 0;
      } else {
        // End of all stories
        onClose();
      }
    }
  }, [currentUserIndex, currentStoryIndex, currentUser, userStories, onClose]);

  // Previous story
  const previousStory = useCallback(() => {
    const prevStoryIdx = currentStoryIndex - 1;
    
    if (prevStoryIdx >= 0) {
      // Previous story in current user
      setCurrentStoryIndex(prevStoryIdx);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else {
      // Previous user
      const prevUserIdx = currentUserIndex - 1;
      if (prevUserIdx >= 0) {
        const prevUser = userStories[prevUserIdx];
        setCurrentUserIndex(prevUserIdx);
        setCurrentStoryIndex(prevUser.stories.length - 1);
        setProgress(0);
        pausedTimeRef.current = 0;
      }
    }
  }, [currentUserIndex, currentStoryIndex, userStories]);

  // Handle touch/mouse events
  const handlePointerDown = useCallback((e: React.PointerEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const isLeftSide = x < rect.width / 2;

    // Start hold timer for pause
    holdTimeoutRef.current = setTimeout(() => {
      pauseTimer();
    }, STORY_CONFIG.HOLD_THRESHOLD);

    // Handle tap zones
    const handleTap = () => {
      if (holdTimeoutRef.current) {
        clearTimeout(holdTimeoutRef.current);
        holdTimeoutRef.current = null;
      }
      
      if (!isPaused) {
        if (isLeftSide) {
          previousStory();
        } else {
          nextStory();
        }
      }
    };

    // Set up pointer up handler
    const handlePointerUp = () => {
      if (holdTimeoutRef.current) {
        clearTimeout(holdTimeoutRef.current);
        holdTimeoutRef.current = null;
        handleTap();
      } else if (isPaused) {
        resumeTimer();
      }
      
      document.removeEventListener('pointerup', handlePointerUp);
      document.removeEventListener('pointercancel', handlePointerUp);
    };

    document.addEventListener('pointerup', handlePointerUp);
    document.addEventListener('pointercancel', handlePointerUp);
  }, [isPaused, pauseTimer, resumeTimer, previousStory, nextStory]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          previousStory();
          break;
        case 'ArrowRight':
        case ' ':
          e.preventDefault();
          nextStory();
          break;
        case 'Escape':
          onClose();
          break;
        case 'p':
        case 'P':
          if (isPaused) {
            resumeTimer();
          } else {
            pauseTimer();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [previousStory, nextStory, onClose, isPaused, pauseTimer, resumeTimer]);

  // Start timer when story changes
  useEffect(() => {
    setProgress(0);
    pausedTimeRef.current = 0;
    setIsPaused(false);
    
    if (currentStory) {
    }
  };

  document.addEventListener('keydown', handleKeyDown);
  return () => document.removeEventListener('keydown', handleKeyDown);
}, [previousStory, nextStory, onClose, isPaused, pauseTimer, resumeTimer]);

// Start timer when story changes
useEffect(() => {
  setProgress(0);
  pausedTimeRef.current = 0;
  setIsPaused(false);

  if (currentStory) {
    // Mark story as viewed
    onStoryView?.(currentStory.id, currentStory.userId);
    startTimer();
  }

  return () => clearTimers();
}, [currentStory, startTimer, clearTimers, onStoryView]);

// Cleanup on unmount
useEffect(() => {
  return () => clearTimers();
}, [clearTimers]);

if (!currentUser || !currentStory) {
  return null;
            onClick={nextStory}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 text-white p-2 hover:bg-white/20 rounded-full transition-colors"
          >
            <ChevronRight className="w-8 h-8" />
          </button>
        </div>

        {/* Pause indicator */}
        {isPaused && (
          <div className="absolute inset-0 flex items-center justify-center z-30">
            <div className="bg-black/50 backdrop-blur-sm rounded-full p-4">
              <div className="text-white text-4xl">⏸️</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
