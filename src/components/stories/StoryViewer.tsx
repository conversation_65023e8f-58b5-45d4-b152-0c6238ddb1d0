'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight, Play, Pause, Heart, MessageCircle, Share } from 'lucide-react';
import { StoryViewerProps, STORY_CONFIG, getStoryDuration } from '@/types/stories';
import StoryMedia from './StoryMedia';

export default function StoryViewer({
  userStories,
  initialUserIndex,
  initialStoryIndex,
  onClose,
  onStoryView
}: StoryViewerProps) {
  // State management
  const [currentUserIndex, setCurrentUserIndex] = useState(initialUserIndex);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(initialStoryIndex);
  const [progress, setProgress] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [isHolding, setIsHolding] = useState(false);
  
  // Refs for timers and state management
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Current story data
  const currentUser = userStories[currentUserIndex];
  const currentStory = currentUser?.stories[currentStoryIndex];
  const storyDuration = currentStory ? getStoryDuration(currentStory) : STORY_CONFIG.DEFAULT_DURATION;

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  }, []);

  // Start story timer
  const startTimer = useCallback(() => {
    if (!currentStory || isPaused) return;
    
    clearTimers();
    startTimeRef.current = Date.now() - pausedTimeRef.current;
    
    // Progress update interval
    progressIntervalRef.current = setInterval(() => {
      if (isPaused) return;
      
      const elapsed = Date.now() - startTimeRef.current;
      const newProgress = Math.min((elapsed / storyDuration) * 100, 100);
      setProgress(newProgress);
      
      if (newProgress >= 100) {
        nextStory();
      }
    }, STORY_CONFIG.PROGRESS_UPDATE_INTERVAL);

    // Set timeout for the current story
    timerRef.current = setTimeout(() => {
      nextStory();
    }, storyDuration - (Date.now() - startTimeRef.current));
  }, [currentStory, isPaused, storyDuration]);

  // Pause timer
  const pauseTimer = useCallback(() => {
    setIsPaused(true);
    pausedTimeRef.current = Date.now() - startTimeRef.current;
    clearTimers();
  }, [clearTimers]);

  // Resume timer
  const resumeTimer = useCallback(() => {
    setIsPaused(false);
    startTimer();
  }, [startTimer]);

  // Toggle play/pause
  const togglePlayPause = useCallback(() => {
    if (isPaused) {
      resumeTimer();
    } else {
      pauseTimer();
    }
  }, [isPaused, pauseTimer, resumeTimer]);

  // Go to next story
  const nextStory = useCallback(() => {
    if (!currentUser) return;
    
    const nextStoryIndex = currentStoryIndex + 1;
    
    if (nextStoryIndex < currentUser.stories.length) {
      // Next story in current user
      setCurrentStoryIndex(nextStoryIndex);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else {
      // Next user
      const nextUserIndex = currentUserIndex + 1;
      if (nextUserIndex < userStories.length) {
        setCurrentUserIndex(nextUserIndex);
        setCurrentStoryIndex(0);
        setProgress(0);
        pausedTimeRef.current = 0;
      } else {
        // End of all stories
        onClose();
      }
    }
  }, [currentUser, currentStoryIndex, currentUserIndex, userStories.length, onClose]);

  // Go to previous story
  const previousStory = useCallback(() => {
    if (currentStoryIndex > 0) {
      // Previous story in current user
      setCurrentStoryIndex(prev => prev - 1);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else if (currentUserIndex > 0) {
      // Previous user's last story
      const prevUserIndex = currentUserIndex - 1;
      const prevUserStories = userStories[prevUserIndex]?.stories || [];
      setCurrentUserIndex(prevUserIndex);
      setCurrentStoryIndex(prevUserStories.length - 1);
      setProgress(0);
      pausedTimeRef.current = 0;
    }
  }, [currentStoryIndex, currentUserIndex, userStories]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          previousStory();
          break;
        case 'ArrowRight':
          nextStory();
          break;
        case 'Escape':
          onClose();
          break;
        case ' ':
        case 'Spacebar':
          togglePlayPause();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [nextStory, onClose, previousStory, togglePlayPause]);

  // Handle touch events for story navigation
  const handleTouchStart = (e: React.TouchEvent | React.MouseEvent) => {
    pauseTimer();
    if ('touches' in e) {
      setTouchStartX(e.touches[0].clientX);
    } else {
      setTouchStartX(e.clientX);
    }
    setIsHolding(true);
  };

  const handleTouchMove = (e: React.TouchEvent | React.MouseEvent) => {
    if ('touches' in e) {
      setTouchEndX(e.touches[0].clientX);
    } else if (isHolding) {
      setTouchEndX(e.clientX);
    }
  };

  const handleTouchEnd = () => {
    if (!isHolding) return;
    
    const diff = touchStartX - touchEndX;
    const threshold = 50;
    
    if (diff > threshold) {
      // Swipe left - next story
      nextStory();
    } else if (diff < -threshold) {
      // Swipe right - previous story
      previousStory();
    } else {
      // Tap - toggle play/pause
      togglePlayPause();
    }
    
    setIsHolding(false);
    if (!isPaused) {
      resumeTimer();
    }
  };

  // Start timer when story changes
  useEffect(() => {
    setProgress(0);
    pausedTimeRef.current = 0;
    setIsPaused(false);
    
    if (currentStory) {
      // Mark story as viewed
      onStoryView?.(currentStory.id, currentStory.userId);
      startTimer();
    }

    return () => clearTimers();
  }, [currentStory, startTimer, clearTimers, onStoryView]);

  // Cleanup on unmount
  useEffect(() => {
    return () => clearTimers();
  }, [clearTimers]);

  if (!currentUser || !currentStory) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center p-4">
      <div
        className="bg-black rounded-3xl shadow-2xl w-full max-w-md h-[600px] flex flex-col text-white relative overflow-hidden"
        onMouseDown={handleTouchStart}
        onMouseMove={handleTouchMove}
        onMouseUp={handleTouchEnd}
        onMouseLeave={handleTouchEnd}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
      {/* Progress bars */}
      <div className="flex w-full p-2 space-x-1">
        {currentUser.stories.map((_, index) => (
          <div key={index} className="h-1 flex-1 bg-gray-600 rounded-full overflow-hidden">
            <div 
              className={`h-full ${index < currentStoryIndex ? 'bg-white' : index === currentStoryIndex ? 'bg-white' : 'bg-gray-600'}`}
              style={{
                width: index === currentStoryIndex ? `${progress}%` : '0%',
                transition: index === currentStoryIndex && progress > 0 ? 'width 0.1s linear' : 'none'
              }}
            />
          </div>
        ))}
      </div>

      {/* Header */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center">
            {currentUser.userAvatar ? (
              <img
                src={currentUser.userAvatar}
                alt={currentUser.userName || 'User'}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="text-lg font-semibold">
                {currentUser.userName ? currentUser.userName.charAt(0).toUpperCase() : '?'}
              </span>
            )}
          </div>
          <div>
            <div className="font-semibold">{currentUser.userName || 'Unknown User'}</div>
            <div className="text-xs text-gray-300">
              {new Date(currentStory.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
          </div>
        </div>
        <button 
          onClick={onClose}
          className="p-2 rounded-full hover:bg-gray-800"
          aria-label="Close story viewer"
        >
          <X size={24} />
        </button>
      </div>

      {/* Story content */}
      <div className="flex-1 relative">
        <StoryMedia 
          story={currentStory} 
          isPaused={isPaused}
          onMediaEnd={nextStory}
        />
        
        {/* Navigation controls */}
        <div className="absolute inset-0 flex">
          <button 
            className="w-1/2 h-full"
            onClick={previousStory}
            aria-label="Previous story"
          />
          <button 
            className="w-1/2 h-full"
            onClick={nextStory}
            aria-label="Next story"
          />
        </div>
      </div>

      {/* Bottom controls */}
      <div className="p-4 flex items-center justify-between">
        <div className="flex space-x-4">
          <button 
            onClick={togglePlayPause}
            className="p-2 rounded-full hover:bg-gray-800"
            aria-label={isPaused ? 'Play' : 'Pause'}
          >
            {isPaused ? <Play size={24} /> : <Pause size={24} />}
          </button>
          <button 
            className="p-2 rounded-full hover:bg-gray-800"
            aria-label="Like story"
          >
            <Heart size={24} />
          </button>
          <button 
            className="p-2 rounded-full hover:bg-gray-800"
            aria-label="Reply to story"
          >
            <MessageCircle size={24} />
          </button>
        </div>
        <button 
          className="p-2 rounded-full hover:bg-gray-800"
          aria-label="Share story"
        >
          <Share size={24} />
        </button>
      </div>
    </div>
    </div>
  );
}
