'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import { StoryViewerProps, STORY_CONFIG, getStoryDuration } from '@/types/stories';
import StoryProgress from './StoryProgress';
import StoryMedia from './StoryMedia';

export default function StoryViewer({
  userStories,
  initialUserIndex,
  initialStoryIndex,
  onClose,
  onStoryView
}: StoryViewerProps) {
  const [currentUserIndex, setCurrentUserIndex] = useState(initialUserIndex);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(initialStoryIndex);
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);
  const holdTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const currentUser = userStories[currentUserIndex];
  const currentStory = currentUser?.stories[currentStoryIndex];
  const storyDuration = currentStory ? getStoryDuration(currentStory) : STORY_CONFIG.DEFAULT_DURATION;

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    if (holdTimeoutRef.current) {
      clearTimeout(holdTimeoutRef.current);
      holdTimeoutRef.current = null;
    }
  }, []);

  // Start story timer
  const startTimer = useCallback(() => {
    if (!isPlaying || isPaused) return;

    clearTimers();
    startTimeRef.current = Date.now() - pausedTimeRef.current;
    
    // Progress update interval
    progressIntervalRef.current = setInterval(() => {
      if (!isPlaying || isPaused) return;
      
      const elapsed = Date.now() - startTimeRef.current;
      const newProgress = Math.min((elapsed / storyDuration) * 100, 100);
      setProgress(newProgress);
      
      if (newProgress >= 100) {
        nextStory();
      }
    }, STORY_CONFIG.PROGRESS_UPDATE_INTERVAL);

  }, [isPlaying, isPaused, storyDuration]);

  // Pause timer
  const pauseTimer = useCallback(() => {
    setIsPaused(true);
    pausedTimeRef.current = Date.now() - startTimeRef.current;
    clearTimers();
  }, [clearTimers]);

  // Resume timer
  const resumeTimer = useCallback(() => {
    setIsPaused(false);
    startTimer();
  }, [startTimer]);

  // Next story
  const nextStory = useCallback(() => {
    const nextStoryIdx = currentStoryIndex + 1;
    
    if (nextStoryIdx < currentUser.stories.length) {
      // Next story in current user
      setCurrentStoryIndex(nextStoryIdx);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else {
      // Next user
      const nextUserIdx = currentUserIndex + 1;
      if (nextUserIdx < userStories.length) {
        setCurrentUserIndex(nextUserIdx);
        setCurrentStoryIndex(0);
        setProgress(0);
        pausedTimeRef.current = 0;
      } else {
        // End of all stories
        onClose();
      }
    }
  }, [currentUserIndex, currentStoryIndex, currentUser, userStories, onClose]);

  // Previous story
  const previousStory = useCallback(() => {
    const prevStoryIdx = currentStoryIndex - 1;
    
    if (prevStoryIdx >= 0) {
      // Previous story in current user
      setCurrentStoryIndex(prevStoryIdx);
      setProgress(0);
      pausedTimeRef.current = 0;
    } else {
      // Previous user
      const prevUserIdx = currentUserIndex - 1;
      if (prevUserIdx >= 0) {
        const prevUser = userStories[prevUserIdx];
        setCurrentUserIndex(prevUserIdx);
        setCurrentStoryIndex(prevUser.stories.length - 1);
        setProgress(0);
        pausedTimeRef.current = 0;
      }
    }
  }, [currentUserIndex, currentStoryIndex, userStories]);

  // Handle touch/mouse events
  const handlePointerDown = useCallback((e: React.PointerEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const isLeftSide = x < rect.width / 2;

    // Start hold timer for pause
    holdTimeoutRef.current = setTimeout(() => {
      pauseTimer();
    }, STORY_CONFIG.HOLD_THRESHOLD);

    // Handle tap zones
    const handleTap = () => {
      if (holdTimeoutRef.current) {
        clearTimeout(holdTimeoutRef.current);
        holdTimeoutRef.current = null;
      }
      
      if (!isPaused) {
        if (isLeftSide) {
          previousStory();
        } else {
          nextStory();
        }
      }
    };

    // Set up pointer up handler
    const handlePointerUp = () => {
      if (holdTimeoutRef.current) {
        clearTimeout(holdTimeoutRef.current);
        holdTimeoutRef.current = null;
        handleTap();
      } else if (isPaused) {
        resumeTimer();
      }
      
      document.removeEventListener('pointerup', handlePointerUp);
      document.removeEventListener('pointercancel', handlePointerUp);
    };

    document.addEventListener('pointerup', handlePointerUp);
    document.addEventListener('pointercancel', handlePointerUp);
  }, [isPaused, pauseTimer, resumeTimer, previousStory, nextStory]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          previousStory();
          break;
        case 'ArrowRight':
        case ' ':
          e.preventDefault();
          nextStory();
          break;
        case 'Escape':
          onClose();
          break;
        case 'p':
        case 'P':
          if (isPaused) {
            resumeTimer();
          } else {
            pauseTimer();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [previousStory, nextStory, onClose, isPaused, pauseTimer, resumeTimer]);

  // Start timer when story changes
  useEffect(() => {
    setProgress(0);
    pausedTimeRef.current = 0;
    setIsPaused(false);
    
    if (currentStory) {
      // Mark story as viewed
      onStoryView?.(currentStory.id, currentStory.userId);
      startTimer();
    }

    return () => clearTimers();
  }, [currentStory, startTimer, clearTimers, onStoryView]);

  // Cleanup on unmount
  useEffect(() => {
    return () => clearTimers();
  }, [clearTimers]);

  if (!currentUser || !currentStory) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      {/* Story content */}
      <div className="relative w-full h-full max-w-md mx-auto">
        {/* Progress bars */}
        <StoryProgress
          segments={currentUser.stories.length}
          currentSegment={currentStoryIndex}
          progress={progress}
          duration={storyDuration}
        />

        {/* User info */}
        <div className="absolute top-16 left-4 right-4 z-20 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img
              src={currentUser.userAvatar}
              alt={currentUser.userName}
              className="w-10 h-10 rounded-full border-2 border-white object-cover"
            />
            <div>
              <h3 className="text-white font-semibold text-sm">{currentUser.userName}</h3>
              <p className="text-white/80 text-xs">
                {new Date(currentStory.createdAt).toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="text-white p-2 hover:bg-white/20 rounded-full transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Story media */}
        <StoryMedia
          story={currentStory}
          isActive={isPlaying && !isPaused}
          onLoadComplete={() => {
            // Story loaded, ensure timer is running
            if (!isPaused) startTimer();
          }}
          onError={(error) => {
            console.error('Story media error:', error);
            nextStory();
          }}
        />

        {/* Touch/click areas */}
        <div
          className="absolute inset-0 z-10 cursor-pointer"
          onPointerDown={handlePointerDown}
          style={{ touchAction: 'none' }}
        >
          {/* Visual tap zones (for debugging) */}
          {process.env.NODE_ENV === 'development' && (
            <>
              <div className="absolute left-0 top-0 bottom-0 w-1/2 bg-red-500/10" />
              <div className="absolute right-0 top-0 bottom-0 w-1/2 bg-blue-500/10" />
            </>
          )}
        </div>

        {/* Navigation arrows (desktop) */}
        <div className="hidden md:block">
          {currentUserIndex > 0 || currentStoryIndex > 0 ? (
            <button
              onClick={previousStory}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 text-white p-2 hover:bg-white/20 rounded-full transition-colors"
            >
              <ChevronLeft className="w-8 h-8" />
            </button>
          ) : null}
          
          <button
            onClick={nextStory}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 text-white p-2 hover:bg-white/20 rounded-full transition-colors"
          >
            <ChevronRight className="w-8 h-8" />
          </button>
        </div>

        {/* Pause indicator */}
        {isPaused && (
          <div className="absolute inset-0 flex items-center justify-center z-30">
            <div className="bg-black/50 backdrop-blur-sm rounded-full p-4">
              <div className="text-white text-4xl">⏸️</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
