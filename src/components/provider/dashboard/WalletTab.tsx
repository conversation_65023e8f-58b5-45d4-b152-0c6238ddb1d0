'use client';

import { useState, useMemo, useEffect } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  Wallet,
  ArrowUpRight,
  ArrowDownLeft,
  ExternalLink,
  Plus,
  Settings,
  Clock,
  BarChart3
} from 'lucide-react';
import { Timestamp } from 'firebase/firestore';
import ProviderOnboarding from '@/components/payments/ProviderOnboarding';
import toast from 'react-hot-toast';

interface StripeOnboardingStatus {
  isOnboarded: boolean;
  accountId?: string;
  status?: {
    detailsSubmitted: boolean;
    chargesEnabled: boolean;
    payoutsEnabled: boolean;
  };
}

interface StripeBalance {
  available: number;
  pending: number;
}

interface StripePayout {
  id: string;
  amount: number;
  currency: string;
  status: string;
  arrival_date: number;
  created: number;
}

export default function WalletTab() {
  const { earnings, payouts, isLoading } = useProvider();
  const { user, getIdToken } = useAuth();
  const [timeFilter, setTimeFilter] = useState<string>('month');
  const [transactionFilter, setTransactionFilter] = useState<string>('all');
  const [stripeOnboardingStatus, setStripeOnboardingStatus] = useState<StripeOnboardingStatus | null>(null);
  const [stripeBalance, setStripeBalance] = useState<StripeBalance | null>(null);
  const [stripePayouts, setStripePayouts] = useState<StripePayout[]>([]);
  const [isLoadingStripe, setIsLoadingStripe] = useState(true);
  const [showCreateInvoiceModal, setShowCreateInvoiceModal] = useState(false);

  // Load Stripe data on component mount
  useEffect(() => {
    if (user?.id) {
      loadStripeData();
    }
  }, [user?.id]);

  const loadStripeData = async () => {
    if (!user) {
      console.log('No user found, skipping Stripe data load');
      return;
    }

    try {
      setIsLoadingStripe(true);
      console.log('Loading Stripe data for user:', user.id);

      const token = await getIdToken();
      console.log('Got ID token, making API call...');

      // Get real wallet data from our API
      const walletResponse = await fetch('/api/providers/wallet', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('Wallet API response status:', walletResponse.status);

      if (walletResponse.ok) {
        const walletData = await walletResponse.json();
        console.log('Wallet data received:', walletData);

        if (walletData.success) {
          // Set real Stripe data
          setStripeOnboardingStatus({
            isOnboarded: walletData.isOnboarded,
            accountId: walletData.accountId,
            status: walletData.status,
          });

          setStripeBalance({
            available: walletData.balance.available,
            pending: walletData.balance.pending,
          });

          setStripePayouts(walletData.payouts || []);

          if (walletData.error) {
            toast.error(walletData.error);
          }
        } else {
          throw new Error(walletData.error || 'Failed to load wallet data');
        }
      } else {
        const errorText = await walletResponse.text();
        console.error('API Error Response:', errorText);
        throw new Error(`API Error: ${walletResponse.status} - ${errorText}`);
      }
    } catch (error: any) {
      console.error('Error loading Stripe data:', error);

      if (error.message.includes('Not authenticated')) {
        toast.error('Please sign in to view wallet data');
      } else {
        toast.error('Failed to load wallet data. Please try again.');
      }

      // Set empty state instead of demo data
      setStripeOnboardingStatus({
        isOnboarded: false,
        accountId: undefined,
      });
      setStripeBalance({ available: 0, pending: 0 });
      setStripePayouts([]);
    } finally {
      setIsLoadingStripe(false);
    }
  };





  const createInvoice = async (amount: number, description: string) => {
    try {
      const token = await getIdToken();
      const response = await fetch('/api/providers/create-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          amount,
          description,
          successUrl: `${window.location.origin}/provider/dashboard?tab=wallet&payment=success`,
          cancelUrl: `${window.location.origin}/provider/dashboard?tab=wallet&payment=canceled`,
        }),
      });

      if (response.ok) {
        const responseText = await response.text();

        // Check if response is valid JSON
        let result;
        try {
          result = JSON.parse(responseText);
        } catch (jsonError) {
          console.error('Invalid JSON response from create-invoice API:', responseText);
          toast.error('Server returned invalid response. Please try again.');
          return;
        }

        if (result.success) {
          toast.success('Invoice created successfully!');
          // Open payment link in new tab
          window.open(result.paymentUrl, '_blank');
          setShowCreateInvoiceModal(false);
        } else {
          toast.error(result.error || 'Failed to create invoice');
        }
      } else {
        console.error('Create invoice API error:', response.status, response.statusText);
        toast.error(`Server error: ${response.status}. Please try again.`);
      }
    } catch (error) {
      console.error('Error creating invoice:', error);
      toast.error('Network error. Please check your connection and try again.');
    }
  };

  // Calculate wallet stats
  const walletStats = useMemo(() => {
    const totalEarnings = earnings?.reduce((sum, earning) => sum + earning.netAmount, 0) || 0;
    const totalPayouts = payouts?.reduce((sum, payout) => sum + payout.totalAmount, 0) || 0;
    const pendingEarnings = earnings?.filter(e => e.status === 'pending').reduce((sum, earning) => sum + earning.netAmount, 0) || 0;
    const availableBalance = totalEarnings - totalPayouts - pendingEarnings;

    // Calculate this month's earnings
    const now = new Date();
    const thisMonth = earnings?.filter(earning => {
      const earningDate = earning.date instanceof Timestamp ? earning.date.toDate() : new Date(earning.date);
      return earningDate.getMonth() === now.getMonth() && earningDate.getFullYear() === now.getFullYear();
    }).reduce((sum, earning) => sum + earning.amount, 0) || 0;

    // Calculate last month's earnings for comparison
    const lastMonth = earnings?.filter(earning => {
      const earningDate = earning.date instanceof Timestamp ? earning.date.toDate() : new Date(earning.date);
      const lastMonthDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      return earningDate.getMonth() === lastMonthDate.getMonth() && earningDate.getFullYear() === lastMonthDate.getFullYear();
    }).reduce((sum, earning) => sum + earning.amount, 0) || 0;

    const monthlyGrowth = lastMonth > 0 ? ((thisMonth - lastMonth) / lastMonth) * 100 : 0;

    return {
      totalEarnings,
      totalPayouts,
      pendingEarnings,
      availableBalance,
      thisMonth,
      monthlyGrowth
    };
  }, [earnings, payouts]);

  // Filter transactions
  const filteredTransactions = useMemo(() => {
    const allTransactions = [
      ...(earnings?.map(earning => ({ ...earning, type: 'earning' })) || []),
      ...(payouts?.map(payout => ({ ...payout, type: 'payout' })) || [])
    ];

    return allTransactions
      .filter(transaction => {
        if (transactionFilter === 'all') return true;
        return transaction.type === transactionFilter;
      })
      .sort((a, b) => {
        const dateA = a.date instanceof Timestamp ? a.date.toDate() : new Date(a.date);
        const dateB = b.date instanceof Timestamp ? b.date.toDate() : new Date(b.date);
        return dateB.getTime() - dateA.getTime();
      });
  }, [earnings, payouts, transactionFilter]);

  const formatDate = (date: any) => {
    if (!date) return 'No date';
    const dateObj = date instanceof Timestamp ? date.toDate() : new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'processing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTransactionIcon = (type: string) => {
    if (type === 'earning') {
      return <ArrowUpRight className="w-4 h-4 text-green-600" />;
    } else {
      return <ArrowDownLeft className="w-4 h-4 text-blue-600" />;
    }
  };

  if (isLoading || isLoadingStripe) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If not onboarded with Stripe, show onboarding component
  if (!stripeOnboardingStatus?.isOnboarded) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
              <Wallet className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">Fetchly Wallet</h2>
              <p className="text-gray-600">Manage your earnings and payouts</p>
            </div>
          </div>

          <ProviderOnboarding />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Wallet Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Available Balance</p>
              <p className="text-2xl font-bold text-gray-800">${stripeBalance?.available.toFixed(2) || '0.00'}</p>
              <p className="text-sm text-green-600 mt-1">Ready to withdraw</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
              <Wallet className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Balance</p>
              <p className="text-2xl font-bold text-gray-800">${stripeBalance?.pending.toFixed(2) || '0.00'}</p>
              <p className="text-sm text-blue-600 mt-1">Processing</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Earnings</p>
              <p className="text-2xl font-bold text-gray-800">${walletStats.totalEarnings.toFixed(2)}</p>
              <p className="text-sm text-purple-600 mt-1">All time</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-gray-800">${walletStats.thisMonth.toFixed(2)}</p>
              <div className="flex items-center mt-1">
                {walletStats.monthlyGrowth >= 0 ? (
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                )}
                <span className={`text-sm ${walletStats.monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(walletStats.monthlyGrowth).toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setShowCreateInvoiceModal(true)}
            className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-4 rounded-xl hover:from-green-700 hover:to-blue-700 transition-all flex items-center justify-center space-x-2"
          >
            <Plus className="w-5 h-5" />
            <span>Create Invoice</span>
          </button>
          <button
            onClick={() => window.open('https://dashboard.stripe.com', '_blank')}
            className="bg-white border-2 border-gray-200 text-gray-700 p-4 rounded-xl hover:border-blue-300 hover:text-blue-600 transition-all flex items-center justify-center space-x-2"
          >
            <ExternalLink className="w-5 h-5" />
            <span>Stripe Dashboard</span>
          </button>
          <button className="bg-white border-2 border-gray-200 text-gray-700 p-4 rounded-xl hover:border-green-300 hover:text-green-600 transition-all flex items-center justify-center space-x-2">
            <Download className="w-5 h-5" />
            <span>Tax Documents</span>
          </button>
        </div>
      </div>

      {/* Transaction History */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800">Transaction History</h3>
            <p className="text-gray-600">Track your earnings and payouts</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={transactionFilter}
              onChange={(e) => setTransactionFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Transactions</option>
              <option value="earning">Earnings</option>
              <option value="payout">Payouts</option>
            </select>
            
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>

        {filteredTransactions.length === 0 ? (
          <div className="text-center py-12">
            <Wallet className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-semibold text-gray-800 mb-2">No transactions found</h4>
            <p className="text-gray-600">
              {transactionFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'Your transaction history will appear here once you start earning.'}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    {getTransactionIcon(transaction.type)}
                  </div>
                  
                  <div>
                    <p className="font-medium text-gray-800">
                      {transaction.type === 'earning' ? 'Service Payment' : 'Payout'}
                    </p>
                    <p className="text-sm text-gray-600">
                      {transaction.type === 'earning' 
                        ? `From ${(transaction as any).customerName || 'Customer'}`
                        : `To ${(transaction as any).bankAccount || 'Bank Account'}`
                      }
                    </p>
                    <p className="text-xs text-gray-500">{formatDate(transaction.date)}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <p className={`font-semibold ${
                    transaction.type === 'earning' ? 'text-green-600' : 'text-blue-600'
                  }`}>
                    {transaction.type === 'earning' ? '+' : '-'}${transaction.amount.toFixed(2)}
                  </p>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(transaction.status)}`}>
                    {transaction.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Stripe Payouts */}
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800">Stripe Payouts</h3>
            <p className="text-sm text-gray-600">Automatic payouts to your bank account</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => window.open('https://dashboard.stripe.com/payouts', '_blank')}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-lg hover:from-green-700 hover:to-blue-700 transition-all"
            >
              <ExternalLink className="w-4 h-4" />
              <span>Manage Payouts</span>
            </button>
          </div>
        </div>

        {/* Payout History */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-800">Recent Payouts</h4>

          {stripePayouts.length > 0 ? (
            <div className="space-y-3">
              {stripePayouts.map((payout) => (
                <div key={payout.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                      <ArrowDownLeft className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Payout to Bank Account</p>
                      <p className="text-sm text-gray-600">
                        {payout.status === 'paid' ? 'Completed' : 'In Transit'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(payout.arrival_date * 1000).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      ${(payout.amount / 100).toFixed(2)}
                    </p>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${
                      payout.status === 'paid'
                        ? 'bg-green-100 text-green-800 border-green-200'
                        : 'bg-blue-100 text-blue-800 border-blue-200'
                    }`}>
                      {payout.status === 'paid' ? 'Paid' : 'In Transit'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <DollarSign className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-600 mb-2">No payouts yet</p>
              <p className="text-sm text-gray-500">
                Payouts will appear here once you start earning
              </p>
            </div>
          )}
        </div>

        {/* Payout Information */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="font-medium text-gray-800 mb-3">Payout Information</h4>
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-800">Automatic Payouts</p>
                <p className="text-sm text-gray-600">Managed by Stripe Connect</p>
                <p className="text-xs text-gray-500 mt-1">
                  Payouts are processed automatically based on your Stripe settings
                </p>
              </div>
              <button
                onClick={() => window.open('https://dashboard.stripe.com/settings/payouts', '_blank')}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1"
              >
                <Settings className="w-4 h-4" />
                <span>Manage</span>
              </button>
            </div>
          </div>
        </div>
      </div>



      {/* Create Invoice Modal */}
      {showCreateInvoiceModal && (
        <CreateInvoiceModal
          isOpen={showCreateInvoiceModal}
          onClose={() => setShowCreateInvoiceModal(false)}
          onCreateInvoice={createInvoice}
        />
      )}

    </div>
  );
}

// Create Invoice Modal Component
interface CreateInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateInvoice: (amount: number, description: string) => void;
}

const CreateInvoiceModal: React.FC<CreateInvoiceModalProps> = ({
  isOpen,
  onClose,
  onCreateInvoice,
}) => {
  const [amount, setAmount] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [isCreating, setIsCreating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const numAmount = parseFloat(amount);
    if (numAmount < 1 || numAmount > 10000) {
      toast.error('Amount must be between $1 and $10,000');
      return;
    }

    if (!description.trim()) {
      toast.error('Description is required');
      return;
    }

    setIsCreating(true);
    try {
      await onCreateInvoice(numAmount, description.trim());
      setAmount('');
      setDescription('');
    } catch (error) {
      console.error('Error creating invoice:', error);
    } finally {
      setIsCreating(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-800">Create Invoice</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Amount ($)
            </label>
            <input
              type="number"
              min="1"
              max="10000"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0.00"
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe the service or product..."
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
              required
            />
          </div>

          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4 border border-green-200">
            <p className="text-sm text-gray-600 mb-1">Platform Fee (10%)</p>
            <p className="text-lg font-semibold text-green-600">
              You'll receive: ${amount ? (parseFloat(amount) * 0.9).toFixed(2) : '0.00'}
            </p>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isCreating}
              className="flex-1 px-4 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isCreating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Creating...
                </>
              ) : (
                'Create Invoice'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
