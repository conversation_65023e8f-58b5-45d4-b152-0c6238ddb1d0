"use client";

import { useState, useEffect } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { toast } from 'react-hot-toast';
import { Crown, Lock, Shield, Check } from 'lucide-react';
import { ProviderSubscription } from '@/lib/types';
import * as SubscriptionService from '@/lib/subscription';

export default function SubscriptionTab() {
  const { provider } = useProvider();
  const [subscription, setSubscription] = useState<ProviderSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [isUpgrading, setIsUpgrading] = useState(false);
  
  // Get current subscription status
  const currentTier = provider?.membershipTier || 'free';
  const isProMember = currentTier === 'pro' || currentTier === 'premium';
  const isFreeTier = currentTier === 'free';
  const isProTier = currentTier === 'pro';
  const isPremiumTier = currentTier === 'premium';

  useEffect(() => {
    loadSubscriptionData();
  }, [provider]);

  const loadSubscriptionData = async () => {
    if (!provider?.id) return;

    try {
      const subscriptionData = await SubscriptionService.getSubscription(provider.id);
      setSubscription(subscriptionData);
    } catch (error) {
      console.error('Error loading subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (tier: 'pro' | 'premium') => {
    if (!provider?.id) {
      toast.error('Provider information not found');
      return;
    }

    setIsUpgrading(true);

    try {
      const result = await SubscriptionService.createSubscriptionCheckout(
        provider.id,
        tier,
        `${window.location.origin}/provider/dashboard?tab=subscription&success=true`,
        `${window.location.origin}/provider/dashboard?tab=subscription&cancelled=true`
      );

      if (result.success && result.url) {
        window.location.href = result.url;
      } else {
        throw new Error(result.error || 'Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating subscription checkout:', error);
      toast.error('Failed to start subscription process. Please try again.');
    } finally {
      setIsUpgrading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const handleCancelSubscription = async () => {
    if (!provider?.id || !subscription) return;

    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
      return;
    }

    try {
      const result = await SubscriptionService.cancelSubscription(provider.id, true);
      if (result.success) {
        toast.success('Subscription cancelled. You will retain access until the end of your billing period.');
        loadSubscriptionData();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('Failed to cancel subscription. Please try again.');
    }
  };

  return (
    <div className="space-y-8">
      {/* Current Status */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
              !isFreeTier ? 'bg-gradient-to-br from-purple-500 to-blue-600' : 'bg-gray-100'
            }`}>
              {!isFreeTier ? (
                <Crown className="w-8 h-8 text-white" />
              ) : (
                <Lock className="w-8 h-8 text-gray-600" />
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">
                {isPremiumTier ? 'Premium Member' : isProTier ? 'Pro Member' : 'Free Plan'}
              </h2>
              <p className="text-gray-600">
                {!isFreeTier
                  ? 'You have access to premium features'
                  : 'Upgrade to unlock advanced features'
                }
              </p>
              {subscription && subscription.currentPeriodEnd && (
                <p className="text-sm text-gray-500 mt-1">
                  {subscription.cancelAtPeriodEnd ? 'Cancels on' : 'Renews on'}: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
          {isFreeTier && (
            <button
              onClick={() => handleUpgrade('pro')}
              disabled={isUpgrading}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50"
            >
              {isUpgrading ? 'Processing...' : 'Upgrade Now'}
            </button>
          )}
        </div>
      </div>

      {/* Subscription Plans */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Free Plan */}
        <div className={`relative rounded-2xl p-8 border-2 transition-all duration-200 ${
          !isProMember
            ? 'border-blue-500 bg-blue-50 shadow-lg'
            : 'border-gray-200 bg-white hover:border-gray-300'
        }`}>
          {!isProMember && (
            <div className="absolute -top-3 left-6 bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
              Current Plan
            </div>
          )}

          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-gray-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-2">Free</h3>
            <p className="text-gray-600 mb-4">Perfect for getting started</p>
            <div className="text-4xl font-bold text-gray-800">
              $0<span className="text-lg text-gray-500">/month</span>
            </div>
          </div>

          <ul className="space-y-3 mb-8">
            {[
              'Up to 10 bookings per month',
              'Basic calendar management',
              'Email notifications',
              'Standard customer support',
              'Basic analytics dashboard',
              'Secure payment processing'
            ].map((feature, index) => (
              <li key={index} className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-green-600 flex-shrink-0" />
                <span className="text-gray-700">{feature}</span>
              </li>
            ))}
          </ul>

          <button
            className={`w-full py-3 rounded-xl font-semibold transition-all duration-200 ${
              !isProMember
                ? 'bg-blue-600 text-white cursor-default'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            disabled={!isProMember}
          >
            {!isProMember ? 'Current Plan' : 'Downgrade'}
          </button>
        </div>

        {/* Pro Plan */}
        <div className={`relative rounded-2xl p-8 border-2 transition-all duration-200 ${
          isProMember
            ? 'border-purple-500 bg-gradient-to-br from-purple-50 to-blue-50 shadow-lg'
            : 'border-purple-200 bg-white hover:border-purple-300 hover:shadow-md'
        }`}>
          {isProMember && (
            <div className="absolute -top-3 left-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
              Current Plan
            </div>
          )}

          <div className="absolute -top-3 right-6 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold">
            POPULAR
          </div>

          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Crown className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-2">Pro</h3>
            <p className="text-gray-600 mb-4">For growing businesses</p>
            <div className="text-4xl font-bold text-gray-800">
              $20<span className="text-lg text-gray-500">/month</span>
            </div>
          </div>

          <ul className="space-y-3 mb-8">
            {[
              'Unlimited bookings',
              'Advanced calendar & scheduling',
              'SMS + Email notifications',
              'Priority customer support',
              'Advanced analytics & reports',
              'Featured listing placement',
              'Custom branding options',
              'Integration with external tools',
              'Automated review requests',
              'Revenue optimization insights'
            ].map((feature, index) => (
              <li key={index} className="flex items-center space-x-3">
                <Check className="w-5 h-5 text-purple-600 flex-shrink-0" />
                <span className="text-gray-700">{feature}</span>
              </li>
            ))}
          </ul>

          <button
            onClick={isProMember ? undefined : handleUpgrade}
            disabled={isProMember || isUpgrading}
            className={`w-full py-3 rounded-xl font-semibold transition-all duration-200 ${
              isProMember
                ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white cursor-default'
                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 transform hover:scale-105'
            }`}
          >
            {isProMember ? 'Current Plan' : isUpgrading ? 'Processing...' : 'Upgrade to Pro'}
          </button>
        </div>
      </div>

      {/* Feature Comparison */}
      <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
        <h3 className="text-xl font-bold text-gray-800 mb-6 text-center">Feature Comparison</h3>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-800">Features</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-800">Free</th>
                <th className="text-center py-3 px-4 font-semibold text-purple-800">Pro</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {[
                { feature: 'Monthly Bookings', free: '10', pro: 'Unlimited' },
                { feature: 'Analytics Dashboard', free: 'Basic', pro: 'Advanced' },
                { feature: 'Customer Support', free: 'Email', pro: 'Priority' },
                { feature: 'Listing Placement', free: 'Standard', pro: 'Featured' },
                { feature: 'Notifications', free: 'Email', pro: 'Email + SMS' },
                { feature: 'Custom Branding', free: '✗', pro: '✓' },
                { feature: 'Integrations', free: '✗', pro: '✓' },
                { feature: 'Revenue Insights', free: '✗', pro: '✓' }
              ].map((row, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="py-3 px-4 text-gray-700">{row.feature}</td>
                  <td className="py-3 px-4 text-center text-gray-600">{row.free}</td>
                  <td className="py-3 px-4 text-center text-purple-600 font-medium">{row.pro}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="bg-gray-50 rounded-2xl p-8">
        <h3 className="text-xl font-bold text-gray-800 mb-6">Frequently Asked Questions</h3>

        <div className="space-y-4">
          {[
            {
              q: "Can I cancel my subscription anytime?",
              a: "Yes, you can cancel your Pro subscription at any time. You'll continue to have access to Pro features until the end of your billing period."
            },
            {
              q: "What happens to my data if I downgrade?",
              a: "Your data is always safe. If you downgrade, you'll lose access to Pro features but all your bookings and customer data remain intact."
            },
            {
              q: "Do you offer refunds?",
              a: "We offer a 30-day money-back guarantee for new Pro subscribers. Contact support if you're not satisfied."
            },
            {
              q: "Can I upgrade mid-billing cycle?",
              a: "Yes! When you upgrade, you'll be charged a prorated amount for the remainder of your billing cycle."
            }
          ].map((faq, index) => (
            <div key={index} className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">{faq.q}</h4>
              <p className="text-gray-600 text-sm">{faq.a}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
