'use client';

import { useState, useEffect } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { toast } from 'react-hot-toast';
import {
  User,
  Bell,
  Shield,
  CreditCard,
  MapPin,
  Clock,
  Phone,
  Mail,
  Camera,
  Save,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Upload,
  Globe,
  Smartphone,
  Lock,
  Key,
  AlertTriangle,
  CheckCircle,
  Settings,
  Palette,
  Languages,
  HelpCircle,
  LogOut,
  Download,
  FileText,
  Calendar,
  DollarSign,
  Star,
  TrendingUp,
  Zap,
  Link,
  ExternalLink
} from 'lucide-react';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

interface FormData {
  [key: string]: any;
}

interface NotificationSettings {
  newBookings: boolean;
  paymentNotifications: boolean;
  reviewNotifications: boolean;
  marketingUpdates: boolean;
  smsNotifications: boolean;
  emailDigest: boolean;
  reminderNotifications: boolean;
}

interface AvailabilitySlot {
  day: string;
  enabled: boolean;
  startTime: string;
  endTime: string;
  breaks: { start: string; end: string }[];
}

export default function SettingsTab() {
  const { provider, isLoading } = useProvider();
  const [activeSection, setActiveSection] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState<FormData>({});
  const [notifications, setNotifications] = useState<NotificationSettings>({
    newBookings: true,
    paymentNotifications: true,
    reviewNotifications: true,
    marketingUpdates: false,
    smsNotifications: true,
    emailDigest: true,
    reminderNotifications: true,
  });
  const [availability, setAvailability] = useState<AvailabilitySlot[]>([
    { day: 'Monday', enabled: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    { day: 'Tuesday', enabled: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    { day: 'Wednesday', enabled: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    { day: 'Thursday', enabled: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    { day: 'Friday', enabled: true, startTime: '09:00', endTime: '17:00', breaks: [] },
    { day: 'Saturday', enabled: false, startTime: '10:00', endTime: '16:00', breaks: [] },
    { day: 'Sunday', enabled: false, startTime: '10:00', endTime: '16:00', breaks: [] },
  ]);

  const sections = [
    { id: 'profile', name: 'Profile', icon: User, description: 'Personal information and photo' },
    { id: 'business', name: 'Business', icon: MapPin, description: 'Business details and services' },
    { id: 'availability', name: 'Availability', icon: Clock, description: 'Working hours and schedule' },
    { id: 'notifications', name: 'Notifications', icon: Bell, description: 'Communication preferences' },
    { id: 'security', name: 'Security', icon: Shield, description: 'Password and account security' },
    { id: 'billing', name: 'Billing', icon: CreditCard, description: 'Payment methods and invoices' },
    { id: 'preferences', name: 'Preferences', icon: Settings, description: 'App settings and customization' },
    { id: 'integrations', name: 'Integrations', icon: Link, description: 'Third-party connections' },
    { id: 'data', name: 'Data & Privacy', icon: FileText, description: 'Export data and privacy settings' }
  ];

  useEffect(() => {
    if (provider) {
      setFormData({
        ownerName: provider.ownerName || '',
        email: provider.email || '',
        phone: provider.phone || '',
        businessName: provider.businessName || '',
        bio: provider.bio || '',
        address: provider.address || '',
        city: provider.city || '',
        state: provider.state || '',
        zipCode: provider.zipCode || '',
        website: provider.website || '',
        instagram: provider.instagram || '',
        facebook: provider.facebook || '',
        serviceTypes: provider.serviceTypes || [],
        experience: provider.experience || '',
        certifications: provider.certifications || [],
        languages: provider.languages || ['English'],
        emergencyContact: provider.emergencyContact || '',
        businessLicense: provider.businessLicense || '',
        insurance: provider.insurance || '',
      });
    }
  }, [provider]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    if (!provider?.id) return;

    setIsSaving(true);
    try {
      await updateDoc(doc(db, COLLECTIONS.PROVIDERS, provider.id), {
        ...formData,
        updatedAt: new Date().toISOString(),
      });

      toast.success('Settings saved successfully!');
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleNotificationChange = (key: keyof NotificationSettings, value: boolean) => {
    setNotifications(prev => ({ ...prev, [key]: value }));
  };

  const handleAvailabilityChange = (index: number, field: string, value: any) => {
    setAvailability(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      return updated;
    });
  };

  const addBreak = (dayIndex: number) => {
    setAvailability(prev => {
      const updated = [...prev];
      updated[dayIndex].breaks.push({ start: '12:00', end: '13:00' });
      return updated;
    });
  };

  const removeBreak = (dayIndex: number, breakIndex: number) => {
    setAvailability(prev => {
      const updated = [...prev];
      updated[dayIndex].breaks.splice(breakIndex, 1);
      return updated;
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const renderProfileSection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">Profile Information</h3>
          <p className="text-gray-600 mt-1">Manage your personal and business profile details</p>
        </div>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-105"
        >
          <Edit className="w-4 h-4" />
          <span>{isEditing ? 'Cancel' : 'Edit Profile'}</span>
        </button>
      </div>

      {/* Profile Photo Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
        <div className="flex items-center space-x-6">
          <div className="relative">
            <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
              {provider?.ownerName?.charAt(0) || 'P'}
            </div>
            {isEditing && (
              <button className="absolute -bottom-2 -right-2 bg-white rounded-full p-2 shadow-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                <Camera className="w-4 h-4 text-gray-600" />
              </button>
            )}
          </div>
          <div className="flex-1">
            <h4 className="text-lg font-semibold text-gray-900">{provider?.businessName || 'Business Name'}</h4>
            <p className="text-gray-600">{provider?.ownerName || 'Owner Name'}</p>
            <div className="flex items-center space-x-4 mt-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                Verified
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {provider?.membershipTier || 'Free'} Member
              </span>
            </div>
          </div>
          {isEditing && (
            <button className="bg-white text-blue-600 px-4 py-2 rounded-lg border border-blue-200 hover:bg-blue-50 transition-colors flex items-center space-x-2">
              <Upload className="w-4 h-4" />
              <span>Upload Photo</span>
            </button>
          )}
        </div>
      </div>

      {/* Personal Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <h4 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Personal Details</h4>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
            <input
              type="text"
              value={formData.ownerName || ''}
              onChange={(e) => handleInputChange('ownerName', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="Enter your full name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
              <input
                type="email"
                value={formData.email || ''}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={!isEditing}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
              <input
                type="tel"
                value={formData.phone || ''}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                disabled={!isEditing}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
                placeholder="(*************"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Emergency Contact</label>
            <input
              type="tel"
              value={formData.emergencyContact || ''}
              onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="Emergency contact number"
            />
          </div>
        </div>

        <div className="space-y-6">
          <h4 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Business Information</h4>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Business Name *</label>
            <input
              type="text"
              value={formData.businessName || ''}
              onChange={(e) => handleInputChange('businessName', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="Your business name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
            <div className="relative">
              <Globe className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
              <input
                type="url"
                value={formData.website || ''}
                onChange={(e) => handleInputChange('website', e.target.value)}
                disabled={!isEditing}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
                placeholder="https://yourwebsite.com"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Business License</label>
            <input
              type="text"
              value={formData.businessLicense || ''}
              onChange={(e) => handleInputChange('businessLicense', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="License number"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Insurance Information</label>
            <input
              type="text"
              value={formData.insurance || ''}
              onChange={(e) => handleInputChange('insurance', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="Insurance provider and policy"
            />
          </div>
        </div>
      </div>

      {/* Bio Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Professional Bio</label>
        <textarea
          rows={6}
          value={formData.bio || ''}
          onChange={(e) => handleInputChange('bio', e.target.value)}
          disabled={!isEditing}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
          placeholder="Tell customers about yourself, your experience, and what makes your services special..."
        />
        <p className="text-sm text-gray-500 mt-1">
          {formData.bio?.length || 0}/500 characters
        </p>
      </div>

      {/* Save Button */}
      {isEditing && (
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            onClick={() => setIsEditing(false)}
            className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
          >
            <Save className="w-4 h-4" />
            <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      )}
    </div>
  );

  const renderBusinessSection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">Business Information</h3>
          <p className="text-gray-600 mt-1">Manage your business details and service offerings</p>
        </div>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-105"
        >
          <Edit className="w-4 h-4" />
          <span>{isEditing ? 'Cancel' : 'Edit Business'}</span>
        </button>
      </div>

      {/* Service Information */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Star className="w-5 h-5 text-yellow-500 mr-2" />
          Service Details
        </h4>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Primary Service Type *</label>
            <select
              value={formData.serviceTypes?.[0] || ''}
              onChange={(e) => handleInputChange('serviceTypes', [e.target.value])}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
            >
              <option value="">Select a service type</option>
              <option value="pet-grooming">Pet Grooming</option>
              <option value="pet-sitting">Pet Sitting</option>
              <option value="dog-walking">Dog Walking</option>
              <option value="veterinary">Veterinary Services</option>
              <option value="pet-training">Pet Training</option>
              <option value="pet-boarding">Pet Boarding</option>
              <option value="pet-daycare">Pet Daycare</option>
              <option value="pet-transport">Pet Transportation</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Years of Experience</label>
            <input
              type="number"
              min="0"
              max="50"
              value={formData.experience || ''}
              onChange={(e) => handleInputChange('experience', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="Years of experience"
            />
          </div>
        </div>

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Certifications & Qualifications</label>
          <textarea
            rows={3}
            value={formData.certifications?.join(', ') || ''}
            onChange={(e) => handleInputChange('certifications', e.target.value.split(', ').filter(Boolean))}
            disabled={!isEditing}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
            placeholder="List your certifications, licenses, and qualifications (separated by commas)"
          />
        </div>
      </div>

      {/* Location Information */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <MapPin className="w-5 h-5 text-purple-500 mr-2" />
          Location & Service Area
        </h4>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
            <input
              type="text"
              value={formData.city || ''}
              onChange={(e) => handleInputChange('city', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="City"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
            <input
              type="text"
              value={formData.state || ''}
              onChange={(e) => handleInputChange('state', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="State"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">ZIP Code *</label>
            <input
              type="text"
              value={formData.zipCode || ''}
              onChange={(e) => handleInputChange('zipCode', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="ZIP Code"
            />
          </div>
        </div>

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Full Business Address</label>
          <textarea
            rows={3}
            value={formData.address || ''}
            onChange={(e) => handleInputChange('address', e.target.value)}
            disabled={!isEditing}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
            placeholder="Complete business address"
          />
        </div>
      </div>

      {/* Social Media & Online Presence */}
      <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-2xl p-6 border border-orange-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Globe className="w-5 h-5 text-orange-500 mr-2" />
          Online Presence
        </h4>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Instagram Handle</label>
            <div className="relative">
              <span className="absolute left-3 top-3 text-gray-500">@</span>
              <input
                type="text"
                value={formData.instagram || ''}
                onChange={(e) => handleInputChange('instagram', e.target.value)}
                disabled={!isEditing}
                className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
                placeholder="your_business_handle"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Facebook Page</label>
            <input
              type="text"
              value={formData.facebook || ''}
              onChange={(e) => handleInputChange('facebook', e.target.value)}
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
              placeholder="Facebook page URL"
            />
          </div>
        </div>
      </div>

      {/* Languages */}
      <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl p-6 border border-indigo-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Languages className="w-5 h-5 text-indigo-500 mr-2" />
          Languages Spoken
        </h4>

        <div>
          <input
            type="text"
            value={formData.languages?.join(', ') || ''}
            onChange={(e) => handleInputChange('languages', e.target.value.split(', ').filter(Boolean))}
            disabled={!isEditing}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
            placeholder="English, Spanish, French (separated by commas)"
          />
          <p className="text-sm text-gray-500 mt-1">
            List all languages you can communicate in with customers
          </p>
        </div>
      </div>

      {/* Save Button */}
      {isEditing && (
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            onClick={() => setIsEditing(false)}
            className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
          >
            <Save className="w-4 h-4" />
            <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      )}
    </div>
  );

  const renderAvailabilitySection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">Availability & Schedule</h3>
          <p className="text-gray-600 mt-1">Set your working hours and availability for bookings</p>
        </div>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-105"
        >
          <Clock className="w-4 h-4" />
          <span>{isEditing ? 'Cancel' : 'Edit Schedule'}</span>
        </button>
      </div>

      {/* Weekly Schedule */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
          <Calendar className="w-5 h-5 text-green-500 mr-2" />
          Weekly Schedule
        </h4>

        <div className="space-y-4">
          {availability.map((day, index) => (
            <div key={day.day} className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={day.enabled}
                      onChange={(e) => handleAvailabilityChange(index, 'enabled', e.target.checked)}
                      disabled={!isEditing}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                  <span className="font-medium text-gray-900 w-20">{day.day}</span>
                </div>

                {day.enabled && (
                  <div className="flex items-center space-x-3">
                    <input
                      type="time"
                      value={day.startTime}
                      onChange={(e) => handleAvailabilityChange(index, 'startTime', e.target.value)}
                      disabled={!isEditing}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
                    />
                    <span className="text-gray-500">to</span>
                    <input
                      type="time"
                      value={day.endTime}
                      onChange={(e) => handleAvailabilityChange(index, 'endTime', e.target.value)}
                      disabled={!isEditing}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
                    />
                    {isEditing && (
                      <button
                        onClick={() => addBreak(index)}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        + Add Break
                      </button>
                    )}
                  </div>
                )}
              </div>

              {/* Breaks */}
              {day.enabled && day.breaks.length > 0 && (
                <div className="ml-14 space-y-2">
                  <p className="text-sm font-medium text-gray-700">Breaks:</p>
                  {day.breaks.map((breakTime, breakIndex) => (
                    <div key={breakIndex} className="flex items-center space-x-3">
                      <input
                        type="time"
                        value={breakTime.start}
                        onChange={(e) => {
                          const updatedBreaks = [...day.breaks];
                          updatedBreaks[breakIndex].start = e.target.value;
                          handleAvailabilityChange(index, 'breaks', updatedBreaks);
                        }}
                        disabled={!isEditing}
                        className="px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
                      />
                      <span className="text-gray-500 text-sm">to</span>
                      <input
                        type="time"
                        value={breakTime.end}
                        onChange={(e) => {
                          const updatedBreaks = [...day.breaks];
                          updatedBreaks[breakIndex].end = e.target.value;
                          handleAvailabilityChange(index, 'breaks', updatedBreaks);
                        }}
                        disabled={!isEditing}
                        className="px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50"
                      />
                      {isEditing && (
                        <button
                          onClick={() => removeBreak(index, breakIndex)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Booking Preferences */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Settings className="w-5 h-5 text-purple-500 mr-2" />
          Booking Preferences
        </h4>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Advance Booking Required</label>
            <select
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
            >
              <option value="0">Same day booking allowed</option>
              <option value="1">1 day advance notice</option>
              <option value="2">2 days advance notice</option>
              <option value="7">1 week advance notice</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Maximum Bookings Per Day</label>
            <input
              type="number"
              min="1"
              max="20"
              defaultValue="5"
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Buffer Time Between Bookings</label>
            <select
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
            >
              <option value="0">No buffer time</option>
              <option value="15">15 minutes</option>
              <option value="30">30 minutes</option>
              <option value="60">1 hour</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Auto-Accept Bookings</label>
            <select
              disabled={!isEditing}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
            >
              <option value="false">Manual approval required</option>
              <option value="true">Auto-accept all bookings</option>
            </select>
          </div>
        </div>
      </div>

      {/* Save Button */}
      {isEditing && (
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            onClick={() => setIsEditing(false)}
            className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
          >
            <Save className="w-4 h-4" />
            <span>{isSaving ? 'Saving...' : 'Save Schedule'}</span>
          </button>
        </div>
      )}
    </div>
  );

  const renderNotificationsSection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900">Notification Preferences</h3>
        <p className="text-gray-600 mt-1">Choose how and when you want to be notified</p>
      </div>

      {/* Email Notifications */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Mail className="w-5 h-5 text-blue-500 mr-2" />
          Email Notifications
        </h4>

        <div className="space-y-4">
          {[
            { key: 'newBookings', title: 'New Bookings', description: 'Get notified when you receive new booking requests' },
            { key: 'paymentNotifications', title: 'Payment Updates', description: 'Notifications about payments, payouts, and earnings' },
            { key: 'reviewNotifications', title: 'Customer Reviews', description: 'Get notified when customers leave reviews' },
            { key: 'reminderNotifications', title: 'Appointment Reminders', description: 'Reminders about upcoming appointments' },
            { key: 'emailDigest', title: 'Weekly Digest', description: 'Weekly summary of your business activity' },
            { key: 'marketingUpdates', title: 'Business Tips', description: 'Tips and updates about growing your business' }
          ].map((notification) => (
            <div key={notification.key} className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
              <div>
                <h5 className="font-medium text-gray-800">{notification.title}</h5>
                <p className="text-sm text-gray-600">{notification.description}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notifications[notification.key as keyof NotificationSettings]}
                  onChange={(e) => handleNotificationChange(notification.key as keyof NotificationSettings, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* SMS Notifications */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Smartphone className="w-5 h-5 text-green-500 mr-2" />
          SMS Notifications
        </h4>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
            <div>
              <h5 className="font-medium text-gray-800">SMS Alerts</h5>
              <p className="text-sm text-gray-600">Urgent notifications via text message</p>
              <p className="text-xs text-gray-500 mt-1">Standard messaging rates may apply</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.smsNotifications}
                onChange={(e) => handleNotificationChange('smsNotifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Notification Schedule */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Clock className="w-5 h-5 text-purple-500 mr-2" />
          Quiet Hours
        </h4>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Do Not Disturb Start</label>
            <input
              type="time"
              defaultValue="22:00"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Do Not Disturb End</label>
            <input
              type="time"
              defaultValue="08:00"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
        </div>
        <p className="text-sm text-gray-600 mt-3">
          Non-urgent notifications will be delayed during these hours
        </p>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
        >
          <Save className="w-4 h-4" />
          <span>{isSaving ? 'Saving...' : 'Save Preferences'}</span>
        </button>
      </div>
    </div>
  );

  const renderSecuritySection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900">Security & Privacy</h3>
        <p className="text-gray-600 mt-1">Manage your account security and privacy settings</p>
      </div>

      {/* Password Management */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Lock className="w-5 h-5 text-blue-500 mr-2" />
          Password & Authentication
        </h4>

        <div className="space-y-6">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h5 className="font-medium text-gray-800 mb-2">Change Password</h5>
            <p className="text-sm text-gray-600 mb-4">Update your password to keep your account secure</p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter current password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter new password"
                />
              </div>
            </div>

            <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
              <Key className="w-4 h-4" />
              <span>Update Password</span>
            </button>
          </div>

          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h5 className="font-medium text-gray-800">Two-Factor Authentication</h5>
                <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
              </div>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
                <Shield className="w-4 h-4" />
                <span>Enable 2FA</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Privacy Settings */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Eye className="w-5 h-5 text-green-500 mr-2" />
          Privacy Settings
        </h4>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
            <div>
              <h5 className="font-medium text-gray-800">Profile Visibility</h5>
              <p className="text-sm text-gray-600">Control who can see your profile information</p>
            </div>
            <select className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
              <option value="public">Public</option>
              <option value="customers">Customers Only</option>
              <option value="private">Private</option>
            </select>
          </div>

          <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
            <div>
              <h5 className="font-medium text-gray-800">Contact Information</h5>
              <p className="text-sm text-gray-600">Show phone number and email to customers</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
            <div>
              <h5 className="font-medium text-gray-800">Online Status</h5>
              <p className="text-sm text-gray-600">Show when you're online and available</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Account Management */}
      <div className="bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl p-6 border border-red-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
          Account Management
        </h4>

        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h5 className="font-medium text-gray-800 mb-2">Deactivate Account</h5>
            <p className="text-sm text-gray-600 mb-4">Temporarily disable your account. You can reactivate it anytime.</p>
            <button className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
              Deactivate Account
            </button>
          </div>

          <div className="bg-red-50 rounded-lg p-4 border border-red-200">
            <h5 className="font-medium text-red-800 mb-2">Delete Account</h5>
            <p className="text-sm text-red-700 mb-4">
              Permanently delete your account and all data. This action cannot be undone.
            </p>
            <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2">
              <Trash2 className="w-4 h-4" />
              <span>Delete Account</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderBillingSection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900">Billing & Payments</h3>
        <p className="text-gray-600 mt-1">Manage your payment methods and billing information</p>
      </div>

      {/* Payment Methods */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <CreditCard className="w-5 h-5 text-green-500 mr-2" />
          Payment Methods
        </h4>

        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-8 bg-blue-600 rounded flex items-center justify-center text-white text-xs font-bold">
                  VISA
                </div>
                <div>
                  <p className="font-medium text-gray-900">•••• •••• •••• 4242</p>
                  <p className="text-sm text-gray-600">Expires 12/25</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Primary
                </span>
                <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                  Edit
                </button>
              </div>
            </div>
          </div>

          <button className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors flex items-center justify-center space-x-2">
            <CreditCard className="w-5 h-5" />
            <span>Add New Payment Method</span>
          </button>
        </div>
      </div>

      {/* Earnings & Payouts */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <DollarSign className="w-5 h-5 text-blue-500 mr-2" />
          Earnings & Payouts
        </h4>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
            <p className="text-sm text-gray-600">Available Balance</p>
            <p className="text-2xl font-bold text-green-600">$1,247.50</p>
          </div>

          <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
            <p className="text-sm text-gray-600">This Month</p>
            <p className="text-2xl font-bold text-blue-600">$3,892.00</p>
          </div>

          <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
            <p className="text-sm text-gray-600">Total Earned</p>
            <p className="text-2xl font-bold text-purple-600">$28,450.75</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPreferencesSection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900">App Preferences</h3>
        <p className="text-gray-600 mt-1">Customize your app experience and settings</p>
      </div>

      {/* Display Settings */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Palette className="w-5 h-5 text-purple-500 mr-2" />
          Display Settings
        </h4>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
            <div>
              <h5 className="font-medium text-gray-800">Dark Mode</h5>
              <p className="text-sm text-gray-600">Switch to dark theme for better viewing in low light</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  const renderIntegrationsSection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900">Integrations</h3>
        <p className="text-gray-600 mt-1">Connect with third-party services to enhance your business</p>
      </div>

      {/* Calendar Integration */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Calendar className="w-5 h-5 text-green-500 mr-2" />
          Calendar Sync
        </h4>

        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
                  G
                </div>
                <div>
                  <h5 className="font-medium text-gray-800">Google Calendar</h5>
                  <p className="text-sm text-gray-600">Sync your bookings with Google Calendar</p>
                </div>
              </div>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
                <Link className="w-4 h-4" />
                <span>Connect</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDataSection = () => (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900">Data & Privacy</h3>
        <p className="text-gray-600 mt-1">Manage your data, privacy settings, and account information</p>
      </div>

      {/* Data Export */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Download className="w-5 h-5 text-blue-500 mr-2" />
          Data Export
        </h4>

        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h5 className="font-medium text-gray-800 mb-2">Download Your Data</h5>
            <p className="text-sm text-gray-600 mb-4">
              Export all your account data including bookings, customer information, and earnings history.
            </p>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Request Data Export</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'profile':
        return renderProfileSection();
      case 'business':
        return renderBusinessSection();
      case 'availability':
        return renderAvailabilitySection();
      case 'notifications':
        return renderNotificationsSection();
      case 'security':
        return renderSecuritySection();
      case 'billing':
        return renderBillingSection();
      case 'preferences':
        return renderPreferencesSection();
      case 'integrations':
        return renderIntegrationsSection();
      case 'data':
        return renderDataSection();
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <h2 className="text-2xl font-bold text-gray-800">Settings</h2>
        <p className="text-gray-600">Manage your account and business preferences</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden sticky top-6">
            <div className="p-6 bg-gradient-to-r from-blue-600 to-purple-600">
              <h3 className="text-white font-semibold text-lg">Settings Menu</h3>
              <p className="text-blue-100 text-sm mt-1">Choose a category</p>
            </div>

            <nav className="p-2">
              {sections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-xl transition-all duration-200 mb-1 ${
                      activeSection === section.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className={`w-5 h-5 ${
                      activeSection === section.id ? 'text-white' : 'text-gray-500'
                    }`} />
                    <div className="flex-1">
                      <span className="font-medium">{section.name}</span>
                      <p className={`text-xs mt-0.5 ${
                        activeSection === section.id ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {section.description}
                      </p>
                    </div>
                  </button>
                );
              })}
            </nav>

            {/* Help Section */}
            <div className="p-4 border-t border-gray-100">
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-left rounded-xl text-gray-700 hover:bg-gray-50 transition-colors">
                <HelpCircle className="w-5 h-5 text-gray-500" />
                <div>
                  <span className="font-medium">Need Help?</span>
                  <p className="text-xs text-gray-500 mt-0.5">Contact support</p>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 min-h-[600px]">
            <div className="p-8">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
