'use client';

import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

interface LanguageSwitcherProps {
  variant?: 'button' | 'toggle' | 'dropdown';
  className?: string;
}

export default function LanguageSwitcher({ 
  variant = 'toggle',
  className = '' 
}: LanguageSwitcherProps) {
  const { language, setLanguage, t } = useLanguage();

  if (variant === 'button') {
    return (
      <button
        onClick={() => setLanguage(language === 'en' ? 'es' : 'en')}
        className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-lg border border-gray-200 bg-white hover:bg-gray-50 transition-colors ${className}`}
        title={t('language.select')}
      >
        <span className="text-sm font-medium">
          {language === 'en' ? '🇺🇸 EN' : '🇪🇸 ES'}
        </span>
      </button>
    );
  }

  return (
    <div className={`inline-flex items-center bg-gray-100 rounded-full p-1 ${className}`}>
      <button
        onClick={() => setLanguage('en')}
        className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
          language === 'en' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'
        }`}
        title="English"
      >
        🇺🇸 EN
      </button>
      <button
        onClick={() => setLanguage('es')}
        className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
          language === 'es' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'
        }`}
        title="Español"
      >
        🇪🇸 ES
      </button>
    </div>
  );
}
