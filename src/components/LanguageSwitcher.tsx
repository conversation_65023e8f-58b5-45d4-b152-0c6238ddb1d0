'use client';

import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

interface LanguageSwitcherProps {
  variant?: 'button' | 'toggle' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

export default function LanguageSwitcher({ 
  variant = 'toggle', 
  size = 'md',
  showText = true,
  className = '' 
}: LanguageSwitcherProps) {
  const { language, setLanguage, t } = useLanguage();

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-2',
    lg: 'text-base px-4 py-3'
  };

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'es' : 'en');
  };

  if (variant === 'button') {
    return (
      <button
        onClick={toggleLanguage}
        className={`
          inline-flex items-center gap-2 rounded-lg border border-gray-300 
          bg-white hover:bg-gray-50 transition-all duration-200 font-medium
          ${sizeClasses[size]} ${className}
        `}
        title={t('language.selectLanguage')}
      >
        <span className="text-lg">
          {language === 'en' ? '🇺🇸' : '🇪🇸'}
        </span>
        {showText && (
          <span>
            {language === 'en' ? 'EN' : 'ES'}
          </span>
        )}
      </button>
    );
  }

  if (variant === 'toggle') {
    return (
      <div className={`inline-flex items-center ${className}`}>
        <div className="relative inline-flex items-center bg-gray-100 rounded-full p-1">
          <button
            onClick={() => setLanguage('en')}
            className={`
              flex items-center gap-1 px-3 py-1 rounded-full transition-all duration-200 text-sm font-medium
              ${language === 'en' 
                ? 'bg-white text-gray-900 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
              }
            `}
            title="English"
          >
            <span className="text-base">🇺🇸</span>
            {showText && <span>EN</span>}
          </button>
          <button
            onClick={() => setLanguage('es')}
            className={`
              flex items-center gap-1 px-3 py-1 rounded-full transition-all duration-200 text-sm font-medium
              ${language === 'es' 
                ? 'bg-white text-gray-900 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
              }
            `}
            title="Español"
          >
            <span className="text-base">🇪🇸</span>
            {showText && <span>ES</span>}
          </button>
        </div>
      </div>
    );
  }

  // Default dropdown variant (same as LanguageSelector)
  return (
    <div className={`relative ${className}`}>
      <button
        onClick={toggleLanguage}
        className="flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 transition-colors duration-200 group"
        title={t('language.selectLanguage')}
      >
        <span className="text-lg">
          {language === 'en' ? '🇺🇸' : '🇪🇸'}
        </span>
        {showText && (
          <span className="text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-200">
            {language === 'en' ? 'English' : 'Español'}
          </span>
        )}
      </button>
    </div>
  );
}

// Compact version for mobile/small spaces
export function CompactLanguageSwitcher({ className = '' }: { className?: string }) {
  return (
    <LanguageSwitcher 
      variant="button" 
      size="sm" 
      showText={false} 
      className={className}
    />
  );
}

// Toggle version for settings pages
export function ToggleLanguageSwitcher({ className = '' }: { className?: string }) {
  return (
    <LanguageSwitcher 
      variant="toggle" 
      size="md" 
      showText={true} 
      className={className}
    />
  );
}

// Full dropdown version (same as original LanguageSelector)
export function DropdownLanguageSwitcher({ className = '' }: { className?: string }) {
  return (
    <LanguageSwitcher 
      variant="dropdown" 
      size="md" 
      showText={true} 
      className={className}
    />
  );
}
