'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Play, Pause, X, Upload, Plus, Camera } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

import { db } from '@/lib/firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import NoSSR from './NoSSR';

// Define the User type that matches the auth context
type User = {
  uid: string;
  displayName: string | null;
  photoURL: string | null;
  email?: string | null;
  emailVerified?: boolean;
  isAnonymous?: boolean;
  metadata?: any;
  providerData?: any[];
  refreshToken?: string;
  tenantId?: string | null;
  delete?: () => Promise<void>;
  getIdToken?: (forceRefresh?: boolean) => Promise<string>;
  getIdTokenResult?: (forceRefresh?: boolean) => Promise<any>;
  reload?: () => Promise<void>;
  toJSON?: () => object;
  // Some auth providers might use 'id' instead of 'uid'
  id?: string;
};

interface Story {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  text?: string;
  image?: string;
  mediaType?: 'image' | 'video';
  mediaUrl?: string;
  timestamp: Date;
  expiresAt: Date;
  isStory: boolean;
  textColor?: string;
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  textAlign?: 'left' | 'center' | 'right' | 'justify' | 'start' | 'end';
}

interface UserStories {
  userId: string;
  userName: string;
  userAvatar: string;
  stories: Story[];
  totalStories: number;
  latestStory: Story;
}

interface StoriesSectionProps {
  onCreateStory?: () => void;
}

export default function StoriesSection({ onCreateStory }: StoriesSectionProps) {
  const { user } = useAuth() as { user: User | null };
  
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // User stories state
  const [userStories, setUserStories] = useState<UserStories[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // Modal state
  const [showModal, setShowModal] = useState<boolean>(false);
  const [selectedUserStories, setSelectedUserStories] = useState<UserStories | null>(null);
  const [currentUserIndex, setCurrentUserIndex] = useState<number>(0);
  const [currentStoryIndex, setCurrentStoryIndex] = useState<number>(0);
  const [progress, setProgress] = useState<number>(0);
  const [isPaused, setIsPaused] = useState<boolean>(false);

  // Story creation states
  const [showCreateStory, setShowCreateStory] = useState<boolean>(false);
  const [storyContent, setStoryContent] = useState<string>('');
  const [storyImage, setStoryImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Timer states
  const [autoProgressEnabled] = useState(true);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [progressTimer, setProgressTimer] = useState<NodeJS.Timeout | null>(null);
  const [showEnlargedImage, setShowEnlargedImage] = useState<boolean>(false);

  // Helper function to get time remaining
  const getTimeRemaining = (expiresAt: Date) => {
    const now = new Date();
    const timeLeft = expiresAt.getTime() - now.getTime();
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    return { hours: Math.max(0, hours), minutes: Math.max(0, minutes) };
  };

  // Navigation functions
  const goToNextStory = useCallback(() => {
    if (!selectedUserStories) return;

    if (currentStoryIndex < selectedUserStories.stories.length - 1) {
      setCurrentStoryIndex(prev => prev + 1);
      setProgress(0);
    } else if (currentUserIndex < userStories.length - 1) {
      // Move to next user
      const nextUserIndex = currentUserIndex + 1;
      setCurrentUserIndex(nextUserIndex);
      setSelectedUserStories(userStories[nextUserIndex]);
      setCurrentStoryIndex(0);
      setProgress(0);
    } else {
      // All stories finished
      closeModal();
    }
  }, [selectedUserStories, currentStoryIndex, currentUserIndex, userStories]);



  const togglePause = useCallback(() => {
    setIsPaused(prev => !prev);
  }, []);

  // Close modal and cleanup
  const closeModal = useCallback(() => {
    setShowModal(false);
    setSelectedUserStories(null);
    setCurrentStoryIndex(0);
    setCurrentUserIndex(0);
    setProgress(0);
    setIsPaused(false);
    clearTimers();
  }, []);

  // Story timer functions
  const startStoryTimer = useCallback(() => {
    if (!autoProgressEnabled || isPaused) return;
    
    const duration = 5000; // 5 seconds per story
    const interval = 50;
    const increment = (interval / duration) * 100;
    
    const timer = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + increment;
        if (newProgress >= 100) {
          goToNextStory();
          return 0;
        }
        return newProgress;
      });
    }, interval);
    
    setProgressTimer(timer);
  }, [autoProgressEnabled, isPaused, goToNextStory]);

  const clearTimers = useCallback(() => {
    if (progressTimer) {
      clearInterval(progressTimer);
      setProgressTimer(null);
    }
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  }, [progressTimer, longPressTimer]);



  // Handle story click
  const handleStoryClick = (userStory: UserStories) => {
    const userIndex = userStories.findIndex(us => us.userId === userStory.userId);
    setCurrentUserIndex(userIndex);
    setSelectedUserStories(userStory);
    setCurrentStoryIndex(0);
    setProgress(0);
    setIsPaused(false);
    setShowModal(true);
  };

  // Handle image upload
  const handleImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      setStoryImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  // Handle story creation
  const handleCreateStory = useCallback(async () => {
    if (!user) return;
    
    if (!storyContent.trim() && !storyImage) {
      toast.error('Please add some content to your story');
      return;
    }

    setIsSubmitting(true);
    try {
      const storyData = {
        userId: user.uid,
        userName: user.displayName || 'Anonymous',
        userAvatar: user.photoURL || '/favicon.png',
        content: storyContent,
        image: storyImage,
        timestamp: serverTimestamp(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        isStory: true,
        isPublic: true // Required by Firestore rules
      };

      await addDoc(collection(db, 'stories'), storyData);
      
      setShowCreateStory(false);
      setStoryContent('');
      setStoryImage(null);
      toast.success('Story posted successfully!');
      
      if (onCreateStory) {
        onCreateStory();
      }
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to post story');
    } finally {
      setIsSubmitting(false);
    }
  }, [user, storyContent, storyImage, onCreateStory]);

  // Auto-advance effect
  useEffect(() => {
    if (showModal && selectedUserStories && !isPaused) {
      startStoryTimer();
    } else {
      clearTimers();
    }

    return () => clearTimers();
  }, [showModal, selectedUserStories, isPaused, startStoryTimer, clearTimers]);

  // Video pause/resume effect
  useEffect(() => {
    if (videoRef.current) {
      if (isPaused) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
    }
  }, [isPaused]);

  // Mock data for now
  useEffect(() => {
    setLoading(false);
    setUserStories([]);
  }, []);

  if (loading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 bg-gray-200 rounded-2xl animate-pulse"></div>
          <div className="w-20 h-20 bg-gray-200 rounded-2xl animate-pulse"></div>
          <div className="w-20 h-20 bg-gray-200 rounded-2xl animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Stories</h3>
      </div>

      {/* Stories Container */}
      <div className="flex items-center space-x-4 overflow-x-auto pb-2 scrollbar-hide">
        {/* Add Story Button */}
        {user && (
          <div className="flex-shrink-0 text-center">
            <button
              onClick={() => setShowCreateStory(true)}
              className="relative w-20 h-20 rounded-2xl bg-gradient-to-br from-green-50 to-blue-50 border-2 border-dashed border-green-300 hover:border-green-500 hover:from-green-100 hover:to-blue-100 transition-all duration-300 flex items-center justify-center group"
            >
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-600 to-blue-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Plus className="w-6 h-6 text-white" />
              </div>
            </button>
            <p className="text-xs text-gray-600 mt-2 font-medium">Add Story</p>
          </div>
        )}

        {/* Stories */}
        {userStories.map((userStory) => (
          <div key={userStory.userId} className="flex-shrink-0 text-center">
            <button
              className="relative group"
              onClick={() => handleStoryClick(userStory)}
            >
              {/* Story Container */}
              <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-green-500 via-blue-500 to-green-600 p-0.5 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="w-full h-full rounded-2xl bg-white p-0.5">
                  <img
                    src={userStory.latestStory.userAvatar || '/favicon.png'}
                    alt={userStory.latestStory.userName}
                    className="w-full h-full rounded-xl object-cover"
                  />
                </div>
              </div>

              {/* Multiple Stories Indicator */}
              {userStory.totalStories > 1 && (
                <div className="absolute -top-2 -right-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg border-2 border-white">
                  {userStory.totalStories}
                </div>
              )}

              {/* Time Remaining */}
              <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs px-2 py-1 rounded-full shadow-lg border-2 border-white font-medium">
                <NoSSR fallback="24h">
                  {getTimeRemaining(userStory.latestStory.expiresAt).hours}h {getTimeRemaining(userStory.latestStory.expiresAt).minutes}m
                </NoSSR>
              </div>
            </button>

            <p className="text-xs text-gray-700 mt-2 font-medium truncate w-20">
              {userStory.userId === user?.uid ? 'You' : userStory.latestStory.userName}
            </p>
          </div>
        ))}

        {/* No Stories Message */}
        {userStories.length === 0 && !user && (
          <div className="flex-1 text-center py-12">
            <div className="text-gray-500">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-lg font-semibold text-gray-700 mb-2">No Stories Yet</p>
              <p className="text-sm text-gray-500">Stories from your community will appear here</p>
            </div>
          </div>
        )}


      </div>

      {/* Story Viewing Modal */}
      {showModal && selectedUserStories && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm" onClick={closeModal} />

          {/* Modal Container */}
          <div className="relative w-full max-w-sm mx-auto">
            {/* Progress Bars */}
            <div className="absolute top-6 left-6 right-6 z-30 flex space-x-2">
              {selectedUserStories.stories.map((_, index: number) => (
                <div key={index} className="flex-1 h-1 bg-white bg-opacity-30 rounded-full overflow-hidden">
                  <div
                    className={`h-full bg-white transition-all duration-300 ${
                      index < currentStoryIndex ? 'w-full' :
                      index === currentStoryIndex ? 'progress-bar-anim' : 'w-0'
                    }`}
                    style={index === currentStoryIndex ? {
                      width: `${progress}%`,
                      transition: isPaused ? 'none' : 'width 50ms linear',
                    } : index < currentStoryIndex ? { width: '100%' } : { width: '0%' }}
                  />
                </div>
              ))}
            </div>

            {/* Story Controls */}
            <div className="absolute top-6 right-6 z-30 flex space-x-3">
              {/* Pause/Play Button */}
              <button
                onClick={togglePause}
                className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-opacity-80 transition-all duration-200 border border-white border-opacity-20"
              >
                {isPaused ? <Play className="w-5 h-5 ml-0.5" /> : <Pause className="w-5 h-5" />}
              </button>

              {/* Close Button */}
              <button
                onClick={closeModal}
                className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-opacity-80 transition-all duration-200 border border-white border-opacity-20"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Story Content */}
            <div className={`bg-white rounded-3xl overflow-hidden shadow-2xl border border-gray-200 relative transition-all duration-300 ${
              isPaused ? 'ring-4 ring-blue-500 ring-opacity-50' : ''
            }`}>
              {/* User Header */}
              <div className="flex items-center p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
                <img
                  src={selectedUserStories.stories[currentStoryIndex].userAvatar || '/favicon.png'}
                  alt={selectedUserStories.stories[currentStoryIndex].userName}
                  className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                />
                <div className="flex-1 ml-4">
                  <p className="font-semibold text-gray-800">{selectedUserStories.stories[currentStoryIndex].userName}</p>
                  <p className="text-sm text-gray-500">
                    <NoSSR fallback="24h remaining">
                      {getTimeRemaining(selectedUserStories.stories[currentStoryIndex].expiresAt).hours}h {getTimeRemaining(selectedUserStories.stories[currentStoryIndex].expiresAt).minutes}m remaining
                    </NoSSR>
                  </p>
                </div>
                <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border border-gray-200">
                  {currentStoryIndex + 1} / {selectedUserStories.totalStories}
                </div>
              </div>

              {/* Story Image */}
              {selectedUserStories.stories[currentStoryIndex].image && (
                <div className="relative bg-gray-50">
                  <img
                    src={selectedUserStories.stories[currentStoryIndex].image}
                    alt={`${selectedUserStories.stories[currentStoryIndex].userName}'s story`}
                    className="w-full max-h-96 object-contain cursor-pointer transition-transform duration-200 hover:scale-105"
                    onClick={() => setShowEnlargedImage(true)}
                  />
                  {showEnlargedImage && (
                    <div
                      className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-90 backdrop-blur-sm p-4"
                      onClick={() => setShowEnlargedImage(false)}
                    >
                      <img
                        src={selectedUserStories.stories[currentStoryIndex].image}
                        alt={`${selectedUserStories.stories[currentStoryIndex].userName}'s story enlarged`}
                        className="max-w-full max-h-full rounded-2xl shadow-2xl"
                      />
                      <button
                        className="absolute top-8 right-8 text-white bg-black bg-opacity-60 backdrop-blur-sm rounded-full p-3 hover:bg-opacity-80 transition-all duration-200"
                        onClick={e => { e.stopPropagation(); setShowEnlargedImage(false); }}
                      >
                        <X className="w-6 h-6" />
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Story Text */}
              {selectedUserStories.stories[currentStoryIndex].content && (
                <div className="p-6">
                  <p className="text-gray-800 text-center leading-relaxed">{selectedUserStories.stories[currentStoryIndex].content}</p>
                </div>
              )}

              {/* Pause Overlay */}
              {isPaused && (
                <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center rounded-3xl">
                  <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl p-6 flex flex-col items-center space-y-3 shadow-xl border border-gray-200">
                    <div className="flex items-center space-x-3">
                      <Pause className="w-7 h-7 text-gray-700" />
                      <span className="text-gray-700 font-semibold text-lg">Paused</span>
                    </div>
                    <div className="text-center text-sm text-gray-600">
                      <p>Tap to resume • Swipe to navigate</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create Story Modal */}
      {showCreateStory && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={() => {
              setShowCreateStory(false);
              setStoryContent('');
              setStoryImage(null);
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-gradient-to-br from-green-50 to-blue-50 rounded-3xl p-8 w-full max-w-md mx-auto shadow-2xl border border-white/20 backdrop-blur-sm">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-1">Create Story</h3>
                <p className="text-gray-600 text-sm">Share a moment with your community</p>
              </div>
              <button
                onClick={() => {
                  setShowCreateStory(false);
                  setStoryContent('');
                  setStoryImage(null);
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-white/50 rounded-xl"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Form Content */}
            <div className="space-y-6">
              {/* Image Upload */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                  <Camera className="w-4 h-4 text-blue-600" />
                  <span>Add Photo (Optional)</span>
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-2xl p-6 text-center bg-gray-50 hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 group">
                  {storyImage ? (
                    <div className="relative">
                      <img
                        src={storyImage}
                        alt="Story preview"
                        className="w-full h-48 object-cover rounded-xl shadow-sm border border-gray-200"
                      />
                      <button
                        onClick={() => setStoryImage(null)}
                        className="absolute top-3 right-3 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm hover:bg-red-600 transition-colors shadow-lg"
                      >
                        ×
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-105 transition-transform duration-300">
                        <Camera className="w-8 h-8 text-white" />
                      </div>
                      <p className="text-gray-700 mb-4 font-medium">Add a photo to your story</p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="story-image-upload"
                        ref={fileInputRef}
                      />
                      <label
                        htmlFor="story-image-upload"
                        className="inline-block bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-indigo-700 cursor-pointer transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-semibold"
                      >
                        Choose Photo
                      </label>
                    </>
                  )}
                </div>
              </div>

              {/* Text Input */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                  <span className="text-lg">✍️</span>
                  <span>Add Text (Optional)</span>
                </label>
                <textarea
                  value={storyContent}
                  onChange={(e) => setStoryContent(e.target.value)}
                  placeholder="What's happening with your pets today? 🐕🐱"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none bg-white transition-all duration-300 placeholder-gray-500 text-gray-800 shadow-sm hover:border-gray-400"
                  rows={4}
                  maxLength={200}
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-xs text-gray-500">
                    {storyContent.length}/200 characters
                  </p>
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <span>✨</span>
                    <span>Express yourself</span>
                  </div>
                </div>
              </div>

              {/* Info Box */}
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-2xl">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full animate-pulse"></div>
                  <p className="text-blue-800 font-semibold text-sm">
                    Your story will be visible for 24 hours
                  </p>
                </div>
                <p className="text-blue-700 mt-1 ml-6 text-xs">
                  Share moments that matter with your pet community
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-2">
                <button
                  onClick={() => {
                    setShowCreateStory(false);
                    setStoryContent('');
                    setStoryImage(null);
                  }}
                  className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 font-semibold"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateStory}
                  disabled={isSubmitting || (!storyContent.trim() && !storyImage)}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Sharing...</span>
                    </>
                  ) : (
                    <>
                      <Upload className="w-5 h-5" />
                      <span>Share Story</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
