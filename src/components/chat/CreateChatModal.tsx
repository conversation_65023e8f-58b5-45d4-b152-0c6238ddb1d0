'use client';

import React, { useState, useEffect } from 'react';
import { X, Search, Users, MessageCircle, Check } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { useChatStore } from '@/stores/chatStore';
import { ChatUser } from '@/types/chat';

interface CreateChatModalProps {
  onClose: () => void;
  onChatCreated: (chatId: string) => void;
}

export default function CreateChatModal({ onClose, onChatCreated }: CreateChatModalProps) {
  const { user } = useAuth();
  const { users } = useData();
  const { createChat } = useChatStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<ChatUser[]>([]);
  const [isGroupChat, setIsGroupChat] = useState(false);
  const [groupName, setGroupName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Filter available users
  const userId = user?.uid || user?.id;
  const availableUsers = users
    .filter(u => u.id !== userId) // Exclude current user
    .filter(u => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        u.name?.toLowerCase().includes(query) ||
        u.email?.toLowerCase().includes(query) ||
        u.role?.toLowerCase().includes(query)
      );
    })
    .map(u => ({
      uid: u.id,
      displayName: u.name || 'Unknown User',
      userType: u.role as 'pet_owner' | 'provider' | 'support' | 'admin',
      avatarUrl: u.avatar,
      email: u.email,
      isOnline: u.isOnline || false
    }));

  // Debug logging
  console.log('Available users:', availableUsers.length, availableUsers);

  // Handle user selection
  const handleUserSelect = (selectedUser: ChatUser) => {
    if (selectedUsers.find(u => u.uid === selectedUser.uid)) {
      setSelectedUsers(selectedUsers.filter(u => u.uid !== selectedUser.uid));
    } else {
      setSelectedUsers([...selectedUsers, selectedUser]);
    }
  };

  // Handle create chat
  const handleCreateChat = async () => {
    console.log('handleCreateChat called', { selectedUsers, user });
    if (selectedUsers.length === 0) {
      console.log('No users selected');
      return;
    }

    // Check for both uid and id properties
    const userId = user?.uid || user?.id;
    if (!userId) {
      console.log('No user UID or ID', { uid: user?.uid, id: user?.id });
      return;
    }

    setIsLoading(true);
    try {
      const participantIds = [userId, ...selectedUsers.map(u => u.uid)];
      console.log('Creating chat with participants:', participantIds);

      const isGroup = selectedUsers.length > 1;
      const chatRequest: any = {
        participantIds,
        isGroupChat: isGroup
      };

      // Only add chatName if it's a group chat and has a name
      if (isGroup && groupName?.trim()) {
        chatRequest.chatName = groupName.trim();
      }

      // Only add initialMessage if it's a group chat
      if (isGroup) {
        chatRequest.initialMessage = `${user.name || user.displayName || 'Someone'} created this group chat`;
      }

      const chatId = await createChat(chatRequest);

      console.log('Chat created with ID:', chatId);
      onChatCreated(chatId);
    } catch (error) {
      console.error('Error creating chat:', error);
      alert('Error creating chat: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-enable group chat mode when multiple users selected
  useEffect(() => {
    setIsGroupChat(selectedUsers.length > 1);
  }, [selectedUsers.length]);

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">New Chat</h2>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-100 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Selected Users */}
        {selectedUsers.length > 0 && (
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-2 mb-3">
              <Users className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">
                Selected ({selectedUsers.length})
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map((selectedUser) => (
                <div
                  key={selectedUser.uid}
                  className="flex items-center space-x-2 px-3 py-2 bg-green-100 rounded-full"
                >
                  <img
                    src={selectedUser.avatarUrl || '/default-avatar.png'}
                    alt={selectedUser.displayName}
                    className="w-6 h-6 rounded-full object-cover"
                  />
                  <span className="text-sm font-medium text-green-800">
                    {selectedUser.displayName}
                  </span>
                  <button
                    onClick={() => handleUserSelect(selectedUser)}
                    className="w-4 h-4 flex items-center justify-center rounded-full hover:bg-green-200 transition-colors"
                  >
                    <X className="w-3 h-3 text-green-600" />
                  </button>
                </div>
              ))}
            </div>

            {/* Group Name Input */}
            {isGroupChat && (
              <div className="mt-4">
                <input
                  type="text"
                  placeholder="Group name (optional)"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-100 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            )}
          </div>
        )}

        {/* User List */}
        <div className="max-h-80 overflow-y-auto">
          {availableUsers.length === 0 ? (
            <div className="p-8 text-center">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                {searchQuery ? 'No users found' : 'No users available'}
              </h3>
              <p className="text-gray-500 mb-4">
                {searchQuery
                  ? 'Try adjusting your search terms'
                  : 'There are no other users to chat with right now'
                }
              </p>
              {!searchQuery && (
                <div className="text-xs text-gray-400">
                  <p>Debug: Total users loaded: {users.length}</p>
                  <p>Current user ID: {user?.uid || user?.id}</p>
                  <p>Available users: {availableUsers.length}</p>
                </div>
              )}
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {availableUsers.map((availableUser) => {
                const isSelected = selectedUsers.find(u => u.uid === availableUser.uid);
                
                return (
                  <button
                    key={availableUser.uid}
                    onClick={() => handleUserSelect(availableUser)}
                    className={`w-full p-4 flex items-center space-x-3 hover:bg-gray-50 transition-colors ${
                      isSelected ? 'bg-green-50' : ''
                    }`}
                  >
                    <div className="relative">
                      <img
                        src={availableUser.avatarUrl || '/default-avatar.png'}
                        alt={availableUser.displayName}
                        className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                        onError={(e) => {
                          e.currentTarget.src = '/default-avatar.png';
                        }}
                      />
                      {availableUser.isOnline && (
                        <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                    </div>
                    
                    <div className="flex-1 text-left">
                      <h3 className="font-semibold text-gray-900">
                        {availableUser.displayName}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {availableUser.userType === 'provider' ? 'Service Provider' : 'Pet Owner'}
                      </p>
                      {availableUser.email && (
                        <p className="text-xs text-gray-400">{availableUser.email}</p>
                      )}
                    </div>

                    {isSelected && (
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <Check className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {selectedUsers.length === 0 
                ? 'Select users to start chatting'
                : `${selectedUsers.length} user${selectedUsers.length > 1 ? 's' : ''} selected`
              }
            </div>
            <button
              onClick={handleCreateChat}
              disabled={selectedUsers.length === 0 || isLoading}
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                selectedUsers.length > 0 && !isLoading
                  ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white hover:from-green-600 hover:to-blue-600 shadow-lg'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Creating...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <MessageCircle className="w-4 h-4" />
                  <span>Start Chat</span>
                </div>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
