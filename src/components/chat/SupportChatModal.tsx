'use client';

import React, { useState } from 'react';
import { X, HeadphonesIcon, MessageCircle, AlertCircle, HelpCircle, CreditCard, Bug } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChatStore } from '@/stores/chatStore';

interface SupportChatModalProps {
  onClose: () => void;
  onChatCreated: (chatId: string) => void;
}

const SUPPORT_CATEGORIES = [
  {
    id: 'technical',
    name: 'Technical Issue',
    icon: Bug,
    description: 'App bugs, login problems, or technical difficulties',
    color: 'text-red-600 bg-red-100'
  },
  {
    id: 'billing',
    name: 'Billing & Payments',
    icon: CreditCard,
    description: 'Payment issues, subscription questions, or billing disputes',
    color: 'text-blue-600 bg-blue-100'
  },
  {
    id: 'general',
    name: 'General Support',
    icon: HelpCircle,
    description: 'General questions about using Fetchly',
    color: 'text-green-600 bg-green-100'
  },
  {
    id: 'complaint',
    name: 'Report Issue',
    icon: AlertCircle,
    description: 'Report inappropriate behavior or content',
    color: 'text-orange-600 bg-orange-100'
  }
];

export default function SupportChatModal({ onClose, onChatCreated }: SupportChatModalProps) {
  const { user } = useAuth();
  const { createSupportChat } = useChatStore();
  
  const [selectedCategory, setSelectedCategory] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Handle create support chat
  const handleCreateSupportChat = async () => {
    if (!subject.trim() || !message.trim() || !user?.uid) return;

    setIsLoading(true);
    try {
      const categoryName = SUPPORT_CATEGORIES.find(c => c.id === selectedCategory)?.name || 'General';
      const fullSubject = `[${categoryName}] ${subject}`;
      
      const chatId = await createSupportChat(user.uid, fullSubject, message);
      onChatCreated(chatId);
    } catch (error) {
      console.error('Error creating support chat:', error);
      // You could show a toast notification here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <HeadphonesIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">Contact Support</h2>
              <p className="text-xs text-gray-600">We're here to help 24/7</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-white/50 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4 space-y-4">
          {/* Category Selection */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              What can we help you with?
            </label>
            <div className="grid grid-cols-2 gap-2">
              {SUPPORT_CATEGORIES.map((category) => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`p-3 rounded-lg border-2 transition-all duration-300 text-left ${
                      selectedCategory === category.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex flex-col items-center text-center space-y-1">
                      <div className={`w-6 h-6 rounded-lg flex items-center justify-center ${category.color}`}>
                        <Icon className="w-3 h-3" />
                      </div>
                      <div>
                        <h3 className="text-xs font-semibold text-gray-900">{category.name}</h3>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Subject */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Subject
            </label>
            <input
              type="text"
              placeholder="Brief description..."
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              className="w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-1">
              Describe your issue
            </label>
            <textarea
              placeholder="Please provide details to help us assist you..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 bg-gray-100 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
            />
          </div>

          {/* Support Info */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <HeadphonesIcon className="w-3 h-3 text-white" />
              </div>
              <div>
                <h4 className="text-sm font-semibold text-gray-800">24/7 Support Available</h4>
                <p className="text-xs text-gray-600">
                  We typically respond within 15 minutes
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <button
            onClick={handleCreateSupportChat}
            disabled={!selectedCategory || !subject.trim() || !message.trim() || isLoading}
            className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 ${
              selectedCategory && subject.trim() && message.trim() && !isLoading
                ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 shadow-lg'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Creating...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <MessageCircle className="w-4 h-4" />
                <span>Start Support Chat</span>
              </div>
            )}
          </button>
          <p className="text-xs text-gray-500 text-center mt-2">
            {!selectedCategory || !subject.trim() || !message.trim()
              ? 'Please fill in all fields'
              : 'Ready to contact support'
            }
          </p>
        </div>
      </div>
    </div>
  );
}
