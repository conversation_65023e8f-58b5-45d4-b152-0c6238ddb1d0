'use client';

import React, { useState, useRef, useEffect } from 'react';
import { 
  ArrowLeft, 
  Phone, 
  Video, 
  MoreVertical, 
  Send, 
  Paperclip, 
  Smile,
  Image as ImageIcon,
  File,
  Mic,
  X
} from 'lucide-react';
import { Chat, Message } from '@/types/chat';
import { useAuth } from '@/contexts/AuthContext';
import { useChatStore, useChatMessages, useIsTyping } from '@/stores/chatStore';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import { formatDistanceToNow } from 'date-fns';

interface ChatWindowProps {
  chat: Chat;
  onBack?: () => void;
}

export default function ChatWindow({ chat, onBack }: ChatWindowProps) {
  const { user } = useAuth();
  const { sendMessage, markChatAsRead, setTyping } = useChatStore();
  const messages = useChatMessages(chat.id);
  const typingUsers = useIsTyping(chat.id);
  
  const [messageText, setMessageText] = useState('');
  const [isTyping, setIsTypingState] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Get chat display info
  const getChatDisplayInfo = () => {
    if (chat.isGroupChat) {
      return {
        name: chat.chatName || 'Group Chat',
        avatar: chat.chatAvatar || '/default-group.png',
        subtitle: `${chat.participants.length} members`,
        isOnline: false
      };
    }

    if (chat.isSupportChat) {
      return {
        name: 'Fetchly Support',
        avatar: '/fetchlylogo.png',
        subtitle: 'Support Team • Always available',
        isOnline: true
      };
    }

    // One-on-one chat
    const otherParticipant = chat.participantDetails.find(p => p.uid !== user?.uid);
    return {
      name: otherParticipant?.displayName || 'Unknown User',
      avatar: otherParticipant?.avatarUrl || '/default-avatar.png',
      subtitle: otherParticipant?.userType === 'provider' ? 'Service Provider' : 'Pet Owner',
      isOnline: otherParticipant?.isOnline || false
    };
  };

  const displayInfo = getChatDisplayInfo();

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Mark messages as read when chat opens
  useEffect(() => {
    if (user?.uid && messages.length > 0) {
      markChatAsRead(chat.id, user.uid);
    }
  }, [chat.id, user?.uid, messages.length, markChatAsRead]);

  // Handle typing indicator
  const handleTyping = (text: string) => {
    setMessageText(text);
    
    if (!isTyping && text.length > 0) {
      setIsTypingState(true);
      setTyping(chat.id, user?.uid || '', true);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTypingState(false);
      setTyping(chat.id, user?.uid || '', false);
    }, 2000);
  };

  // Handle send message
  const handleSendMessage = async () => {
    if (!messageText.trim() && !selectedFile) return;
    if (!user?.uid) return;

    try {
      await sendMessage({
        chatId: chat.id,
        text: messageText.trim(),
        mediaFile: selectedFile || undefined
      }, user.uid);

      setMessageText('');
      setSelectedFile(null);
      setIsTypingState(false);
      setTyping(chat.id, user.uid, false);
    } catch (error) {
      console.error('Error sending message:', error);
      // You could show a toast notification here
    }
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  // Handle key press
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="h-full flex flex-col bg-white/90 backdrop-blur-sm">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 glass-card">
        <div className="flex items-center space-x-3">
          {onBack && (
            <button
              onClick={onBack}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors md:hidden"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
          )}
          
          <div className="relative">
            <img
              src={displayInfo.avatar}
              alt={displayInfo.name}
              className="w-10 h-10 rounded-full object-cover border-2 border-white shadow-sm"
              onError={(e) => {
                e.currentTarget.src = '/default-avatar.png';
              }}
            />
            {displayInfo.isOnline && (
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
            )}
          </div>
          
          <div className="min-w-0 flex-1">
            <h2 className="font-semibold text-gray-900 truncate">{displayInfo.name}</h2>
            <p className="text-sm text-gray-500 truncate">
              {typingUsers.length > 0 && typingUsers.some(uid => uid !== user?.uid) ? (
                <span className="text-green-600 italic">Typing...</span>
              ) : (
                displayInfo.subtitle
              )}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
            <Phone className="w-4 h-4 text-gray-600" />
          </button>
          <button className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
            <Video className="w-4 h-4 text-gray-600" />
          </button>
          <button className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
            <MoreVertical className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-sm mx-auto">
              <div className="w-16 h-16 bg-gradient-to-r from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <img
                  src={displayInfo.avatar}
                  alt={displayInfo.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
              </div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">
                Start the conversation
              </h3>
              <p className="text-gray-500 text-sm">
                Say hello to {displayInfo.name} and start chatting!
              </p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => (
              <MessageBubble
                key={message.id}
                message={message}
                isOwn={message.senderId === user?.uid}
                showAvatar={
                  index === 0 || 
                  messages[index - 1].senderId !== message.senderId ||
                  (message.timestamp.getTime() - messages[index - 1].timestamp.getTime()) > 300000 // 5 minutes
                }
              />
            ))}
            
            {/* Typing Indicator */}
            {typingUsers.length > 0 && typingUsers.some(uid => uid !== user?.uid) && (
              <TypingIndicator 
                users={typingUsers
                  .filter(uid => uid !== user?.uid)
                  .map(uid => chat.participantDetails.find(p => p.uid === uid)?.displayName || 'Someone')
                }
              />
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* File Preview */}
      {selectedFile && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
            <div className="flex items-center space-x-3">
              {selectedFile.type.startsWith('image/') ? (
                <ImageIcon className="w-8 h-8 text-blue-500" />
              ) : (
                <File className="w-8 h-8 text-gray-500" />
              )}
              <div>
                <p className="font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
            <button
              onClick={() => setSelectedFile(null)}
              className="w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 glass-card">
        <div className="flex items-end space-x-3">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
            >
              <Paperclip className="w-4 h-4 text-gray-600" />
            </button>
            <button
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
            >
              <Smile className="w-4 h-4 text-gray-600" />
            </button>
          </div>

          <div className="flex-1 relative">
            <textarea
              value={messageText}
              onChange={(e) => handleTyping(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-4 py-3 bg-gray-100 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none max-h-32"
              rows={1}
              style={{ minHeight: '48px' }}
            />
          </div>

          <button
            onClick={handleSendMessage}
            disabled={!messageText.trim() && !selectedFile}
            className={`w-10 h-10 flex items-center justify-center rounded-full transition-all duration-300 ${
              messageText.trim() || selectedFile
                ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white hover:from-green-600 hover:to-blue-600 shadow-lg'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
          >
            <Send className="w-4 h-4" />
          </button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*,video/*,.pdf,.doc,.docx"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>
    </div>
  );
}
