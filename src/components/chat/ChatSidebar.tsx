'use client';

import React, { useState } from 'react';
import { 
  Search, 
  Plus, 
  MessageCircle, 
  Users, 
  HeadphonesIcon,
  ArrowLeft,
  MoreVertical,
  Pin,
  Archive,
  Trash2
} from 'lucide-react';
import { Chat } from '@/types/chat';
import { useAuth } from '@/contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';

interface ChatSidebarProps {
  chats: Chat[];
  activeChat: Chat | null;
  isLoading: boolean;
  onChatSelect: (chat: Chat) => void;
  onCreateChat: () => void;
  onSupportChat: () => void;
  onBack?: () => void;
}

export default function ChatSidebar({
  chats,
  activeChat,
  isLoading,
  onChatSelect,
  onCreateChat,
  onSupportChat,
  onBack
}: ChatSidebarProps) {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [showMenu, setShowMenu] = useState<string | null>(null);

  // Filter chats based on search query
  const filteredChats = chats.filter(chat => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    return (
      chat.chatName?.toLowerCase().includes(query) ||
      chat.participantDetails.some(p => 
        p.displayName.toLowerCase().includes(query) ||
        p.email?.toLowerCase().includes(query)
      ) ||
      chat.lastMessage.toLowerCase().includes(query)
    );
  });

  // Get chat display info
  const getChatDisplayInfo = (chat: Chat) => {
    if (chat.isGroupChat) {
      return {
        name: chat.chatName || 'Group Chat',
        avatar: chat.chatAvatar || '/default-group.png',
        subtitle: `${chat.participants.length} members`
      };
    }

    if (chat.isSupportChat) {
      return {
        name: 'Fetchly Support',
        avatar: '/fetchlylogo.png',
        subtitle: 'Support Team'
      };
    }

    // One-on-one chat
    const otherParticipant = chat.participantDetails.find(p => p.uid !== user?.uid);
    return {
      name: otherParticipant?.displayName || 'Unknown User',
      avatar: otherParticipant?.avatarUrl || '/default-avatar.png',
      subtitle: otherParticipant?.userType === 'provider' ? 'Service Provider' : 'Pet Owner'
    };
  };

  // Get unread count for chat
  const getUnreadCount = (chat: Chat) => {
    return chat.unreadCount[user?.uid || ''] || 0;
  };

  // Format last message time
  const formatLastMessageTime = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <div className="h-full flex flex-col bg-white/90 backdrop-blur-sm">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          {onBack && (
            <button
              onClick={onBack}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
          )}
          <h1 className="text-xl font-bold text-gray-800">Messages</h1>
          <div className="flex items-center space-x-2">
            <button
              onClick={onCreateChat}
              className="w-8 h-8 flex items-center justify-center rounded-full bg-green-500 text-white hover:bg-green-600 transition-colors"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-100 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-gray-200">
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={onCreateChat}
            className="flex items-center justify-center gap-2 p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl hover:from-green-100 hover:to-blue-100 transition-colors"
          >
            <Users className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-gray-700">New Chat</span>
          </button>
          <button
            onClick={onSupportChat}
            className="flex items-center justify-center gap-2 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl hover:from-blue-100 hover:to-purple-100 transition-colors"
          >
            <HeadphonesIcon className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">Support</span>
          </button>
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredChats.length === 0 ? (
          <div className="p-8 text-center">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">
              {searchQuery ? 'No chats found' : 'No conversations yet'}
            </h3>
            <p className="text-gray-500 mb-4">
              {searchQuery 
                ? 'Try adjusting your search terms'
                : 'Start a conversation with other pet owners or service providers'
              }
            </p>
            {!searchQuery && (
              <button
                onClick={onCreateChat}
                className="px-4 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-300"
              >
                Start Your First Chat
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredChats.map((chat) => {
              const displayInfo = getChatDisplayInfo(chat);
              const unreadCount = getUnreadCount(chat);
              const isActive = activeChat?.id === chat.id;

              return (
                <div
                  key={chat.id}
                  className={`relative p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                    isActive ? 'bg-green-50 border-r-2 border-green-500' : ''
                  }`}
                  onClick={() => onChatSelect(chat)}
                >
                  <div className="flex items-center space-x-3">
                    {/* Avatar */}
                    <div className="relative">
                      <img
                        src={displayInfo.avatar}
                        alt={displayInfo.name}
                        className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                        onError={(e) => {
                          e.currentTarget.src = '/default-avatar.png';
                        }}
                      />
                      {/* Online indicator */}
                      {chat.participantDetails.some(p => p.isOnline && p.uid !== user?.uid) && (
                        <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                    </div>

                    {/* Chat Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className={`font-semibold truncate ${
                          unreadCount > 0 ? 'text-gray-900' : 'text-gray-700'
                        }`}>
                          {displayInfo.name}
                        </h3>
                        <div className="flex items-center space-x-2">
                          {chat.lastMessageTime && (
                            <span className="text-xs text-gray-500">
                              {formatLastMessageTime(chat.lastMessageTime)}
                            </span>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowMenu(showMenu === chat.id ? null : chat.id);
                            }}
                            className="w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-200 transition-colors"
                          >
                            <MoreVertical className="w-4 h-4 text-gray-400" />
                          </button>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <p className={`text-sm truncate ${
                          unreadCount > 0 ? 'text-gray-900 font-medium' : 'text-gray-500'
                        }`}>
                          {chat.typingUsers.length > 0 && chat.typingUsers.some(uid => uid !== user?.uid) ? (
                            <span className="text-green-600 italic">Typing...</span>
                          ) : (
                            chat.lastMessage || 'No messages yet'
                          )}
                        </p>
                        {unreadCount > 0 && (
                          <div className="w-5 h-5 bg-green-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                            {unreadCount > 99 ? '99+' : unreadCount}
                          </div>
                        )}
                      </div>
                      
                      <p className="text-xs text-gray-400 mt-1">{displayInfo.subtitle}</p>
                    </div>
                  </div>

                  {/* Context Menu */}
                  {showMenu === chat.id && (
                    <div className="absolute right-4 top-16 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                      <button className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                        <Pin className="w-4 h-4" />
                        Pin Chat
                      </button>
                      <button className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                        <Archive className="w-4 h-4" />
                        Archive
                      </button>
                      <button className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2">
                        <Trash2 className="w-4 h-4" />
                        Delete Chat
                      </button>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
