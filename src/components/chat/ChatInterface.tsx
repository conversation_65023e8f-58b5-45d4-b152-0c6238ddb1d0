'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useChatStore } from '@/stores/chatStore';
import type { Chat } from '@/types/chat';
import { Button } from '@/components/ui/button';
import { Menu, MessageSquare, Plus, Loader2 } from 'lucide-react';

// Components
import { ChatSidebar } from './ChatSidebar';
import { ChatWindow } from './ChatWindow';
import { CreateChatModal } from './CreateChatModal';
import { SupportChatModal } from './SupportChatModal';

// Types
type ChatWithId = Chat & { id: string };

interface ChatInterfaceProps {
  className?: string;
  initialChatId?: string;
}

// Main chat interface component
export function ChatInterface() {
  const { user, isLoading: isAuthLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialChatId = searchParams.get('chatId') || undefined;
  
  // Get chat store state and actions
  const {
    chats = [],
    activeChat,
    isLoading: isChatLoading,
    loadUserChats,
    setActiveChat,
    getChatById,
    cleanup,
    createChat
  } = useChatStore();
  
  // Local state for UI
  const [isMobileView, setIsMobileView] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showCreateChat, setShowCreateChat] = useState(false);
  const [showSupportChat, setShowSupportChat] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Memoize the active chat ID to prevent unnecessary re-renders
  const activeChatId = useMemo(() => activeChat?.id, [activeChat]);
  
  // Handle window resize for mobile view
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      setIsMobileView(isMobile);
      if (!isMobile) {
        setShowSidebar(true);
      }
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthLoading && !user) {
      router.push('/login');
    }
  }, [user, isAuthLoading, router]);
  
  // Load user chats when component mounts or userId changes
  useEffect(() => {
    if (!user?.id) return;
    
    loadUserChats(user.id);
    
    // Request notification permission if the browser supports it
    if ('Notification' in window) {
      Notification.requestPermission()
        .then(permission => console.log('Notification permission:', permission))
        .catch(error => console.error('Error requesting notification permission:', error));
    }
    
    return cleanup;
  }, [user?.id, loadUserChats, cleanup]);
  
  // Set initial chat if initialChatId is provided
  useEffect(() => {
    if (!initialChatId || !user?.id) {
      setIsInitialized(true);
      return;
    }
    
    const setInitialChat = async () => {
      try {
        const chat = await getChatById(initialChatId);
        if (chat) {
          setActiveChat(chat);
          if (isMobileView) {
            setShowSidebar(false);
          }
        }
      } catch (error) {
        console.error('Error loading initial chat:', error);
      } finally {
        setIsInitialized(true);
      }
    };
    
    setInitialChat();
  }, [initialChatId, getChatById, setActiveChat, isMobileView, user?.id]);
  
  // Show loading state while initializing
  if (isAuthLoading || !isInitialized) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // Helper function to safely set active chat
  const handleSetActiveChat = useCallback((chat: ChatWithId | null) => {
    setActiveChat(chat);
    if (chat && isMobileView) {
      setShowSidebar(false);
    }
  }, [isMobileView, setActiveChat]);
  
  // Handle chat creation
  const handleCreateChat = useCallback(async (chatId: string) => {
    try {
      const chat = await getChatById(chatId);
      if (chat) {
        handleSetActiveChat(chat);
      }
    } catch (error) {
      console.error('Error loading created chat:', error);
    } finally {
      setShowCreateChat(false);
    }
  }, [getChatById, handleSetActiveChat]);
  
  // Handle support chat creation
  const handleSupportChatCreated = useCallback(async (chatId: string) => {
    try {
      const chat = await getChatById(chatId);
      if (chat) {
        handleSetActiveChat(chat);
      }
    } catch (error) {
      console.error('Error loading support chat:', error);
    } finally {
      setShowSupportChat(false);
    }
  }, [getChatById, handleSetActiveChat]);
  
  // Handle chat selection
  const handleChatSelect = useCallback((chat: ChatWithId) => {
    handleSetActiveChat(chat);
    if (isMobileView) {
      setShowSidebar(false);
    }
  }, [handleSetActiveChat, isMobileView]);
  // Get chat store state and actions
  const {
    chats = [],
    activeChat,
    isLoading = false,
    loadUserChats,
    setActiveChat,
    getChatById,
    cleanup,
    createChat
  } = useChatStore();
  
  // Local state for UI
  const [isMobileView, setIsMobileView] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showCreateChat, setShowCreateChat] = useState(false);
  const [showSupportChat, setShowSupportChat] = useState(false);
  
  // Memoize the active chat ID to prevent unnecessary re-renders
  const activeChatId = useMemo(() => activeChat?.id, [activeChat]);
  
  // Handle window resize for mobile view
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      setIsMobileView(isMobile);
      if (!isMobile) {
        setShowSidebar(true);
      }
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Load user chats when component mounts or userId changes
  useEffect(() => {
    if (!userId) return;
    
    loadUserChats(userId);
    
    // Request notification permission if the browser supports it
    if ('Notification' in window) {
      Notification.requestPermission()
        .then(permission => console.log('Notification permission:', permission))
        .catch(error => console.error('Error requesting notification permission:', error));
    }
    
    return cleanup;
  }, [userId, loadUserChats, cleanup]);
  
  // Set initial chat if initialChatId is provided
  useEffect(() => {
    if (!initialChatId) {
      onInitialized();
      return;
    }
    
    const setInitialChat = async () => {
      try {
        const chat = await getChatById(initialChatId);
        if (chat) {
          setActiveChat(chat);
          if (isMobileView) {
            setShowSidebar(false);
          }
        }
      } catch (error) {
        console.error('Error loading initial chat:', error);
      } finally {
        onInitialized();
      }
    };
    
    setInitialChat();
  }, [initialChatId, getChatById, setActiveChat, isMobileView, onInitialized]);
  
  // Helper function to safely set active chat
  const handleSetActiveChat = useCallback((chat: Chat | null) => {
    setActiveChat(chat);
    if (chat && isMobileView) {
      setShowSidebar(false);
    }
  }, [isMobileView, setActiveChat]);
  
  // Handle chat creation
  const handleCreateChat = useCallback(async (chatId: string) => {
    try {
      const chat = await getChatById(chatId);
      if (chat) {
        handleSetActiveChat(chat);
      }
    } catch (error) {
      console.error('Error loading created chat:', error);
    } finally {
      setShowCreateChat(false);
    }
  }, [getChatById, handleSetActiveChat]);
  
  // Handle support chat creation
  const handleSupportChatCreated = useCallback(async (chatId: string) => {
    try {
      const chat = await getChatById(chatId);
      if (chat) {
        handleSetActiveChat(chat);
      }
    } catch (error) {
      console.error('Error loading support chat:', error);
    } finally {
      setShowSupportChat(false);
    }
  }, [getChatById, handleSetActiveChat]);

  // Render the chat interface
  return (
    <div className="flex h-full bg-white dark:bg-gray-900">
      {/* Mobile header */}
      {isMobileView && !showSidebar && activeChat && (
        <div className="md:hidden fixed top-0 left-0 right-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-2 z-10">
          <button
            onClick={() => setShowSidebar(true)}
            className="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Sidebar */}
      <div 
        className={`${showSidebar ? 'block' : 'hidden'} md:block w-full md:w-80 lg:w-96 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 overflow-y-auto`}
        style={isMobileView && !showSidebar ? { display: 'none' } : {}}
      >
        <ChatSidebar
          chats={chats}
          activeChat={activeChat}
          isLoading={isLoading}
          onChatSelect={handleSetActiveChat}
          onCreateChat={() => setShowCreateChat(true)}
          onSupportChat={() => setShowSupportChat(true)}
          onBack={isMobileView ? () => setShowSidebar(false) : undefined}
        />
      </div>

      {/* Chat content */}
      <div className={`flex-1 flex flex-col ${!showSidebar && activeChat ? 'block' : 'hidden'} md:block`}>
        {activeChat ? (
          <ChatWindow 
            chat={activeChat} 
            onBack={isMobileView ? () => setShowSidebar(true) : undefined} 
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <MessageSquare className="h-12 w-12 mb-4" />
            <h3 className="text-lg font-medium">Select a chat to start messaging</h3>
            <p className="text-sm mt-1">Or create a new one</p>
            <Button 
              className="mt-4" 
              onClick={() => setShowCreateChat(true)}
            >
              New Chat
            </Button>
          </div>
        )}
      </div>

      {showCreateChat && (
        <CreateChatModal
          onClose={() => setShowCreateChat(false)}
          onChatCreated={handleCreateChat}
        />
      )}

      {showSupportChat && (
        <SupportChatModal
          onClose={() => setShowSupportChat(false)}
          onChatCreated={handleSupportChatCreated}
        />
      )}
    </div>
  );
};

export default function ChatInterface({ className = '', initialChatId }: ChatInterfaceProps) {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isInitializing, setIsInitializing] = useState(true);
  
  const userId = user?.id;
  
  // Handle authentication state
  useEffect(() => {
    if (!authLoading && !userId) {
      router.push('/login?returnUrl=' + encodeURIComponent(window.location.pathname));
    }
  }, [authLoading, userId, router]);
  
  // Show loading state while checking auth or initializing
  if (authLoading || isInitializing) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="sr-only">Loading chat...</span>
      </div>
    );
  }
  
  // Show sign in prompt if not authenticated
  if (!userId) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-4 text-center">
        <MessageCircle className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">Sign in to chat</h3>
        <p className="text-muted-foreground mb-4">Please sign in to start chatting with other users.</p>
        <button
          onClick={() => router.push(`/login?returnUrl=${encodeURIComponent(window.location.pathname)}`)}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          Sign In
        </button>
      </div>
    );
  }

  // Debug chat loading
  useEffect(() => {
    console.log('ChatInterface: Chats updated:', {
      chatCount: chats.length,
      isLoading,
      activeChat: activeChat?.id,
      chats: chats.map(c => ({ id: c.id, participantIds: c.participantIds }))
    });
  }, [chats, isLoading, activeChat]);

  // Handle initial chat selection and chat creation callback
  useEffect(() => {
    const handleChatCreated = async (chatId: string) => {
      try {
        // Try to find the chat in the existing list
        const existingChat = chats.find(c => c.id === chatId);
        
        if (existingChat) {
          // If chat exists, set it as active
          safeSetActiveChat(existingChat);
        } else {
          // If chat not found in the list, it might be a new chat
          // The real-time listener should pick it up shortly
          console.log('New chat created, waiting for real-time update...');
        }
      } catch (error) {
        console.error('Error handling chat creation:', error);
      }
    };

    // Handle initial chat ID from URL or props
    const handleInitialChat = async () => {
      if (initialChatId) {
        try {
          const chat = await getChatById(initialChatId);
          if (chat) {
            safeSetActiveChat(chat);
          }
        } catch (error) {
          console.error('Error loading initial chat:', error);
        }
      }
    };

    handleInitialChat();

    // Cleanup function
    return () => {
      // Any cleanup if needed
    };
  }, [initialChatId, chats, isMobileView, setActiveChat, getChatById]);

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobileView(mobile);
      setShowSidebar(!mobile || !activeChat);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [activeChat]);

  if (!user) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">Sign in to Chat</h3>
          <p className="text-gray-500">Connect with other pet owners and service providers</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex h-full overflow-hidden ${className}`}>
      {/* Chat Sidebar - Always visible on desktop, toggleable on mobile */}
      <div className={`${
        isMobileView
          ? (showSidebar ? 'w-full' : 'hidden')
          : 'w-80 flex-shrink-0'
      } border-r border-gray-200/50 bg-white/95 backdrop-blur-sm`}>
        <ChatSidebar
          chats={chats}
          activeChat={activeChat}
          isLoading={isLoading}
          onChatSelect={(chat) => {
            safeSetActiveChat(chat);
            if (isMobileView) setShowSidebar(false);
          }}
          onCreateChat={() => setShowCreateChat(true)}
          onSupportChat={() => setShowSupportChat(true)}
          onBack={isMobileView ? () => setShowSidebar(true) : undefined}
        />
      </div>

      {/* Chat Window - Facebook-style layout */}
      <div className={`flex-1 flex flex-col ${
        isMobileView && showSidebar ? 'hidden' : 'flex'
      }`}>
        {getActiveChat() ? (
          <ChatWindow
            chat={getActiveChat()!}
            onBack={isMobileView ? () => setShowSidebar(true) : undefined}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-green-50/50 to-blue-50/50">
            <div className="text-center max-w-md mx-auto p-8">
              <div className="w-20 h-20 bg-gradient-to-r from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <MessageCircle className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-3">
                Fetchly Chat
              </h3>
              <p className="text-gray-500 mb-6">
                Connect with other pet owners, chat with service providers, or get help from our support team.
              </p>
              <div className="space-y-3">
                <button
                  onClick={() => setShowCreateChat(true)}
                  className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-300 shadow-lg"
                >
                  <Plus className="w-5 h-5" />
                  Start New Chat
                </button>
                <button
                  onClick={() => setShowSupportChat(true)}
                  className="w-full flex items-center justify-center gap-2 px-6 py-3 glass-card text-gray-700 rounded-xl hover:bg-white/80 transition-all duration-300"
                >
                  <MessageCircle className="w-5 h-5" />
                  Contact Support
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {showCreateChat && (
        <CreateChatModal
          onClose={() => setShowCreateChat(false)}
          onChatCreated={async (chatId) => {
            setShowCreateChat(false);

            // Try to find the chat in current chats first
            let chat = chats.find(c => c.id === chatId);

            // If not found, fetch it directly from the database
            if (!chat) {
              console.log('Chat not found in current list, fetching from database...');
              try {
                chat = await getChatById(chatId);

                if (chat) {
                  console.log('Chat fetched from database:', chat);
                  safeSetActiveChat(chat);

                  // Reload user chats in background to update the sidebar
                  const userId = user?.id;
                  if (userId) {
                    // Don't await this to avoid blocking the UI
                    loadUserChats(userId);
                  }
                } else {
                  console.log('Chat not found in database');
                }
              } catch (error) {
                console.error('Error fetching chat:', error);
              }
            } else {
              safeSetActiveChat(chat);
              console.log('Chat found immediately:', chat);
            }
          }}
        />
      )}

      {showSupportChat && (
        <SupportChatModal
          onClose={() => setShowSupportChat(false)}
          onChatCreated={async (chatId) => {
            setShowSupportChat(false);

            // Try to find the chat in current chats first
            let chat = chats.find(c => c.id === chatId);

            // If not found, fetch it directly from the database
            if (!chat) {
              console.log('Support chat not found in current list, fetching from database...');
              try {
                if (!initialChatId) return;
                chat = await getChatById(initialChatId);
                if (chat) {
                  safeSetActiveChat(chat);
                  // Reload user chats in background to update the sidebar
                  const userId = user?.id;
                  if (userId) {
                    loadUserChats(userId);
                  }
                }
              } catch (error) {
                console.error('Error fetching support chat:', error);
              }
            } else {
              setActiveChat(chat);
            }
          }}
        />
      )}
    </div>
  );
}

// Quick Chat Button Component for other pages
export function QuickChatButton({ className = '' }: { className?: string }) {
  const [showChat, setShowChat] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowChat(true)}
        className={`fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40 ${className}`}
      >
        <MessageCircle className="w-6 h-6" />
      </button>

      {showChat && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-6xl h-[80vh] bg-white rounded-2xl shadow-2xl overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-800">Fetchly Chat</h2>
              <button
                onClick={() => setShowChat(false)}
                className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
              >
                ×
              </button>
            </div>
            <div className="h-[calc(100%-4rem)]">
              <ChatInterface />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
