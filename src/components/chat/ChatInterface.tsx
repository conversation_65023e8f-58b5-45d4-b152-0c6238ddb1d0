'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useChatStore } from '@/stores/chatStore';
import type { Chat } from '@/types/chat';
import { MessageSquare, Loader2, Menu } from 'lucide-react';

// Components
import ChatSidebar from './ChatSidebar';
import ChatWindow from './ChatWindow';
import CreateChatModal from './CreateChatModal';
import SupportChatModal from './SupportChatModal';

interface ChatInterfaceProps {
  className?: string;
  initialChatId?: string;
}

export default function ChatInterface({ initialChatId }: ChatInterfaceProps) {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get initial chat ID from props or URL params
  const chatIdFromUrl = searchParams.get('chatId');
  const targetChatId = initialChatId || chatIdFromUrl || undefined;

  // Get chat store state and actions
  const {
    chats = [],
    activeChat,
    isLoading,
    loadUserChats,
    setActiveChat,
    getChatById,
    cleanup
  } = useChatStore();

  // Local state for UI
  const [isMobileView, setIsMobileView] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showCreateChat, setShowCreateChat] = useState(false);
  const [showSupportChat, setShowSupportChat] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const userId = user?.id;
  
  // Handle window resize for mobile view
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      setIsMobileView(isMobile);
      if (!isMobile) {
        setShowSidebar(true);
      }
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);
  
  // Load user chats when component mounts or userId changes
  useEffect(() => {
    if (!user?.id) return;
    
    loadUserChats(user.id);
    
    // Request notification permission if the browser supports it
    if ('Notification' in window) {
      Notification.requestPermission()
        .then(permission => console.log('Notification permission:', permission))
        .catch(error => console.error('Error requesting notification permission:', error));
    }
    
    return cleanup;
  }, [user?.id, loadUserChats, cleanup]);
  
  // Set initial chat if targetChatId is provided
  useEffect(() => {
    if (!targetChatId || !user?.id) {
      setIsInitialized(true);
      return;
    }

    const setInitialChat = async () => {
      try {
        const chat = await getChatById(targetChatId);
        if (chat) {
          setActiveChat(chat);
          if (isMobileView) {
            setShowSidebar(false);
          }
        }
      } catch (error) {
        console.error('Error loading initial chat:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    setInitialChat();
  }, [targetChatId, getChatById, setActiveChat, isMobileView, user?.id]);
  
  // Show loading state while initializing
  if (authLoading || !isInitialized) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Helper function to safely set active chat
  const handleSetActiveChat = useCallback((chat: Chat | null) => {
    setActiveChat(chat);
    if (chat && isMobileView) {
      setShowSidebar(false);
    }
  }, [isMobileView, setActiveChat]);
  
  // Handle chat creation
  const handleCreateChat = useCallback(async (chatId: string) => {
    try {
      const chat = await getChatById(chatId);
      if (chat) {
        handleSetActiveChat(chat);
      }
    } catch (error) {
      console.error('Error loading created chat:', error);
    } finally {
      setShowCreateChat(false);
    }
  }, [getChatById, handleSetActiveChat]);
  
  // Handle support chat creation
  const handleSupportChatCreated = useCallback(async (chatId: string) => {
    try {
      const chat = await getChatById(chatId);
      if (chat) {
        handleSetActiveChat(chat);
      }
    } catch (error) {
      console.error('Error loading support chat:', error);
    } finally {
      setShowSupportChat(false);
    }
  }, [getChatById, handleSetActiveChat]);
  

  // Show sign in prompt if not authenticated
  if (!userId) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-4 text-center">
        <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">Sign in to chat</h3>
        <p className="text-muted-foreground mb-4">Please sign in to start chatting with other users.</p>
        <button
          onClick={() => router.push(`/login?returnUrl=${encodeURIComponent(window.location.pathname)}`)}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          Sign In
        </button>
      </div>
    );
  }


  // Render the chat interface
  return (
    <div className="flex h-full bg-white dark:bg-gray-900">
      {/* Mobile header */}
      {isMobileView && !showSidebar && activeChat && (
        <div className="md:hidden fixed top-0 left-0 right-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-2 z-10">
          <button
            onClick={() => setShowSidebar(true)}
            className="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Sidebar */}
      <div 
        className={`${showSidebar ? 'block' : 'hidden'} md:block w-full md:w-80 lg:w-96 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 overflow-y-auto`}
        style={isMobileView && !showSidebar ? { display: 'none' } : {}}
      >
        <ChatSidebar
          chats={chats}
          activeChat={activeChat}
          isLoading={isLoading}
          onChatSelect={handleSetActiveChat}
          onCreateChat={() => setShowCreateChat(true)}
          onSupportChat={() => setShowSupportChat(true)}
          onBack={isMobileView ? () => setShowSidebar(false) : undefined}
        />
      </div>

      {/* Chat content */}
      <div className={`flex-1 flex flex-col ${!showSidebar && activeChat ? 'block' : 'hidden'} md:block`}>
        {activeChat ? (
          <ChatWindow 
            chat={activeChat} 
            onBack={isMobileView ? () => setShowSidebar(true) : undefined} 
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <MessageSquare className="h-12 w-12 mb-4" />
            <h3 className="text-lg font-medium">Select a chat to start messaging</h3>
            <p className="text-sm mt-1">Or create a new one</p>
            <button
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              onClick={() => setShowCreateChat(true)}
            >
              New Chat
            </button>
          </div>
        )}
      </div>

      {showCreateChat && (
        <CreateChatModal
          onClose={() => setShowCreateChat(false)}
          onChatCreated={handleCreateChat}
        />
      )}

      {/* Modals */}
      {showCreateChat && (
        <CreateChatModal
          onClose={() => setShowCreateChat(false)}
          onChatCreated={handleCreateChat}
        />
      )}

      {showSupportChat && (
        <SupportChatModal
          onClose={() => setShowSupportChat(false)}
          onChatCreated={handleSupportChatCreated}
        />
      )}
    </div>
  );
}

// Quick Chat Button Component for other pages
export function QuickChatButton({ className = '' }: { className?: string }) {
  const [showChat, setShowChat] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowChat(true)}
        className={`fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40 ${className}`}
      >
        <MessageSquare className="w-6 h-6" />
      </button>

      {showChat && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-6xl h-[80vh] bg-white rounded-2xl shadow-2xl overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-800">Fetchly Chat</h2>
              <button
                onClick={() => setShowChat(false)}
                className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
              >
                ×
              </button>
            </div>
            <div className="h-[calc(100%-4rem)]">
              <ChatInterface />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
