'use client';

import React, { useState, useEffect } from 'react';
import { MessageCircle, Users, Phone, Video, Settings, Search, Plus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

import { useChatStore } from '@/stores/chatStore';
import ChatSidebar from './ChatSidebar';
import ChatWindow from './ChatWindow';
import CreateChatModal from './CreateChatModal';
import SupportChatModal from './SupportChatModal';
import { notificationService } from '@/lib/services/notification-service';

interface ChatInterfaceProps {
  className?: string;
  initialChatId?: string;
}

export default function ChatInterface({ className = '', initialChatId }: ChatInterfaceProps) {
  const { user } = useAuth();
  const {
    chats,
    activeChat,
    isLoading,
    loadUserChats,
    setActiveChat,
    getChatById,
    cleanup
  } = useChatStore();

  const [showCreateChat, setShowCreateChat] = useState(false);
  const [showSupportChat, setShowSupportChat] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);

  // Load user chats on mount and request notification permission
  useEffect(() => {
    const userId = user?.uid || user?.id;
    console.log('ChatInterface: Loading chats for user:', userId);
    if (userId) {
      loadUserChats(userId);
      // Request notification permission
      notificationService.requestNotificationPermission();
    }

    return () => {
      cleanup();
    };
  }, [user?.uid, user?.id, loadUserChats, cleanup]);

  // Debug chat loading
  useEffect(() => {
    console.log('ChatInterface: Chats updated:', {
      chatCount: chats.length,
      isLoading,
      activeChat: activeChat?.id,
      chats: chats.map(c => ({ id: c.id, participantIds: c.participantIds }))
    });
  }, [chats, isLoading, activeChat]);

  // Handle initial chat selection
  useEffect(() => {
    if (initialChatId && chats.length > 0) {
      const chat = chats.find(c => c.id === initialChatId);
      if (chat) {
        setActiveChat(chat);
      }
    }
  }, [initialChatId, chats, setActiveChat]);

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobileView(mobile);
      setShowSidebar(!mobile || !activeChat);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [activeChat]);

  if (!user) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">Sign in to Chat</h3>
          <p className="text-gray-500">Connect with other pet owners and service providers</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex h-full overflow-hidden ${className}`}>
      {/* Chat Sidebar - Always visible on desktop, toggleable on mobile */}
      <div className={`${
        isMobileView
          ? (showSidebar ? 'w-full' : 'hidden')
          : 'w-80 flex-shrink-0'
      } border-r border-gray-200/50 bg-white/95 backdrop-blur-sm`}>
        <ChatSidebar
          chats={chats}
          activeChat={activeChat}
          isLoading={isLoading}
          onChatSelect={(chat) => {
            setActiveChat(chat);
            if (isMobileView) setShowSidebar(false);
          }}
          onCreateChat={() => setShowCreateChat(true)}
          onSupportChat={() => setShowSupportChat(true)}
          onBack={isMobileView ? () => setShowSidebar(true) : undefined}
        />
      </div>

      {/* Chat Window - Facebook-style layout */}
      <div className={`flex-1 flex flex-col ${
        isMobileView && showSidebar ? 'hidden' : 'flex'
      }`}>
        {activeChat ? (
          <ChatWindow
            chat={activeChat}
            onBack={isMobileView ? () => setShowSidebar(true) : undefined}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-green-50/50 to-blue-50/50">
            <div className="text-center max-w-md mx-auto p-8">
              <div className="w-20 h-20 bg-gradient-to-r from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <MessageCircle className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-3">
                Fetchly Chat
              </h3>
              <p className="text-gray-500 mb-6">
                Connect with other pet owners, chat with service providers, or get help from our support team.
              </p>
              <div className="space-y-3">
                <button
                  onClick={() => setShowCreateChat(true)}
                  className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-300 shadow-lg"
                >
                  <Plus className="w-5 h-5" />
                  Start New Chat
                </button>
                <button
                  onClick={() => setShowSupportChat(true)}
                  className="w-full flex items-center justify-center gap-2 px-6 py-3 glass-card text-gray-700 rounded-xl hover:bg-white/80 transition-all duration-300"
                >
                  <MessageCircle className="w-5 h-5" />
                  Contact Support
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {showCreateChat && (
        <CreateChatModal
          onClose={() => setShowCreateChat(false)}
          onChatCreated={async (chatId) => {
            setShowCreateChat(false);

            // Try to find the chat in current chats first
            let chat = chats.find(c => c.id === chatId);

            // If not found, fetch it directly from the database
            if (!chat) {
              console.log('Chat not found in current list, fetching from database...');
              chat = await getChatById(chatId);

              if (chat) {
                console.log('Chat fetched from database:', chat);
                setActiveChat(chat);

                // Also reload user chats to update the sidebar
                const userId = user?.uid || user?.id;
                if (userId) {
                  loadUserChats(userId);
                }
              } else {
                console.log('Chat not found in database, this should not happen');
                // Fallback: reload chats and try again
                const userId = user?.uid || user?.id;
                if (userId) {
                  await loadUserChats(userId);
                  const reloadedChat = chats.find(c => c.id === chatId);
                  if (reloadedChat) {
                    setActiveChat(reloadedChat);
                  }
                }
              }
            } else {
              setActiveChat(chat);
              console.log('Chat found immediately:', chat);
            }
          }}
        />
      )}

      {showSupportChat && (
        <SupportChatModal
          onClose={() => setShowSupportChat(false)}
          onChatCreated={async (chatId) => {
            setShowSupportChat(false);

            // Try to find the chat in current chats first
            let chat = chats.find(c => c.id === chatId);

            // If not found, fetch it directly from the database
            if (!chat) {
              console.log('Support chat not found in current list, fetching from database...');
              chat = await getChatById(chatId);

              if (chat) {
                setActiveChat(chat);
                // Also reload user chats to update the sidebar
                const userId = user?.uid || user?.id;
                if (userId) {
                  loadUserChats(userId);
                }
              }
            } else {
              setActiveChat(chat);
            }
          }}
        />
      )}
    </div>
  );
}

// Quick Chat Button Component for other pages
export function QuickChatButton({ className = '' }: { className?: string }) {
  const [showChat, setShowChat] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowChat(true)}
        className={`fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40 ${className}`}
      >
        <MessageCircle className="w-6 h-6" />
      </button>

      {showChat && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-6xl h-[80vh] bg-white rounded-2xl shadow-2xl overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-800">Fetchly Chat</h2>
              <button
                onClick={() => setShowChat(false)}
                className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
              >
                ×
              </button>
            </div>
            <div className="h-[calc(100%-4rem)]">
              <ChatInterface />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
