'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useState } from 'react';
import { Bug, RefreshCw, CheckCircle, XCircle } from 'lucide-react';

export default function UserDebugInfo() {
  const { user, firebaseUser, forceCreateUserDocument } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [createResult, setCreateResult] = useState<string>('');

  const handleForceCreateUser = async () => {
    setIsCreating(true);
    setCreateResult('');
    
    try {
      const result = await forceCreateUserDocument();
      if (result.success) {
        setCreateResult('✅ User document created successfully!');
      } else {
        setCreateResult(`❌ Failed: ${result.error}`);
      }
    } catch (error: any) {
      setCreateResult(`❌ Error: ${error.message}`);
    } finally {
      setIsCreating(false);
    }
  };

  if (!user && !firebaseUser) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="flex items-center space-x-2 mb-3">
        <Bug className="w-5 h-5 text-blue-600" />
        <h3 className="font-semibold text-gray-800">Debug Info</h3>
      </div>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Firebase User:</strong>
          <div className="ml-2 text-gray-600">
            {firebaseUser ? (
              <>
                <div>✅ Authenticated</div>
                <div>ID: {firebaseUser.uid}</div>
                <div>Email: {firebaseUser.email}</div>
              </>
            ) : (
              <div>❌ Not authenticated</div>
            )}
          </div>
        </div>
        
        <div>
          <strong>Firestore User:</strong>
          <div className="ml-2 text-gray-600">
            {user ? (
              <>
                <div>✅ Document exists</div>
                <div>ID: {user.id}</div>
                <div>Name: {user.name}</div>
                <div>Role: {user.role}</div>
              </>
            ) : (
              <div>❌ No document found</div>
            )}
          </div>
        </div>
        
        {firebaseUser && !user && (
          <div className="pt-2 border-t">
            <button
              onClick={handleForceCreateUser}
              disabled={isCreating}
              className="w-full flex items-center justify-center space-x-2 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50"
            >
              {isCreating ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <CheckCircle className="w-4 h-4" />
              )}
              <span>{isCreating ? 'Creating...' : 'Create User Doc'}</span>
            </button>
            
            {createResult && (
              <div className="mt-2 text-xs">
                {createResult}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
