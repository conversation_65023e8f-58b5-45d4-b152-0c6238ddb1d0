'use client';

import { useState, useEffect } from 'react';
import { 
  Wallet, 
  Plus, 
  ArrowUpRight, 
  ArrowDownLeft, 
  CreditCard,
  TrendingUp,
  DollarSign,
  Shield,
  Clock,
  CheckCircle,
  AlertCircle,
  Zap
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';

interface WalletStats {
  fetchlyBalance: number;
  pendingBalance: number;
  totalEarned: number;
  totalSpent: number;
  thisMonthEarned: number;
  thisMonthSpent: number;
  stripeBalance?: {
    available: Array<{ amount: number; currency: string }>;
    pending: Array<{ amount: number; currency: string }>;
  };
}

interface WalletData {
  id: string;
  userType: 'pet_owner' | 'provider';
  isActive: boolean;
  isVerified: boolean;
  stripeCustomerId: string;
  stripeAccountId?: string;
}

interface Transaction {
  id: string;
  type: string;
  amount: number;
  description: string;
  status: string;
  createdAt: string;
}

export default function WalletDashboard() {
  const { user, getIdToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [walletExists, setWalletExists] = useState(false);
  const [walletData, setWalletData] = useState<WalletData | null>(null);
  const [stats, setStats] = useState<WalletStats | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [showAddFunds, setShowAddFunds] = useState(false);

  useEffect(() => {
    if (user) {
      checkWalletExists();
    }
  }, [user]);

  const checkWalletExists = async () => {
    try {
      setLoading(true);
      const token = await getIdToken();
      
      const response = await fetch('/api/wallet/create', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      
      if (result.success && result.exists) {
        setWalletExists(true);
        setWalletData(result.wallet);
        await fetchWalletBalance();
      } else {
        setWalletExists(false);
      }
    } catch (error) {
      console.error('Error checking wallet:', error);
      toast.error('Failed to load wallet');
    } finally {
      setLoading(false);
    }
  };

  const createWallet = async () => {
    try {
      setLoading(true);
      const token = await getIdToken();
      
      const response = await fetch('/api/wallet/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          userType: user?.role || 'pet_owner'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setWalletExists(true);
        setWalletData(result.wallet);
        toast.success('Wallet created successfully!');
        await fetchWalletBalance();
      } else {
        // Handle specific error types
        if (result.action === 'setup_connect') {
          toast.error(
            'Stripe Connect Setup Required',
            {
              duration: 8000,
              style: {
                background: '#FEF3C7',
                color: '#92400E',
                border: '1px solid #F59E0B',
              },
            }
          );

          // Show detailed instructions
          console.error('❌ Stripe Connect Setup Required:');
          console.error('1. Go to https://dashboard.stripe.com/connect/overview');
          console.error('2. Click "Get started with Connect"');
          console.error('3. Complete the Connect setup process');
          console.error('4. Enable Express accounts for your platform');

          alert(`Stripe Connect Setup Required:\n\n${result.instructions?.join('\n') || result.message}`);
        } else {
          toast.error(result.message || result.error || 'Failed to create wallet');
        }
        throw new Error(result.error);
      }
    } catch (error: any) {
      console.error('Error creating wallet:', error);
      if (!error.message?.includes('Stripe Connect')) {
        toast.error(error.message || 'Failed to create wallet');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchWalletBalance = async () => {
    try {
      const token = await getIdToken();
      
      const response = await fetch('/api/wallet/balance', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      
      if (result.success) {
        setStats(result.stats);
        setTransactions(result.recentTransactions || []);
      }
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
      </div>
    );
  }

  if (!walletExists) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <Wallet className="w-12 h-12 text-white" />
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Create Your Fetchly Wallet</h2>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            Set up your secure wallet to manage payments, track earnings, and handle transactions seamlessly.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 max-w-2xl mx-auto">
            <div className="text-center">
              <Shield className="w-8 h-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Secure</h3>
              <p className="text-sm text-gray-600">Bank-level security with Stripe</p>
            </div>
            <div className="text-center">
              <Zap className="w-8 h-8 text-blue-500 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Fast</h3>
              <p className="text-sm text-gray-600">Instant payments and transfers</p>
            </div>
            <div className="text-center">
              <TrendingUp className="w-8 h-8 text-purple-500 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Track</h3>
              <p className="text-sm text-gray-600">Monitor all your transactions</p>
            </div>
          </div>

          <button
            onClick={createWallet}
            disabled={loading}
            className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-3 px-8 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Wallet'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Wallet Dashboard</h1>
          <p className="text-gray-600">Manage your balance and transactions</p>
        </div>
        <button
          onClick={() => setShowAddFunds(true)}
          className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-2 px-4 rounded-lg flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          Add Funds
        </button>
      </div>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600">Fetchly Balance</span>
            <Wallet className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ${stats?.fetchlyBalance?.toFixed(2) || '0.00'}
          </div>
          <div className="text-sm text-gray-500">Available to spend</div>
        </motion.div>

        {walletData?.userType === 'provider' && stats?.stripeBalance && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">Stripe Balance</span>
              <CreditCard className="w-5 h-5 text-blue-500" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              ${stats.stripeBalance.available[0]?.amount?.toFixed(2) || '0.00'}
            </div>
            <div className="text-sm text-gray-500">Ready for payout</div>
          </motion.div>
        )}

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600">This Month</span>
            <TrendingUp className="w-5 h-5 text-purple-500" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ${stats?.thisMonthEarned?.toFixed(2) || '0.00'}
          </div>
          <div className="text-sm text-gray-500">Earned</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600">Total Earned</span>
            <DollarSign className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ${stats?.totalEarned?.toFixed(2) || '0.00'}
          </div>
          <div className="text-sm text-gray-500">All time</div>
        </motion.div>
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
        </div>
        <div className="p-6">
          {transactions.length > 0 ? (
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      transaction.type.includes('topup') || transaction.type.includes('received')
                        ? 'bg-green-100 text-green-600'
                        : 'bg-red-100 text-red-600'
                    }`}>
                      {transaction.type.includes('topup') || transaction.type.includes('received') ? (
                        <ArrowDownLeft className="w-5 h-5" />
                      ) : (
                        <ArrowUpRight className="w-5 h-5" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{transaction.description}</div>
                      <div className="text-sm text-gray-500">
                        {new Date(transaction.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`font-semibold ${
                      transaction.type.includes('topup') || transaction.type.includes('received')
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      {transaction.type.includes('topup') || transaction.type.includes('received') ? '+' : '-'}
                      ${transaction.amount.toFixed(2)}
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      {transaction.status === 'completed' ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : transaction.status === 'pending' ? (
                        <Clock className="w-4 h-4 text-yellow-500" />
                      ) : (
                        <AlertCircle className="w-4 h-4 text-red-500" />
                      )}
                      <span className="text-gray-500 capitalize">{transaction.status}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-gray-400" />
              </div>
              <h4 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h4>
              <p className="text-gray-600">Your transaction history will appear here</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
