'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { confirmPasswordReset, verifyPasswordResetCode, applyActionCode } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { Eye, EyeOff, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function AuthActionPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const [mode, setMode] = useState<string | null>(null);
  const [actionCode, setActionCode] = useState<string | null>(null);
  const [email, setEmail] = useState<string>('');
  const [newPassword, setNewPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [processing, setProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<boolean>(false);

  useEffect(() => {
    const modeParam = searchParams.get('mode');
    const actionCodeParam = searchParams.get('oobCode');
    
    setMode(modeParam);
    setActionCode(actionCodeParam);

    if (modeParam === 'resetPassword' && actionCodeParam) {
      // Verify the password reset code and get the email
      verifyPasswordResetCode(auth, actionCodeParam)
        .then((email) => {
          setEmail(email);
          setLoading(false);
        })
        .catch((error) => {
          console.error('Error verifying reset code:', error);
          setError('Invalid or expired reset link. Please request a new password reset.');
          setLoading(false);
        });
    } else if (modeParam === 'verifyEmail' && actionCodeParam) {
      // Handle email verification
      applyActionCode(auth, actionCodeParam)
        .then(() => {
          setSuccess(true);
          setLoading(false);
        })
        .catch((error) => {
          console.error('Error verifying email:', error);
          setError('Invalid or expired verification link.');
          setLoading(false);
        });
    } else {
      setError('Invalid action link.');
      setLoading(false);
    }
  }, [searchParams]);

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newPassword || !confirmPassword) {
      setError('Please fill in all fields.');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }

    if (newPassword.length < 6) {
      setError('Password must be at least 6 characters long.');
      return;
    }

    if (!actionCode) {
      setError('Invalid reset code.');
      return;
    }

    setProcessing(true);
    setError('');

    try {
      await confirmPasswordReset(auth, actionCode, newPassword);

      // Send password reset confirmation email to user
      try {
        await fetch('/api/send-email', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'password_reset_confirmation',
            to: email,
            subject: '🔐 Your Fetchly Password Has Been Reset',
            data: {
              name: 'User'
            }
          })
        });
      } catch (emailError) {
        console.error('Failed to send password reset confirmation:', emailError);
        // Don't fail password reset if email fails
      }

      setSuccess(true);

      // Redirect to sign in after 3 seconds
      setTimeout(() => {
        router.push('/auth/signin?message=Password reset successful. Please sign in with your new password.');
      }, 3000);
    } catch (error: any) {
      console.error('Error resetting password:', error);
      let errorMessage = 'Failed to reset password. Please try again.';
      
      switch (error.code) {
        case 'auth/expired-action-code':
          errorMessage = 'Reset link has expired. Please request a new password reset.';
          break;
        case 'auth/invalid-action-code':
          errorMessage = 'Invalid reset link. Please request a new password reset.';
          break;
        case 'auth/weak-password':
          errorMessage = 'Password is too weak. Please choose a stronger password.';
          break;
      }
      
      setError(errorMessage);
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 flex items-center justify-center p-4">
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 w-full max-w-md text-center border border-white/30">
          <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800">Processing...</h2>
          <p className="text-gray-600 mt-2">Please wait while we verify your request.</p>
        </div>
      </div>
    );
  }

  if (mode === 'verifyEmail') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 flex items-center justify-center p-4">
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 w-full max-w-md text-center border border-white/30">
          {success ? (
            <>
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Email Verified!</h2>
              <p className="text-gray-600 mb-6">Your email has been successfully verified. You can now sign in to your account.</p>
              <Link 
                href="/auth/signin"
                className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-6 rounded-2xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-300 inline-block"
              >
                Sign In
              </Link>
            </>
          ) : (
            <>
              <XCircle className="w-16 h-16 text-red-600 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Verification Failed</h2>
              <p className="text-red-600 mb-6">{error}</p>
              <Link 
                href="/auth/signin"
                className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-6 rounded-2xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-300 inline-block"
              >
                Back to Sign In
              </Link>
            </>
          )}
        </div>
      </div>
    );
  }

  if (mode === 'resetPassword') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 flex items-center justify-center p-4">
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 w-full max-w-md border border-white/30">
          {success ? (
            <div className="text-center">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Password Reset Successful!</h2>
              <p className="text-gray-600 mb-6">Your password has been updated. You will be redirected to sign in shortly.</p>
              <div className="animate-pulse">
                <Loader2 className="w-6 h-6 text-blue-600 animate-spin mx-auto" />
                <p className="text-sm text-gray-500 mt-2">Redirecting...</p>
              </div>
            </div>
          ) : (
            <>
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
                  Reset Your Password
                </h2>
                <p className="text-gray-600">Enter your new password for: <strong>{email}</strong></p>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
                  <div className="flex items-center space-x-2">
                    <XCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
                    <p className="text-red-700 text-sm">{error}</p>
                  </div>
                </div>
              )}

              <form onSubmit={handlePasswordReset} className="space-y-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="w-full px-4 py-3 rounded-2xl border-2 border-green-200 bg-white/90 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 font-medium pr-12"
                      placeholder="Enter new password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Confirm New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="w-full px-4 py-3 rounded-2xl border-2 border-green-200 bg-white/90 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 font-medium pr-12"
                      placeholder="Confirm new password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={processing}
                  className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-6 rounded-2xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {processing ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>Updating Password...</span>
                    </>
                  ) : (
                    <span>Update Password</span>
                  )}
                </button>
              </form>

              <div className="mt-6 text-center">
                <Link 
                  href="/auth/signin"
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Back to Sign In
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 flex items-center justify-center p-4">
      <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 w-full max-w-md text-center border border-white/30">
        <XCircle className="w-16 h-16 text-red-600 mx-auto mb-6" />
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Invalid Link</h2>
        <p className="text-red-600 mb-6">{error}</p>
        <Link 
          href="/auth/signin"
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-6 rounded-2xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-300 inline-block"
        >
          Back to Sign In
        </Link>
      </div>
    </div>
  );
}
