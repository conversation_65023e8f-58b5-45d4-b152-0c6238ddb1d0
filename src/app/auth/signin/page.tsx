'use client';

import { useState } from 'react';
import { Eye, EyeOff, Mail, Lock, Heart, ArrowRight, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import DevelopmentNoticeModal from '@/components/modals/DevelopmentNoticeModal';

export default function SignInPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [showDevelopmentNotice, setShowDevelopmentNotice] = useState(false);
  const [pendingUserRole, setPendingUserRole] = useState<'provider' | 'petowner' | null>(null);
  const { signIn, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    const result = await signIn(formData.email, formData.password);

    if (result.success && result.user) {
      // Show development notice before redirecting
      const userRole = result.user.role === 'provider' ? 'provider' : 'petowner';
      setPendingUserRole(userRole);
      setShowDevelopmentNotice(true);
    } else {
      setError(result.error || 'Sign in failed');
    }
  };

  const handleContinueToDashboard = () => {
    // Close the development notice modal
    setShowDevelopmentNotice(false);
    
    // Small delay to allow the modal to close before redirecting
    setTimeout(() => {
      // Check for redirect parameter
      const redirectTo = searchParams.get('redirect');

      if (redirectTo) {
        // If there's a redirect parameter, go there
        router.push(redirectTo);
      } else if (pendingUserRole === 'provider') {
        router.push('/provider/dashboard');
      } else {
        router.push('/dashboard');
      }
    }, 100);
  };





  return (
    <div className="min-h-screen pt-20 flex items-center justify-center relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
      <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary-500/20 to-accent-500/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
      <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-gradient-to-r from-accent-500/15 to-warm-500/15 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-6">
              <Image
                src="/favicon.png"
                alt="Fetchly Logo"
                width={64}
                height={64}
                className="w-16 h-16"
              />
            </div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Welcome Back!</h1>
            <p className="text-cool-600">Sign in to your account to continue</p>
          </div>

          {/* Sign In Form */}
          <div className="glass-card rounded-2xl p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-cool-700 mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    placeholder="Enter your email"
                    required
                  />
                </div>
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-cool-700 mb-2">
                  Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="w-full pl-10 pr-12 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    placeholder="Enter your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cool-500 hover:text-primary-500 transition-colors duration-300"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertCircle className="w-5 h-5 text-red-500" />
                  <span className="text-red-700 text-sm">{error}</span>
                </div>
              )}

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.rememberMe}
                    onChange={(e) => setFormData({ ...formData, rememberMe: e.target.checked })}
                    className="w-4 h-4 text-primary-500 border-2 border-white/30 rounded focus:ring-primary-500"
                  />
                  <span className="text-sm text-cool-700">Remember me</span>
                </label>
                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-primary-500 hover:text-primary-600 transition-colors duration-300"
                >
                  Forgot password?
                </Link>
              </div>

              {/* Sign In Button */}
              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary w-full flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <>
                    Sign In
                    <ArrowRight className="w-5 h-5" />
                  </>
                )}
              </button>
            </form>

            {/* Sign Up Link */}
            <div className="text-center mt-6 pt-6 border-t border-white/30">
              <p className="text-cool-600">
                Don't have an account?{' '}
                <Link 
                  href="/auth/signup" 
                  className="text-primary-500 hover:text-primary-600 font-medium transition-colors duration-300"
                >
                  Sign up for free
                </Link>
              </p>
            </div>
          </div>

          {/* Additional Links */}
          <div className="text-center mt-6 space-y-2">
            <Link 
              href="/providers" 
              className="block text-cool-600 hover:text-primary-500 transition-colors duration-300"
            >
              Are you a service provider? Join here
            </Link>
            <Link 
              href="/help" 
              className="block text-cool-600 hover:text-primary-500 transition-colors duration-300"
            >
              Need help? Contact support
            </Link>
          </div>
        </div>
      </div>

      {/* Development Notice Modal */}
      <DevelopmentNoticeModal
        isOpen={showDevelopmentNotice}
        onClose={() => setShowDevelopmentNotice(false)}
        onContinue={handleContinueToDashboard}
        userType={pendingUserRole || 'petowner'}
      />
    </div>
  );
}
