'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Calendar, User, Heart, MessageCircle, Share2, ArrowLeft, Clock, Tag, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { BlogService, BlogPost, BlogComment } from '@/lib/services/blog-service';

// Remove local interfaces since we're importing from blog-service

export default function BlogPostPage() {
  const params = useParams();
  const { user } = useAuth();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [comments, setComments] = useState<BlogComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [liked, setLiked] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);

  // Mock data - Fetchly Team authored content with veterinary disclaimers
  const mockPost: BlogPost = {
    id: '1',
    title: 'Essential Grooming Tips for Long-Haired Dogs',
    content: `
      <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700">
              <strong>Important Veterinary Disclaimer:</strong> This content is for educational purposes only. Always consult with a qualified veterinarian before making any changes to your pet's grooming routine, especially if your pet has skin conditions, allergies, or health concerns.
            </p>
          </div>
        </div>
      </div>

      <h2>Introduction</h2>
      <p>Long-haired dogs are absolutely beautiful, but they require special care to keep their coats healthy and manageable. At Fetchly, we've worked with professional groomers across Puerto Rico to compile these essential tips for maintaining long-haired breeds in our tropical climate.</p>

      <h2>Daily Brushing is Essential</h2>
      <p>The most important thing you can do for your long-haired dog is to brush them daily. This prevents matting, reduces shedding, and distributes natural oils throughout their coat. In Puerto Rico's humid climate, this becomes even more critical.</p>

      <h3>Choosing the Right Brush</h3>
      <ul>
        <li><strong>Slicker brushes</strong> - Great for removing loose undercoat</li>
        <li><strong>Pin brushes</strong> - Perfect for daily maintenance</li>
        <li><strong>Undercoat rakes</strong> - Essential for double-coated breeds</li>
        <li><strong>Dematting combs</strong> - For stubborn tangles</li>
      </ul>

      <h2>Bathing Frequency</h2>
      <p>In Puerto Rico's climate, you might need to bathe your long-haired dog more frequently than in cooler climates. Generally, every 4-6 weeks is appropriate, but active dogs or those with skin conditions may need more frequent baths. <strong>Always consult your veterinarian about the appropriate bathing frequency for your specific pet.</strong></p>

      <h3>Pre-Bath Preparation</h3>
      <p>Always brush your dog thoroughly before bathing. Wet mats become tighter and harder to remove. If you find mats, work them out gently with a dematting spray and comb.</p>

      <h2>Dealing with Puerto Rico's Climate</h2>
      <p>Our tropical climate can be challenging for long-haired breeds. Here are some specific tips:</p>

      <ul>
        <li>Consider a "summer cut" during the hottest months (discuss with your vet first)</li>
        <li>Keep your dog well-hydrated</li>
        <li>Provide plenty of shade and air conditioning</li>
        <li>Watch for signs of overheating and contact your vet immediately if concerned</li>
      </ul>

      <h2>Professional Grooming</h2>
      <p>While daily care is essential, professional grooming every 6-8 weeks helps maintain your dog's coat health. A professional can:</p>

      <ul>
        <li>Properly trim and shape the coat</li>
        <li>Clean ears and trim nails</li>
        <li>Check for skin issues</li>
        <li>Provide breed-specific styling</li>
      </ul>

      <h2>When to Contact Your Veterinarian</h2>
      <p><strong>Always consult your veterinarian if you notice:</strong></p>
      <ul>
        <li>Excessive scratching or skin irritation</li>
        <li>Unusual odors from the coat or skin</li>
        <li>Changes in coat texture or appearance</li>
        <li>Any signs of skin conditions or allergies</li>
        <li>Behavioral changes related to grooming</li>
      </ul>

      <h2>Conclusion</h2>
      <p>Caring for a long-haired dog in Puerto Rico requires dedication, but the results are worth it. With proper daily care, regular professional grooming, and guidance from your veterinarian, your furry friend will stay comfortable, healthy, and beautiful year-round.</p>

      <p>At Fetchly, we connect you with certified professional groomers throughout Puerto Rico who understand the unique needs of long-haired breeds in our tropical climate. <strong>Remember: this information is educational only - always consult with your veterinarian for personalized pet care advice.</strong></p>
    `,
    author: {
      name: 'Fetchly Team',
      avatar: '/favicon.png',
      isPro: true,
      specialization: 'Pet Care Experts'
    },
    category: 'Grooming',
    tags: ['grooming', 'long-hair', 'maintenance', 'tropical-climate'],
    publishedAt: new Date(),
    readTime: 5,
    likes: 24,
    comments: 8,
    image: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=800',
    featured: true,
    likedBy: []
  };

  const mockComments: BlogComment[] = [
    {
      id: '1',
      postId: '1',
      author: {
        name: 'Carmen Silva',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',
        userId: 'user1'
      },
      content: 'This is so helpful! My Golden Retriever has been struggling with the humidity here. Going to try the summer cut after consulting with my vet first.',
      publishedAt: new Date('2024-01-16'),
      likes: 3,
      likedBy: []
    },
    {
      id: '2',
      postId: '1',
      author: {
        name: 'Roberto Martinez',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        userId: 'user2'
      },
      content: 'Great advice about the pre-bath brushing. I learned this the hard way! Thanks Fetchly team for the comprehensive guide.',
      publishedAt: new Date('2024-01-17'),
      likes: 5,
      likedBy: []
    },
    {
      id: '3',
      postId: '1',
      author: {
        name: 'Dr. Ana Morales',
        avatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=100',
        userId: 'vet1'
      },
      content: 'As a veterinarian in San Juan, I appreciate the emphasis on consulting with professionals. The climate considerations are spot-on for our tropical environment.',
      publishedAt: new Date('2024-01-18'),
      likes: 12,
      likedBy: []
    }
  ];

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setPost(mockPost);
      setComments(mockComments);
      setLoading(false);
    }, 1000);
  }, [params.id]);

  const handleLike = async () => {
    if (!user || !post) return;

    try {
      // Toggle like locally first for instant feedback
      const wasLiked = post.likedBy.includes(user.id);
      const newLikeCount = wasLiked ? post.likes - 1 : post.likes + 1;
      const newLikedBy = wasLiked 
        ? post.likedBy.filter(id => id !== user.id)
        : [...post.likedBy, user.id];

      setLiked(!wasLiked);
      setPost({
        ...post,
        likes: newLikeCount,
        likedBy: newLikedBy
      });

      // Then update on the server
      await BlogService.togglePostLike(post.id, user.id);
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert on error
      setLiked(!liked);
      setPost({
        ...post,
        likes: liked ? post.likes - 1 : post.likes + 1
      });
    }
  };

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !user || !post) return;

    setSubmittingComment(true);
    try {
      // Create optimistic comment
      const tempComment: BlogComment = {
        id: `temp-${Date.now()}`,
        postId: post.id,
        author: {
          name: user.name || user.email?.split('@')[0] || 'Anonymous',
          avatar: user.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
          userId: user.id
        },
        content: newComment,
        publishedAt: new Date(),
        likes: 0,
        likedBy: []
      };

      // Add to UI immediately
      setComments([tempComment, ...comments]);
      setNewComment('');

      // Update server
      const savedComment = await BlogService.addComment(
        post.id,
        user.id,
        user.name || user.email?.split('@')[0] || 'Anonymous',
        user.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        newComment
      );

      // Replace temp comment with saved one
      setComments(prev => [savedComment, ...prev.filter(c => c.id !== tempComment.id)]);

      // Update comment count
      setPost({
        ...post,
        comments: post.comments + 1
      });
    } catch (error) {
      console.error('Error adding comment:', error);
      // Remove temp comment on error
      setComments(prev => prev.filter(c => !c.id.startsWith('temp-')));
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleCommentLike = async (commentId: string) => {
    if (!user) return;

    try {
      const result = await BlogService.toggleCommentLike(commentId, user.uid);
      setComments(comments.map(comment =>
        comment.id === commentId
          ? { ...comment, likes: result.newLikeCount }
          : comment
      ));
    } catch (error) {
      console.error('Error toggling comment like:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen pt-20 bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading article...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen pt-20 bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h1>
          <Link href="/blog" className="text-blue-600 hover:text-blue-700">
            ← Back to Blog
          </Link>
        </div>
      </div>
              />
            </div>
          </div>

          {/* Veterinary Disclaimer Footer */}
          <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-xl">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Important Medical Disclaimer</h3>
                <p className="text-blue-800 text-sm leading-relaxed">
                  This article is provided by Fetchly for educational purposes only and should not replace professional veterinary advice.
                  Every pet is unique, and what works for one may not be suitable for another. Always consult with a qualified veterinarian
                  before making any changes to your pet's care routine, diet, exercise, or treatment plan. If your pet shows signs of illness
                  or distress, seek immediate veterinary attention.
                </p>
                <p className="text-blue-700 text-xs mt-3 font-medium">
                  Fetchly connects pet owners with certified professionals but does not provide veterinary services directly.
                </p>
              </div>
            </div>
          </div>

          {/* Article Actions */}
          <div className="flex items-center justify-between pt-8 mt-8 border-t border-gray-200">
            <div className="flex items-center space-x-6">
              <button
                onClick={handleLike}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  liked 
                    ? 'bg-red-100 text-red-700' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Heart className={`w-5 h-5 ${liked ? 'fill-current' : ''}`} />
                <span>{post.likes}</span>
              </button>
              <div className="flex items-center space-x-2 text-gray-700">
                <MessageCircle className="w-5 h-5" />
                <span>{post.comments}</span>
              </div>
            </div>
            <button className="flex items-center space-x-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
              <Share2 className="w-5 h-5" />
              <span>Share</span>
            </button>
          </div>
        </article>

        {/* Comments Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mt-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Comments ({comments.length})</h2>

          {/* Comment Form */}
          {user ? (
            <form onSubmit={handleCommentSubmit} className="mb-8">
              <div className="flex space-x-4">
                <img
                  src={user.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100'}
                  alt={user.name || 'User'}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div className="flex-1">
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Share your thoughts..."
                    className="w-full p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    rows={3}
                  />
                  <div className="flex justify-end mt-3">
                    <button
                      type="submit"
                      disabled={!newComment.trim() || submittingComment}
                      className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                    >
                      {submittingComment && (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      )}
                      <span>{submittingComment ? 'Posting...' : 'Post Comment'}</span>
                    </button>
                  </div>
                </div>
              </div>
            </form>
          ) : (
            <div className="bg-gray-50 rounded-lg p-6 mb-8 text-center">
              <p className="text-gray-600 mb-4">Please sign in to leave a comment</p>
              <Link href="/auth/signin" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Sign In
              </Link>
            </div>
          )}

          {/* Comments List */}
          <div className="space-y-6">
            {comments.map((comment) => (
              <div key={comment.id} className="flex space-x-4">
                <img
                  src={comment.author.avatar}
                  alt={comment.author.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div className="flex-1">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-gray-900">{comment.author.name}</h4>
                      <span className="text-sm text-gray-500">
                        {comment.publishedAt.toLocaleDateString()}
                      </span>
                      <h3 className="text-lg font-semibold text-blue-900 mb-2">Important Medical Disclaimer</h3>
                      <p className="text-blue-800 text-sm leading-relaxed">
                        This article is provided by Fetchly for educational purposes only and should not replace professional veterinary advice.
                        Every pet is unique, and what works for one may not be suitable for another. Always consult with a qualified veterinarian
                        before making any changes to your pet's care routine, diet, exercise, or treatment plan. If your pet shows signs of illness
                        or distress, seek immediate veterinary attention.
                      </p>
                      <p className="text-blue-700 text-xs mt-3 font-medium">
                        Fetchly connects pet owners with certified professionals but does not provide veterinary services directly.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Article Actions */}
                <div className="flex items-center justify-between pt-8 mt-8 border-t border-gray-200">
                  <div className="flex items-center space-x-6">
                    <button
                      onClick={handleLike}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                        liked 
                          ? 'bg-red-100 text-red-700' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <Heart className={`w-5 h-5 ${liked ? 'fill-current' : ''}`} />
                      <span>{post.likes}</span>
                    </button>
                    <div className="flex items-center space-x-2 text-gray-700">
                      <MessageCircle className="w-5 h-5" />
                      <span>{post.comments}</span>
                    </div>
                  </div>
                  <button className="flex items-center space-x-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                    <Share2 className="w-5 h-5" />
                    <span>Share</span>
                  </button>
                </div>
              </div>
            </article>

            {/* Comments Section */}
            <div className="bg-white rounded-2xl shadow-lg p-8 mt-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Comments ({comments.length})</h2>

              {/* Comment Form */}
              {user ? (
                <form onSubmit={handleCommentSubmit} className="mb-8">
                  <div className="flex space-x-4">
                    <img
                      src={user.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100'}
                      alt={user.name || 'User'}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <textarea
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="Share your thoughts..."
                        className="w-full p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        rows={3}
                      />
                      <div className="flex justify-end mt-3">
                        <button
                          type="submit"
                          disabled={!newComment.trim() || submittingComment}
                          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                        >
                          {submittingComment && (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          )}
                          <span>{submittingComment ? 'Posting...' : 'Post Comment'}</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              ) : (
                <div className="bg-gray-50 rounded-lg p-6 mb-8 text-center">
                  <p className="text-gray-600 mb-4">Please sign in to leave a comment</p>
                  <Link href="/auth/signin" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Sign In
                  </Link>
                </div>
              )}

              {/* Comments List */}
              <div className="space-y-6">
                {comments.map((comment) => (
                  <div key={comment.id} className="flex space-x-4">
                    <img
                      src={comment.author.avatar}
                      alt={comment.author.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-gray-900">{comment.author.name}</h4>
                          <span className="text-sm text-gray-500">
                            {comment.publishedAt.toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{comment.content}</p>
                      </div>
                      <div className="flex items-center space-x-4 mt-2">
                        <button
                          onClick={() => handleCommentLike(comment.id)}
                          className={`flex items-center space-x-1 text-sm transition-colors ${
                            user && comment.likedBy?.includes(user.uid)
                              ? 'text-red-600 hover:text-red-700'
                              : 'text-gray-500 hover:text-gray-700'
                          }`}
                          disabled={!user}
                        >
                          <Heart className={`w-4 h-4 ${
                            user && comment.likedBy?.includes(user.uid) ? 'fill-current' : ''
                          }`} />
                          <span>{comment.likes}</span>
                        </button>
                        {user && (
                          <button className="text-sm text-gray-500 hover:text-gray-700">
                            Reply
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Author Bio */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
              <h3 className="text-lg font-bold text-gray-900 mb-4">About the Author</h3>
              <div className="text-center">
                <img
                  src={post.author.avatar}
                  alt={post.author.name}
                  className="w-20 h-20 rounded-full object-cover mx-auto mb-4"
                />
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <h4 className="font-semibold text-gray-900">{post.author.name}</h4>
                  {post.author.isPro && (
                    <span className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      VERIFIED
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-3">{post.author.specialization}</p>
                <p className="text-sm text-gray-700">
                  The Fetchly team consists of certified pet care professionals, veterinarians, and animal behaviorists
                  dedicated to providing reliable, educational content for pet owners in Puerto Rico and beyond.
                </p>
                <div className="mt-4 p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                  <p className="text-xs text-gray-600">
                    <strong>Our Mission:</strong> Connecting pet owners with trusted professionals while providing
                    educational resources to promote pet health and wellbeing.
                  </p>
                </div>
              </div>
            </div>

            {/* Related Articles */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Related Articles</h3>
              <div className="space-y-4">
                <Link href="/blog/2" className="block hover:bg-gray-50 p-3 rounded-lg transition-colors">
                  <h4 className="font-semibold text-gray-900 text-sm mb-1">Understanding Your Cat's Behavior</h4>
                  <p className="text-xs text-gray-600">5 min read</p>
                </Link>
                <Link href="/blog/3" className="block hover:bg-gray-50 p-3 rounded-lg transition-colors">
                  <h4 className="font-semibold text-gray-900 text-sm mb-1">Exercise for Senior Dogs</h4>
                  <p className="text-xs text-gray-600">6 min read</p>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
