'use client';

import { useState, useEffect } from 'react';
import { Search, Calendar, User, Heart, MessageCircle, Share2, Filter, TrendingUp, Star, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { BlogService, BlogPost } from '@/lib/services/blog-service';

// BlogPost interface imported from blog-service

export default function BlogPage() {
  const { user } = useAuth();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);

  // Mock data for demonstration - All posts by Fetchly Team
  const mockPosts: BlogPost[] = [
    {
      id: '1',
      title: 'Essential Grooming Tips for Long-Haired Dogs',
      excerpt: 'Learn the best practices for keeping your long-haired dog healthy and beautiful with these professional grooming techniques. Always consult your veterinarian before making significant changes to your pet\'s care routine.',
      content: 'Full content here...',
      author: {
        name: '<PERSON>tch<PERSON>',
        avatar: '/favicon.png',
        isPro: true,
        specialization: 'Pet Care Experts'
      },
      category: 'Grooming',
      tags: ['grooming', 'long-hair', 'maintenance'],
      publishedAt: new Date('2024-01-15'),
      readTime: 5,
      likes: 24,
      comments: 8,
      featured: true,
      image: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=800',
      likedBy: []
    },
    {
      id: '2',
      title: 'Understanding Your Cat\'s Behavior: A Veterinarian\'s Guide',
      excerpt: 'Decode your feline friend\'s mysterious behaviors and learn what they\'re trying to tell you. Remember to always consult with a qualified veterinarian for any behavioral concerns.',
      content: 'Full content here...',
      author: {
        name: 'Fetchly Team',
        avatar: '/favicon.png',
        isPro: true,
        specialization: 'Pet Care Experts'
      },
      category: 'Health',
      tags: ['cats', 'behavior', 'veterinary'],
      publishedAt: new Date('2024-01-12'),
      readTime: 8,
      likes: 42,
      comments: 15,
      featured: false,
      image: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=800',
      likedBy: []
    },
    {
      id: '3',
      title: 'Creating a Safe Exercise Routine for Senior Dogs',
      excerpt: 'Keep your older dog active and healthy with age-appropriate exercises and activities. Always consult your veterinarian before starting any new exercise program for your senior pet.',
      content: 'Full content here...',
      author: {
        name: 'Fetchly Team',
        avatar: '/favicon.png',
        isPro: true,
        specialization: 'Pet Care Experts'
      },
      category: 'Exercise',
      tags: ['senior-dogs', 'exercise', 'health'],
      publishedAt: new Date('2024-01-10'),
      readTime: 6,
      likes: 18,
      comments: 5,
      featured: false,
      image: 'https://images.unsplash.com/photo-**********-71594a27632d?w=800',
      likedBy: []
    }
  ];

  const categories = [
    { id: 'all', name: 'All Posts', count: mockPosts.length },
    { id: 'health', name: 'Health & Wellness', count: 1 },
    { id: 'grooming', name: 'Grooming', count: 1 },
    { id: 'exercise', name: 'Exercise & Training', count: 1 },
    { id: 'nutrition', name: 'Nutrition', count: 0 },
    { id: 'behavior', name: 'Behavior', count: 0 }
  ];

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setPosts(mockPosts);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || post.category.toLowerCase() === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const featuredPost = posts.find(post => post.featured);
  const regularPosts = posts.filter(post => !post.featured);

  if (loading) {
    return (
      <div className="min-h-screen pt-20 bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading pet care insights...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              PetCare Blog
            </h1>
            <p className="text-xl text-green-100 max-w-2xl mx-auto mb-4">
              Expert insights, tips, and advice from the Fetchly team and certified pet care professionals in Puerto Rico
            </p>

            {/* Veterinary Disclaimer */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 max-w-3xl mx-auto mb-8">
              <p className="text-green-100 text-sm">
                <strong>⚠️ Important:</strong> All content is for educational purposes only. Always consult with a qualified veterinarian before making any decisions about your pet's health, behavior, or care routine.
              </p>
            </div>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search articles, tips, or topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 rounded-xl border-0 bg-white/90 focus:outline-none focus:ring-2 focus:ring-white focus:bg-white transition-all duration-300 text-gray-900 text-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-24">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full text-left px-4 py-3 rounded-lg transition-all duration-300 flex items-center justify-between ${
                      selectedCategory === category.id
                        ? 'bg-gradient-to-r from-green-600 to-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <span>{category.name}</span>
                    <span className={`text-sm px-2 py-1 rounded-full ${
                      selectedCategory === category.id
                        ? 'bg-white/20 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>

              {/* Pro Provider CTA */}
              {user?.role === 'provider' && (
                <div className="mt-8 p-4 bg-gradient-to-r from-green-600 to-blue-600 rounded-xl text-white text-center">
                  <Star className="w-8 h-8 mx-auto mb-3" />
                  <h4 className="font-bold mb-2">Share Your Expertise</h4>
                  <p className="text-sm text-green-100 mb-4">
                    Upgrade to Pro to publish your own pet care articles
                  </p>
                  <Link href="/provider/upgrade" className="bg-white text-green-600 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-colors">
                    Upgrade to Pro
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Featured Post */}
            {featuredPost && (
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                <div className="relative">
                  <img
                    src={featuredPost.image}
                    alt={featuredPost.title}
                    className="w-full h-64 object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-semibold">
                      Featured
                    </span>
                  </div>
                </div>
                <div className="p-8">
                  <div className="flex items-center space-x-4 mb-4">
                    <img
                      src={featuredPost.author.avatar}
                      alt={featuredPost.author.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-gray-900">{featuredPost.author.name}</h4>
                        {featuredPost.author.isPro && (
                          <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs font-semibold">
                            PRO
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{featuredPost.author.specialization}</p>
                    </div>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-3">{featuredPost.title}</h2>
                  <p className="text-gray-600 mb-4">{featuredPost.excerpt}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{featuredPost.publishedAt.toLocaleDateString()}</span>
                      </span>
                      <span>{featuredPost.readTime} min read</span>
                    </div>
                    <Link href={`/blog/${featuredPost.id}`} className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                      Read More
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {/* Regular Posts Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredPosts.filter(post => !post.featured).map((post) => (
                <div key={post.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <img
                    src={post.image}
                    alt={post.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-6">
                    <div className="flex items-center space-x-3 mb-3">
                      <img
                        src={post.author.avatar}
                        alt={post.author.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <div>
                        <div className="flex items-center space-x-1">
                          <span className="text-sm font-semibold text-gray-900">{post.author.name}</span>
                          {post.author.isPro && (
                            <span className="bg-purple-100 text-purple-700 px-1.5 py-0.5 rounded-full text-xs font-semibold">
                              PRO
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">{post.title}</h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">{post.excerpt}</p>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>{post.readTime} min read</span>
                      <span>{post.publishedAt.toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Heart className="w-4 h-4" />
                          <span>{post.likes}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <MessageCircle className="w-4 h-4" />
                          <span>{post.comments}</span>
                        </span>
                      </div>
                      <Link href={`/blog/${post.id}`} className="text-blue-600 hover:text-blue-700 font-semibold">
                        Read More
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredPosts.length === 0 && (
              <div className="bg-white rounded-2xl shadow-lg p-12 text-center">
                <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">No articles found</h3>
                <p className="text-gray-600">
                  Try adjusting your search or browse different categories
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
