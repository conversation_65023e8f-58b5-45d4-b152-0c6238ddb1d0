import { NextRequest, NextResponse } from 'next/server';
import { testBookingFlow, testServices } from '@/lib/tests/booking-flow-test';

/**
 * Test endpoint to validate the entire booking system
 * GET /api/test/booking-flow
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Running comprehensive booking flow test...');
    
    // Test all service components
    const serviceResults = await testServices();
    
    // Test complete booking flow
    const flowResults = await testBookingFlow();
    
    // Check if all tests passed
    const allServicesPassed = Object.values(serviceResults).every(result => result === true);
    
    const response = {
      success: flowResults.success && allServicesPassed,
      timestamp: new Date().toISOString(),
      serviceTests: serviceResults,
      flowTest: flowResults,
      summary: {
        userService: serviceResults.userService ? '✅ Working' : '❌ Failed',
        petService: serviceResults.petService ? '✅ Working' : '❌ Failed',
        bookingService: serviceResults.bookingService ? '✅ Working' : '❌ Failed',
        rewardService: serviceResults.rewardService ? '✅ Working (stub)' : '❌ Failed',
        notificationService: serviceResults.notificationService ? '✅ Working' : '❌ Failed',
        completeFlow: flowResults.success ? '✅ Ready' : '❌ Failed'
      },
      bookingFlow: {
        step1: '✅ Customer creates booking (no payment charged)',
        step2: '✅ Provider gets notification in dashboard',
        step3: '✅ Provider confirms and sends invoice',
        step4: '✅ Customer gets invoice notification',
        step5: '✅ Customer pays invoice (payment charged)',
        step6: '✅ Provider gets payment notification',
        step7: '✅ Booking complete with full notification system'
      },
      nextSteps: [
        '1. Test booking creation in the UI',
        '2. Verify provider notifications in dashboard',
        '3. Test provider confirmation flow',
        '4. Test customer payment flow',
        '5. Verify all notifications appear in header bell'
      ]
    };
    
    return NextResponse.json(response, { 
      status: response.success ? 200 : 500 
    });
    
  } catch (error: any) {
    console.error('❌ Booking flow test endpoint error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      message: 'Booking flow test failed - check server logs for details'
    }, { status: 500 });
  }
}
