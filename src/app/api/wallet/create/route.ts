import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/server-config';
import { adminDb } from '@/lib/firebase/admin-config';
import { COLLECTIONS } from '@/lib/database';
import { verifyIdToken } from '@/lib/auth/server';

export async function POST(request: NextRequest) {
  // Global error wrapper to ensure we always return JSON
  try {
    console.log('🔧 Wallet creation API called');

    // Check if firebase-admin is available
    if (!process.env.FIREBASE_ADMIN_PROJECT_ID) {
      console.error('❌ Firebase Admin not configured');
      return NextResponse.json(
        { success: false, error: 'Server configuration error - Firebase Admin not available' },
        { status: 500 }
      );
    }

    // Check if Stripe is available
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('❌ Stripe not configured');
      return NextResponse.json(
        { success: false, error: 'Server configuration error - Stripe not available' },
        { status: 500 }
      );
    }
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Invalid token' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;
    const { userType = 'provider' } = await request.json(); // Default to provider for provider dashboard

    // Check if adminDb is available
    if (!adminDb) {
      return NextResponse.json(
        { success: false, error: 'Firebase Admin not initialized' },
        { status: 500 }
      );
    }

    // Get user data
    const userCollection = userType === 'provider' ? COLLECTIONS.PROVIDERS : COLLECTIONS.USERS;
    const userRef = adminDb.collection(userCollection).doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();

    // Check if wallet already exists
    const walletRef = adminDb.collection(COLLECTIONS.WALLETS).doc(userId);
    const walletDoc = await walletRef.get();

    if (walletDoc.exists) {
      return NextResponse.json({
        success: true,
        wallet: walletDoc.data(),
        message: 'Wallet already exists'
      });
    }

    // 1. Create Stripe Customer (for all users)
    let stripeCustomerId = userData.stripeCustomerId;
    
    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        name: userData.name || userData.businessName,
        email: userData.email,
        metadata: {
          firebaseUid: userId,
          userType,
          platform: 'fetchly',
        },
      });

      stripeCustomerId = customer.id;

      // Save customer ID to user record
      await userRef.update({
        stripeCustomerId,
        updatedAt: new Date().toISOString(),
      });

      console.log(`✅ Created Stripe customer ${stripeCustomerId} for ${userType} ${userId}`);
    }

    // 2. Create Stripe Connected Account (for providers only)
    let stripeAccountId = userData.stripeAccountId;
    
    if (userType === 'provider' && !stripeAccountId) {
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: userData.email,
        business_profile: {
          name: userData.businessName || userData.name,
          product_description: `Pet care services provided by ${userData.businessName || userData.name}`,
          support_email: userData.email,
          url: `${process.env.NEXTAUTH_URL}/provider/${userId}`,
        },
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        settings: {
          payouts: {
            schedule: {
              interval: 'daily',
            },
          },
        },
        metadata: {
          firebaseUid: userId,
          userType: 'provider',
          platform: 'fetchly',
        },
      });

      stripeAccountId = account.id;

      // Save account ID to provider record
      await userRef.update({
        stripeAccountId,
        stripeAccountStatus: 'pending',
        updatedAt: new Date().toISOString(),
      });

      console.log(`✅ Created Stripe Connect account ${stripeAccountId} for provider ${userId}`);
    }

    // 3. Initialize Wallet in Database
    const walletData = {
      userId,
      userType,
      stripeCustomerId,
      ...(userType === 'provider' && { stripeAccountId }),
      
      // Balance tracking
      balance: 0,
      pendingBalance: 0,
      totalEarned: 0,
      totalSpent: 0,
      
      // Payment methods
      defaultPaymentMethod: null,
      paymentMethods: [],
      
      // Settings
      autoTopup: false,
      autoTopupAmount: 25,
      autoTopupThreshold: 10,
      
      // Status
      isActive: true,
      isVerified: false,
      
      // Timestamps
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Save wallet to database
    await walletRef.set(walletData);

    // Send admin notification
    try {
      await fetch(`${process.env.NEXTAUTH_URL}/api/send-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: `💳 New Wallet Created - ${userData.name || userData.businessName} (${userType})`,
          data: {
            type: 'wallet_created',
            userId,
            userType,
            userName: userData.name || userData.businessName,
            userEmail: userData.email,
            stripeCustomerId,
            ...(stripeAccountId && { stripeAccountId }),
            timestamp: new Date()
          }
        })
      });
    } catch (emailError) {
      console.error('Failed to send admin notification:', emailError);
    }

    return NextResponse.json({
      success: true,
      wallet: walletData,
      message: 'Wallet created successfully'
    });

  } catch (error: any) {
    console.error('Wallet creation error:', error);

    // Always return JSON, never HTML
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to create wallet',
        details: process.env.NODE_ENV === 'development' ? {
          stack: error.stack,
          name: error.name,
          cause: error.cause
        } : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Invalid token' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;

    // Check if adminDb is available
    if (!adminDb) {
      return NextResponse.json(
        { success: false, error: 'Firebase Admin not initialized' },
        { status: 500 }
      );
    }

    // Get wallet data
    const walletRef = adminDb.collection(COLLECTIONS.WALLETS).doc(userId);
    const walletDoc = await walletRef.get();

    if (!walletDoc.exists) {
      return NextResponse.json({
        success: false,
        exists: false,
        message: 'Wallet not found'
      });
    }

    const walletData = walletDoc.data();

    // Get real-time Stripe data if available
    if (walletData.stripeCustomerId) {
      try {
        const customer = await stripe.customers.retrieve(walletData.stripeCustomerId);
        walletData.stripeCustomerStatus = customer.deleted ? 'deleted' : 'active';
      } catch (error) {
        console.error('Error fetching Stripe customer:', error);
      }
    }

    if (walletData.stripeAccountId) {
      try {
        const account = await stripe.accounts.retrieve(walletData.stripeAccountId);
        walletData.stripeAccountStatus = account.details_submitted 
          ? (account.charges_enabled ? 'active' : 'restricted')
          : 'pending';
        walletData.stripeChargesEnabled = account.charges_enabled;
        walletData.stripePayoutsEnabled = account.payouts_enabled;
      } catch (error) {
        console.error('Error fetching Stripe account:', error);
      }
    }

    return NextResponse.json({
      success: true,
      exists: true,
      wallet: walletData
    });

  } catch (error: any) {
    console.error('Wallet fetch error:', error);

    // Always return JSON, never HTML
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to fetch wallet',
        details: process.env.NODE_ENV === 'development' ? {
          stack: error.stack,
          name: error.name,
          cause: error.cause
        } : undefined
      },
      { status: 500 }
    );
  }
}
