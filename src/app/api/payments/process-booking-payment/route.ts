import { NextRequest, NextResponse } from 'next/server';
import { adminAuth } from '@/lib/firebase/admin-config';
import { doc, updateDoc, getDoc, addDoc, collection } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Stripe from 'stripe';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

/**
 * Process payment for booking
 * POST /api/payments/process-booking-payment
 */
export async function POST(request: NextRequest) {
  try {
    console.log('💳 Processing booking payment...');
    
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ 
        success: false,
        error: 'Authentication required' 
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    
    // Verify the token
    let decodedToken;
    try {
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error) {
      console.error('Token verification failed:', error);
      return NextResponse.json({ 
        success: false,
        error: 'Invalid authentication token' 
      }, { status: 401 });
    }

    const { bookingId, paymentMethodId, saveCard = false } = await request.json();
    
    if (!bookingId || !paymentMethodId) {
      return NextResponse.json({
        success: false,
        error: 'Booking ID and payment method are required'
      }, { status: 400 });
    }

    // Get booking details
    const bookingRef = doc(db, 'bookings', bookingId);
    const bookingDoc = await getDoc(bookingRef);
    
    if (!bookingDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Booking not found'
      }, { status: 404 });
    }

    const booking = bookingDoc.data();
    
    // Verify customer owns this booking
    if (booking.userId !== decodedToken.uid) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized - not your booking'
      }, { status: 403 });
    }

    // Check if already paid
    if (booking.paymentStatus === 'paid') {
      return NextResponse.json({
        success: false,
        error: 'Booking has already been paid'
      }, { status: 400 });
    }

    // Get provider's Stripe Connect account
    const providerRef = doc(db, 'providers', booking.providerId);
    const providerDoc = await getDoc(providerRef);
    
    if (!providerDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Provider not found'
      }, { status: 404 });
    }

    const provider = providerDoc.data();
    const stripeAccountId = provider.stripeAccountId;

    if (!stripeAccountId) {
      return NextResponse.json({
        success: false,
        error: 'Provider has not set up payments yet'
      }, { status: 400 });
    }

    // Calculate amounts
    const totalAmount = Math.round((booking.finalPrice || booking.totalPrice) * 100); // Convert to cents
    const platformFee = Math.round(totalAmount * 0.10); // 10% platform fee
    const providerAmount = totalAmount - platformFee;

    console.log(`💰 Payment breakdown: Total: $${totalAmount/100}, Platform: $${platformFee/100}, Provider: $${providerAmount/100}`);

    // Create payment intent with Stripe Connect
    const paymentIntent = await stripe.paymentIntents.create({
      amount: totalAmount,
      currency: 'usd',
      payment_method: paymentMethodId,
      confirmation_method: 'manual',
      confirm: true,
      return_url: `${process.env.NEXT_PUBLIC_APP_URL}/booking-success/${bookingId}`,
      
      // Stripe Connect - transfer to provider
      transfer_data: {
        destination: stripeAccountId,
        amount: providerAmount,
      },
      
      // Application fee (platform commission)
      application_fee_amount: platformFee,
      
      // Metadata
      metadata: {
        bookingId: bookingId,
        providerId: booking.providerId,
        customerId: booking.userId,
        serviceName: booking.serviceName,
        petName: booking.petName,
      },
      
      // Description
      description: `${booking.serviceName} for ${booking.petName} - ${booking.providerName}`,
    });

    console.log('✅ Stripe payment intent created:', paymentIntent.id);

    // Handle payment result
    if (paymentIntent.status === 'succeeded') {
      // Payment successful - update booking
      const paymentData = {
        paymentStatus: 'paid',
        status: 'confirmed',
        paidAt: new Date(),
        paymentIntentId: paymentIntent.id,
        paymentAmount: totalAmount / 100,
        platformFee: platformFee / 100,
        providerAmount: providerAmount / 100,
        updatedAt: new Date(),
        workflow: {
          step: 3,
          description: 'Payment completed - Booking confirmed',
          nextAction: 'Enjoy your pet service!'
        }
      };

      await updateDoc(bookingRef, paymentData);
      console.log('✅ Booking updated with payment info');

      // Update invoice status
      const invoicesRef = collection(db, 'invoices');
      // Find and update the invoice (simplified - in production, store invoice ID in booking)
      
      // Create payment record
      const paymentRecord = {
        bookingId: bookingId,
        providerId: booking.providerId,
        customerId: booking.userId,
        paymentIntentId: paymentIntent.id,
        amount: totalAmount / 100,
        platformFee: platformFee / 100,
        providerAmount: providerAmount / 100,
        currency: 'usd',
        status: 'completed',
        paymentMethod: 'card',
        createdAt: new Date(),
        metadata: {
          serviceName: booking.serviceName,
          petName: booking.petName,
          scheduledDate: booking.scheduledDate
        }
      };

      const paymentsRef = collection(db, 'payments');
      await addDoc(paymentsRef, paymentRecord);
      console.log('✅ Payment record created');

      // Create notifications
      const customerNotification = {
        type: 'payment_successful',
        title: 'Payment Successful! 🎉',
        message: `Your booking with ${booking.providerName} is confirmed!`,
        bookingId: bookingId,
        userId: booking.userId,
        read: false,
        createdAt: new Date(),
        data: {
          bookingId: bookingId,
          providerName: booking.providerName,
          serviceName: booking.serviceName,
          amount: totalAmount / 100,
          scheduledDate: booking.scheduledDate,
          scheduledTime: booking.scheduledTime
        }
      };

      const providerNotification = {
        type: 'payment_received',
        title: 'Payment Received! 💰',
        message: `${booking.userName} paid $${providerAmount/100} for ${booking.serviceName}`,
        bookingId: bookingId,
        userId: booking.providerId,
        read: false,
        createdAt: new Date(),
        data: {
          bookingId: bookingId,
          customerName: booking.userName,
          amount: providerAmount / 100,
          serviceName: booking.serviceName,
          scheduledDate: booking.scheduledDate
        }
      };

      // Save notifications
      try {
        const notificationsRef = collection(db, 'notifications');
        await addDoc(notificationsRef, customerNotification);
        await addDoc(notificationsRef, providerNotification);
        console.log('✅ Payment notifications created');
      } catch (notificationError) {
        console.error('⚠️ Failed to create notifications:', notificationError);
      }

      return NextResponse.json({
        success: true,
        message: 'Payment successful! Your booking is confirmed.',
        paymentIntentId: paymentIntent.id,
        bookingId: bookingId,
        amountPaid: totalAmount / 100,
        booking: {
          id: bookingId,
          status: 'confirmed',
          providerName: booking.providerName,
          serviceName: booking.serviceName,
          scheduledDate: booking.scheduledDate,
          scheduledTime: booking.scheduledTime,
          petName: booking.petName
        }
      });

    } else if (paymentIntent.status === 'requires_action') {
      // 3D Secure or other authentication required
      return NextResponse.json({
        success: false,
        requiresAction: true,
        clientSecret: paymentIntent.client_secret,
        message: 'Additional authentication required'
      });

    } else {
      // Payment failed
      return NextResponse.json({
        success: false,
        error: 'Payment failed. Please try again.',
        paymentStatus: paymentIntent.status
      }, { status: 400 });
    }

  } catch (error: any) {
    console.error('❌ Error processing booking payment:', error);
    
    // Handle Stripe errors
    if (error.type === 'StripeCardError') {
      return NextResponse.json({
        success: false,
        error: error.message || 'Your card was declined'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'Payment processing failed. Please try again.',
      details: error.message
    }, { status: 500 });
  }
}
