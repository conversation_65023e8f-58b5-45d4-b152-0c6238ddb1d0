import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc, addDoc, collection } from 'firebase/firestore';
import { verifyIdToken } from '@/lib/auth';
import { getOrCreateStripeCustomer } from '@/lib/stripe/customer-utils';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// Platform fee percentage (5-10%)
const PLATFORM_FEE_PERCENTAGE = 0.08; // 8%

export async function POST(request: NextRequest) {
  try {
    const {
      bookingId,
      providerId,
      amount,
      currency = 'usd',
      paymentMethodId,
      customerEmail,
      description
    } = await request.json();

    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);

    if (!decodedToken) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;

    // Validate amount
    if (!amount || amount < 100) { // Minimum $1.00
      return NextResponse.json(
        { error: 'Amount must be at least $1.00' },
        { status: 400 }
      );
    }

    // Get provider data
    const providerRef = doc(db, 'providers', providerId);
    const providerDoc = await getDoc(providerRef);
    
    if (!providerDoc.exists()) {
      return NextResponse.json(
        { error: 'Provider not found' },
        { status: 404 }
      );
    }

    const providerData = providerDoc.data();
    const stripeAccountId = providerData.stripeAccountId;
    
    if (!stripeAccountId) {
      return NextResponse.json(
        { error: 'Provider has not completed Stripe onboarding' },
        { status: 400 }
      );
    }

    // Verify provider's Stripe account is active
    const account = await stripe.accounts.retrieve(stripeAccountId);
    if (!account.charges_enabled) {
      return NextResponse.json(
        { error: 'Provider account is not enabled for charges' },
        { status: 400 }
      );
    }

    // Get or create Stripe customer using utility function
    const stripeCustomerId = await getOrCreateStripeCustomer(userId, 'pet_owner');

    // Calculate platform fee
    const platformFeeAmount = Math.round(amount * PLATFORM_FEE_PERCENTAGE);
    const providerAmount = amount - platformFeeAmount;

    // Create payment intent with application fee
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      customer: stripeCustomerId, // Use proper Stripe customer ID
      payment_method: paymentMethodId,
      confirmation_method: 'manual',
      confirm: true,
      return_url: `${process.env.NEXTAUTH_URL}/dashboard?payment=success`,
      application_fee_amount: platformFeeAmount,
      transfer_data: {
        destination: stripeAccountId,
      },
      metadata: {
        bookingId,
        providerId,
        userId,
        platformFee: platformFeeAmount.toString(),
        providerAmount: providerAmount.toString(),
      },
      description: description || `Fetchly booking payment - ${bookingId}`,
      receipt_email: customerEmail,
    });

    // Update booking with payment information
    if (bookingId) {
      const bookingRef = doc(db, 'bookings', bookingId);
      await updateDoc(bookingRef, {
        paymentIntentId: paymentIntent.id,
        paymentStatus: paymentIntent.status,
        totalAmount: amount,
        platformFee: platformFeeAmount,
        providerAmount: providerAmount,
        paymentProcessedAt: new Date(),
        updatedAt: new Date(),
      });
    }

    // Record transaction
    await addDoc(collection(db, 'transactions'), {
      type: 'payment',
      bookingId,
      providerId,
      customerId: decodedToken.uid,
      paymentIntentId: paymentIntent.id,
      totalAmount: amount,
      platformFee: platformFeeAmount,
      providerAmount: providerAmount,
      currency,
      status: paymentIntent.status,
      stripeAccountId,
      createdAt: new Date(),
    });

    // Send admin notification
    try {
      await fetch(`${process.env.NEXTAUTH_URL}/api/send-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: `💳 Payment Processed - $${(amount / 100).toFixed(2)} (Fee: $${(platformFeeAmount / 100).toFixed(2)})`,
          data: {
            type: 'payment',
            bookingId,
            providerId,
            providerName: providerData.businessName || providerData.name,
            totalAmount: amount / 100,
            platformFee: platformFeeAmount / 100,
            providerAmount: providerAmount / 100,
            paymentIntentId: paymentIntent.id,
            timestamp: new Date()
          }
        })
      });
    } catch (emailError) {
      console.error('Failed to send admin notification:', emailError);
    }

    return NextResponse.json({
      success: true,
      paymentIntent: {
        id: paymentIntent.id,
        status: paymentIntent.status,
        client_secret: paymentIntent.client_secret,
        amount: paymentIntent.amount,
        platformFee: platformFeeAmount,
        providerAmount: providerAmount,
      },
    });

  } catch (error: any) {
    console.error('Payment charge error:', error);
    
    // Handle specific Stripe errors
    if (error.type === 'StripeCardError') {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message || 'Payment processing failed' },
      { status: 500 }
    );
  }
}
