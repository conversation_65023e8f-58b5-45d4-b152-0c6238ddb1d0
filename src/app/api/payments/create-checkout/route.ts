import { NextRequest, NextResponse } from 'next/server';
import { adminAuth } from '@/lib/firebase/admin-config';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Stripe from 'stripe';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

/**
 * Create Stripe Checkout Session for booking payment
 * POST /api/payments/create-checkout
 */
export async function POST(request: NextRequest) {
  try {
    console.log('💳 Creating Stripe Checkout session...');
    
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ 
        success: false,
        error: 'Authentication required' 
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    
    // Verify the token
    let decodedToken;
    try {
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error) {
      console.error('Token verification failed:', error);
      return NextResponse.json({ 
        success: false,
        error: 'Invalid authentication token' 
      }, { status: 401 });
    }

    const { bookingId } = await request.json();
    
    if (!bookingId) {
      return NextResponse.json({
        success: false,
        error: 'Booking ID is required'
      }, { status: 400 });
    }

    // Get booking details
    const bookingRef = doc(db, 'bookings', bookingId);
    const bookingDoc = await getDoc(bookingRef);
    
    if (!bookingDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Booking not found'
      }, { status: 404 });
    }

    const booking = bookingDoc.data();
    
    // Verify customer owns this booking
    if (booking.userId !== decodedToken.uid) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized - not your booking'
      }, { status: 403 });
    }

    // Check if already paid
    if (booking.paymentStatus === 'paid') {
      return NextResponse.json({
        success: false,
        error: 'Booking has already been paid'
      }, { status: 400 });
    }

    // Get provider's Stripe Connect account
    const providerRef = doc(db, 'providers', booking.providerId);
    const providerDoc = await getDoc(providerRef);
    
    if (!providerDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Provider not found'
      }, { status: 404 });
    }

    const provider = providerDoc.data();
    const stripeAccountId = provider.stripeAccountId;

    if (!stripeAccountId) {
      return NextResponse.json({
        success: false,
        error: 'Provider has not set up payments yet. Please contact support.'
      }, { status: 400 });
    }

    // Calculate amounts
    const totalAmount = Math.round((booking.finalPrice || booking.totalPrice) * 100); // Convert to cents
    const platformFeePercent = 0.10; // 10% platform fee
    const platformFee = Math.round(totalAmount * platformFeePercent);

    console.log(`💰 Payment breakdown: Total: $${totalAmount/100}, Platform Fee: $${platformFee/100}`);

    // Create Stripe Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'payment',
      
      // Success/Cancel URLs
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/booking-success/${bookingId}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/pay/${bookingId}?cancelled=true`,
      
      // Customer info
      customer_email: booking.userEmail,
      
      // Line items
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `${booking.serviceName} for ${booking.petName}`,
              description: `Pet service with ${booking.providerName} on ${booking.scheduledDate} at ${booking.scheduledTime}`,
              images: provider.profilePhoto ? [provider.profilePhoto] : [],
            },
            unit_amount: totalAmount,
          },
          quantity: 1,
        },
      ],
      
      // Payment intent data for Stripe Connect
      payment_intent_data: {
        // Application fee (platform commission)
        application_fee_amount: platformFee,
        
        // Transfer to provider's connected account
        transfer_data: {
          destination: stripeAccountId,
        },
        
        // Metadata
        metadata: {
          bookingId: bookingId,
          providerId: booking.providerId,
          customerId: booking.userId,
          serviceName: booking.serviceName,
          petName: booking.petName,
          platformFee: (platformFee / 100).toString(),
        },
        
        // Description
        description: `${booking.serviceName} for ${booking.petName} - ${booking.providerName}`,
      },
      
      // Session metadata
      metadata: {
        bookingId: bookingId,
        providerId: booking.providerId,
        customerId: booking.userId,
      },
      
      // Automatic tax calculation (optional)
      automatic_tax: {
        enabled: false, // Set to true if you want to collect taxes
      },
    });

    console.log('✅ Stripe Checkout session created:', session.id);

    // Update booking with checkout session ID
    await updateDoc(bookingRef, {
      stripeSessionId: session.id,
      paymentStatus: 'checkout_created',
      updatedAt: new Date(),
    });

    return NextResponse.json({
      success: true,
      sessionId: session.id,
      checkoutUrl: session.url,
      message: 'Checkout session created successfully',
      booking: {
        id: bookingId,
        providerName: booking.providerName,
        serviceName: booking.serviceName,
        petName: booking.petName,
        amount: totalAmount / 100,
        platformFee: platformFee / 100,
      }
    });

  } catch (error: any) {
    console.error('❌ Error creating checkout session:', error);
    
    // Handle Stripe errors
    if (error.type === 'StripeError') {
      return NextResponse.json({
        success: false,
        error: error.message || 'Payment processing error'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to create checkout session. Please try again.',
      details: error.message
    }, { status: 500 });
  }
}
