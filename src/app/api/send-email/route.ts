import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

interface EmailRequest {
  type: 'welcome' | 'password_reset_confirmation' | 'admin_notification' | 'test';
  to: string;
  subject: string;
  data: any;
}

export async function POST(request: NextRequest) {
  try {
    const body: EmailRequest = await request.json();
    const { type, to, subject, data } = body;

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.office365.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
      tls: {
        ciphers: 'SSLv3'
      }
    });

    let htmlContent = '';

    // Generate HTML based on email type
    switch (type) {
      case 'welcome':
        htmlContent = generateWelcomeHTML(data);
        break;
      case 'password_reset_confirmation':
        htmlContent = generatePasswordResetConfirmationHTML(data);
        break;
      case 'admin_notification':
        htmlContent = generateAdminNotificationHTML(data);
        break;
      case 'test':
        htmlContent = generateTestHTML(data);
        break;
      default:
        htmlContent = `<p>${JSON.stringify(data)}</p>`;
    }

    // Send email
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: to,
      subject: subject,
      html: htmlContent,
    };

    await transporter.sendMail(mailOptions);

    return NextResponse.json({ 
      success: true, 
      message: `Email sent successfully to ${to}` 
    });

  } catch (error: any) {
    console.error('Email sending failed:', error);
    
    return NextResponse.json({ 
      success: false, 
      error: error.message || 'Failed to send email'
    }, { status: 500 });
  }
}

function generateWelcomeHTML(data: any): string {
  const { name, role } = data;
  const isProvider = role === 'provider';
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 30px; border-radius: 15px 15px 0 0; text-align: center;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to Fetchly! 🎉</h1>
        <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 18px;">Your pet care journey starts here</p>
      </div>
      
      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 15px 15px; border: 1px solid #e2e8f0;">
        <h2 style="color: #1e293b; margin-top: 0;">Hi ${name}! 👋</h2>
        
        <p style="color: #475569; line-height: 1.6;">
          Thank you for joining Fetchly as a ${isProvider ? 'Pet Care Provider' : 'Pet Owner'}! 
          We're excited to have you as part of our growing community in Puerto Rico.
        </p>
        
        ${isProvider ? `
          <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3 style="color: white; margin-top: 0;">🏥 Provider Benefits</h3>
            <ul style="color: #e5f3ff; margin: 0;">
              <li>Connect with pet owners in your area</li>
              <li>Manage your services and bookings</li>
              <li>Build your professional reputation</li>
              <li>Grow your pet care business</li>
            </ul>
          </div>
        ` : `
          <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3 style="color: white; margin-top: 0;">🐾 Pet Owner Benefits</h3>
            <ul style="color: #e5f3ff; margin: 0;">
              <li>Find trusted pet care providers</li>
              <li>Book services easily</li>
              <li>Connect with the pet community</li>
              <li>Access pet care resources</li>
            </ul>
          </div>
        `}
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.NEXTAUTH_URL || 'https://fetchlyapp.herokuapp.com'}/auth/signin" 
             style="background: linear-gradient(135deg, #10b981, #3b82f6); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
            Get Started Now
          </a>
        </div>
        
        <div style="background: #f1f5f9; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h4 style="color: #1e293b; margin-top: 0;">📞 Need Help?</h4>
          <p style="color: #475569; margin: 0;">
            Our support team is here to help! Contact us at 
            <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
          </p>
        </div>
      </div>
      
      <div style="text-align: center; padding: 20px; color: #94a3b8; font-size: 12px;">
        <p>Welcome to the Fetchly family! 🐕🐱</p>
        <p>© 2025 Fetchly. All rights reserved.</p>
      </div>
    </div>
  `;
}

function generatePasswordResetConfirmationHTML(data: any): string {
  const { name } = data;
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 30px; border-radius: 15px 15px 0 0; text-align: center;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Password Reset Successful 🔐</h1>
        <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 18px;">Your account is secure</p>
      </div>
      
      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 15px 15px; border: 1px solid #e2e8f0;">
        <h2 style="color: #1e293b; margin-top: 0;">Hi ${name}! 👋</h2>
        
        <p style="color: #475569; line-height: 1.6;">
          Your Fetchly account password has been successfully reset. You can now sign in with your new password.
        </p>
        
        <div style="background: #dcfce7; border: 1px solid #bbf7d0; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h3 style="color: #166534; margin-top: 0;">✅ What happened?</h3>
          <ul style="color: #166534; margin: 0;">
            <li>Your password was successfully changed</li>
            <li>Your account remains secure</li>
            <li>You can now sign in with your new password</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.NEXTAUTH_URL || 'https://fetchlyapp.herokuapp.com'}/auth/signin" 
             style="background: linear-gradient(135deg, #10b981, #3b82f6); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
            Sign In Now
          </a>
        </div>
        
        <div style="background: #fef3c7; border: 1px solid #fcd34d; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h4 style="color: #92400e; margin-top: 0;">🛡️ Security Tip</h4>
          <p style="color: #92400e; margin: 0;">
            If you didn't request this password reset, please contact our support team immediately at 
            <a href="mailto:<EMAIL>" style="color: #92400e;"><EMAIL></a>
          </p>
        </div>
      </div>
      
      <div style="text-align: center; padding: 20px; color: #94a3b8; font-size: 12px;">
        <p>Your security is our priority 🔒</p>
        <p>© 2025 Fetchly. All rights reserved.</p>
      </div>
    </div>
  `;
}

function generateAdminNotificationHTML(data: any): string {
  const { type, timestamp, ...otherData } = data;
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 20px; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0;">Fetchly Admin Notification</h1>
        <p style="color: #e5f3ff; margin: 5px 0 0 0;">Type: ${type?.toUpperCase() || 'NOTIFICATION'}</p>
      </div>
      <div style="background: #f8fafc; padding: 20px; border-radius: 0 0 10px 10px; border: 1px solid #e2e8f0;">
        <p style="color: #64748b; margin: 0 0 20px 0;">Received: ${new Date(timestamp).toLocaleString()}</p>
        <pre style="background: #f1f5f9; padding: 15px; border-radius: 5px; overflow-x: auto; color: #475569;">${JSON.stringify(otherData, null, 2)}</pre>
      </div>
      <div style="text-align: center; padding: 20px; color: #94a3b8; font-size: 12px;">
        <p>This is an automated notification from Fetchly Platform</p>
        <p>© 2025 Fetchly. All rights reserved.</p>
      </div>
    </div>
  `;
}

function generateTestHTML(data: any): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 30px; border-radius: 15px 15px 0 0; text-align: center;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🧪 Test Email</h1>
        <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 18px;">Email system is working!</p>
      </div>
      
      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 15px 15px; border: 1px solid #e2e8f0;">
        <h2 style="color: #1e293b; margin-top: 0;">Congratulations! 🎉</h2>
        
        <p style="color: #475569; line-height: 1.6;">
          Your Fetchly email notification system is working correctly. You should now receive emails for:
        </p>
        
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h3 style="color: white; margin-top: 0;">📧 Email Types</h3>
          <ul style="color: #e5f3ff; margin: 0;">
            <li>New user signups</li>
            <li>Password reset requests</li>
            <li>Purchase notifications</li>
            <li>Booking confirmations</li>
            <li>System alerts</li>
          </ul>
        </div>
        
        <div style="background: #f1f5f9; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h4 style="color: #1e293b; margin-top: 0;">Test Data:</h4>
          <pre style="background: #ffffff; padding: 15px; border-radius: 5px; overflow-x: auto; color: #475569; border: 1px solid #e2e8f0;">${JSON.stringify(data, null, 2)}</pre>
        </div>
      </div>
      
      <div style="text-align: center; padding: 20px; color: #94a3b8; font-size: 12px;">
        <p>Email system test completed successfully! ✅</p>
        <p>© 2025 Fetchly. All rights reserved.</p>
      </div>
    </div>
  `;
}
