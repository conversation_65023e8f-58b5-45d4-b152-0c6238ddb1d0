import { NextRequest, NextResponse } from 'next/server';
import { stripe, logProductionEvent } from '@/lib/stripe/server-config';
import { auth as adminAuth, adminDb } from '@/lib/firebase/admin-config';
import { COLLECTIONS } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('No or invalid authorization header');
      return NextResponse.json({ 
        success: false,
        error: 'No authentication token provided' 
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    if (!token) {
      console.log('Empty token provided');
      return NextResponse.json({ 
        success: false,
        error: 'Invalid token format' 
      }, { status: 401 });
    }

    // Verify the Firebase token
    let decodedToken;
    try {
      console.log('Verifying ID token...');
      if (!adminAuth) {
        throw new Error('Firebase Admin not initialized');
      }
      decodedToken = await adminAuth.verifyIdToken(token);
      console.log('Token verified for user:', decodedToken.uid);
    } catch (error: any) {
      console.error('Token verification failed:', error);
      logProductionEvent('wallet_auth_failed', { 
        error: error?.message || 'Unknown error',
        errorCode: error?.code || 'unknown',
        errorName: error?.name || 'Error'
      }, 'error');
      
      let errorMessage = 'Invalid or expired token';
      if (error?.code === 'auth/id-token-expired') {
        errorMessage = 'Your session has expired. Please sign in again.';
      } else if (error?.code === 'auth/argument-error') {
        errorMessage = 'Invalid token format';
      }
      
      return NextResponse.json({ 
        success: false,
        error: errorMessage 
      }, { status: 401 });
    }

    if (!adminDb) {
      console.error('Firebase Admin not initialized');
      return NextResponse.json({ 
        success: false,
        error: 'Server configuration error' 
      }, { status: 503 });
    }

    // Get provider data with better error handling
    let providerDoc;
    try {
      if (!adminDb) {
        throw new Error('Firebase Admin database not initialized');
      }
      const providerRef = adminDb.collection(COLLECTIONS.PROVIDERS).doc(decodedToken.uid);
      providerDoc = await providerRef.get();

      if (!providerDoc.exists) {
        console.log(`Provider profile not found for user: ${decodedToken.uid}`);
        return NextResponse.json({ 
          success: false,
          error: 'Provider profile not found. Please complete your provider profile setup.' 
        }, { status: 404 });
      }
    } catch (dbError: any) {
      console.error('Database error when fetching provider:', dbError);
      logProductionEvent('wallet_db_error', {
        error: dbError?.message || 'Unknown database error',
        code: dbError?.code || 'unknown',
        userId: decodedToken.uid
      }, 'error');
      
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch provider data. Please try again.'
      }, { status: 500 });
    }

    const provider = providerDoc.data();

    if (!provider?.stripeAccountId) {
      return NextResponse.json({
        success: true,
        balance: { available: 0, pending: 0 },
        payouts: [],
        isOnboarded: false,
        message: 'No Stripe account found',
      });
    }

    try {
      // Get Stripe account balance
      const balance = await stripe.balance.retrieve({
        stripeAccount: provider.stripeAccountId,
      });

      // Calculate available and pending amounts
      const availableAmount = balance.available.reduce((sum, item) => sum + item.amount, 0) / 100;
      const pendingAmount = balance.pending.reduce((sum, item) => sum + item.amount, 0) / 100;

      // Get recent payouts
      const payouts = await stripe.payouts.list(
        { limit: 10 },
        { stripeAccount: provider.stripeAccountId }
      );

      // Get account status
      const account = await stripe.accounts.retrieve(provider.stripeAccountId);
      const isOnboarded = account.details_submitted && 
                         account.charges_enabled && 
                         account.payouts_enabled;

      logProductionEvent('wallet_data_retrieved', { 
        providerId: decodedToken.uid, 
        availableAmount, 
        pendingAmount,
        payoutCount: payouts.data.length
      });

      return NextResponse.json({
        success: true,
        balance: {
          available: availableAmount,
          pending: pendingAmount,
        },
        payouts: payouts.data.map(payout => ({
          id: payout.id,
          amount: payout.amount / 100,
          currency: payout.currency,
          status: payout.status,
          arrival_date: payout.arrival_date,
          created: payout.created,
          description: payout.description,
        })),
        isOnboarded,
        accountId: provider.stripeAccountId,
        status: {
          detailsSubmitted: account.details_submitted,
          chargesEnabled: account.charges_enabled,
          payoutsEnabled: account.payouts_enabled,
        },
      });

    } catch (error: any) {
      logProductionEvent('stripe_wallet_data_failed', { 
        providerId: decodedToken.uid, 
        error: error.message 
      }, 'error');

      // If Stripe account exists but has issues, return empty data instead of demo data
      return NextResponse.json({
        success: true,
        balance: { available: 0, pending: 0 },
        payouts: [],
        isOnboarded: false,
        error: 'Unable to retrieve wallet data. Please complete Stripe onboarding.',
      });
    }

  } catch (error: any) {
    logProductionEvent('wallet_api_error', { error: error.message }, 'error');
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
