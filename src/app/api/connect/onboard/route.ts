import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { db } from '@/lib/firebase';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { verifyIdToken } from '@/lib/auth';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export async function POST(request: NextRequest) {
  try {
    const { providerId, refreshUrl, returnUrl } = await request.json();
    
    // Verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyIdToken(token);
    
    if (!decodedToken) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    // Get provider data
    const providerRef = doc(db, 'providers', providerId);
    const providerDoc = await getDoc(providerRef);
    
    if (!providerDoc.exists()) {
      return NextResponse.json(
        { error: 'Provider not found' },
        { status: 404 }
      );
    }

    const providerData = providerDoc.data();
    
    // Check if provider already has a Stripe account
    let stripeAccountId = providerData.stripeAccountId;
    
    if (!stripeAccountId) {
      // Create new Stripe Express account
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US', // You can make this dynamic based on provider location
        email: providerData.email,
        business_profile: {
          name: providerData.businessName || providerData.name,
          product_description: `Pet care services provided by ${providerData.businessName || providerData.name}`,
          support_email: providerData.email,
          url: `${process.env.NEXTAUTH_URL}/provider/${providerId}`,
        },
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        settings: {
          payouts: {
            schedule: {
              interval: 'daily', // Automatic daily payouts
            },
          },
        },
      });

      stripeAccountId = account.id;

      // Save Stripe account ID to provider record
      await updateDoc(providerRef, {
        stripeAccountId,
        stripeAccountStatus: 'pending',
        stripeOnboardingStarted: new Date(),
        updatedAt: new Date(),
      });
    }

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: stripeAccountId,
      refresh_url: refreshUrl || `${process.env.NEXTAUTH_URL}/provider/dashboard?tab=wallet&refresh=true`,
      return_url: returnUrl || `${process.env.NEXTAUTH_URL}/provider/dashboard?tab=wallet&success=true`,
      type: 'account_onboarding',
    });

    // Send admin notification
    try {
      await fetch(`${process.env.NEXTAUTH_URL}/api/send-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'admin_notification',
          to: '<EMAIL>',
          subject: `🏦 Provider Started Stripe Onboarding - ${providerData.businessName}`,
          data: {
            type: 'stripe_onboarding',
            providerName: providerData.businessName || providerData.name,
            providerEmail: providerData.email,
            providerId,
            stripeAccountId,
            timestamp: new Date()
          }
        })
      });
    } catch (emailError) {
      console.error('Failed to send admin notification:', emailError);
    }

    return NextResponse.json({
      success: true,
      onboardingUrl: accountLink.url,
      stripeAccountId,
    });

  } catch (error: any) {
    console.error('Stripe Connect onboarding error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create onboarding link' },
      { status: 500 }
    );
  }
}
