import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/server-config';
import { headers } from 'next/headers';
import { doc, updateDoc, addDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';
import { ServerSubscriptionService } from '@/lib/stripe/server-subscription-service';
import { BoostService } from '@/lib/monetization/boost-service';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headersList = headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      console.error('No Stripe signature found');
      return NextResponse.json({ error: 'No signature' }, { status: 400 });
    }

    let event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log('Received Stripe webhook:', event.type);

    // Handle different event types
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;

      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object);
        break;

      case 'transfer.paid':
        await handleTransferPaid(event.data.object);
        break;

      case 'payout.paid':
        await handlePayoutPaid(event.data.object);
        break;

      case 'account.updated':
        await handleAccountUpdated(event.data.object);
        break;

      case 'charge.dispute.created':
        await handleDisputeCreated(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error: any) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

/**
 * Handle checkout session completed
 */
async function handleCheckoutSessionCompleted(session: any) {
  try {
    console.log('Checkout session completed:', session.id);

    if (session.metadata?.type === 'subscription') {
      console.log('Subscription checkout completed for provider:', session.metadata.providerId);
    }
  } catch (error) {
    console.error('Error handling checkout session completed:', error);
  }
}

/**
 * Handle subscription created/updated
 */
async function handleSubscriptionUpdated(subscription: any) {
  try {
    console.log('Subscription updated:', subscription.id);

    const result = await ServerSubscriptionService.handleSubscriptionCreated(subscription);
    if (!result.success) {
      console.error('Failed to handle subscription update:', result.error);
    }
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

/**
 * Handle subscription deleted
 */
async function handleSubscriptionDeleted(subscription: any) {
  try {
    console.log('Subscription deleted:', subscription.id);

    const providerId = subscription.metadata?.providerId;
    if (!providerId) {
      console.error('No provider ID in subscription metadata');
      return;
    }

    // Update provider to free tier
    await updateDoc(doc(db, COLLECTIONS.PROVIDERS, providerId), {
      membershipTier: 'free',
      subscriptionStatus: 'cancelled',
      updatedAt: new Date().toISOString(),
    });

    console.log('Provider downgraded to free tier:', providerId);
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}

/**
 * Handle invoice payment succeeded
 */
async function handleInvoicePaymentSucceeded(invoice: any) {
  try {
    console.log('Invoice payment succeeded:', invoice.id);

    const subscription = invoice.subscription;
    if (subscription) {
      const subscriptionObj = await stripe.subscriptions.retrieve(subscription);
      const providerId = subscriptionObj.metadata?.providerId;

      if (providerId) {
        await updateDoc(doc(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS, providerId), {
          bookingsThisMonth: 0,
          status: 'active',
          updatedAt: new Date().toISOString(),
        });

        console.log('Reset booking count for provider:', providerId);
      }
    }
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

/**
 * Handle invoice payment failed
 */
async function handleInvoicePaymentFailed(invoice: any) {
  try {
    console.log('Invoice payment failed:', invoice.id);

    const subscription = invoice.subscription;
    if (subscription) {
      const subscriptionObj = await stripe.subscriptions.retrieve(subscription);
      const providerId = subscriptionObj.metadata?.providerId;

      if (providerId) {
        await updateDoc(doc(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS, providerId), {
          status: 'past_due',
          updatedAt: new Date().toISOString(),
        });

        console.log('Marked subscription as past due for provider:', providerId);
      }
    }
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
  }
}

/**
 * Handle successful payment intent
 */
async function handlePaymentIntentSucceeded(paymentIntent: any) {
  try {
    const metadata = paymentIntent.metadata;

    // Handle boost purchases
    if (metadata.type === 'boost_purchase') {
      const boostId = metadata.boostId;
      if (boostId) {
        const result = await BoostService.activateBoost(boostId);
        if (result.success) {
          console.log('Boost activated:', boostId);
        } else {
          console.error('Failed to activate boost:', result.error);
        }
      }
    }

    // Handle booking payments
    else if (metadata.type === 'booking_payment') {
      const bookingId = metadata.bookingId;
      if (bookingId) {
        await updateDoc(doc(db, COLLECTIONS.BOOKINGS, bookingId), {
          paymentStatus: 'paid',
          stripePaymentIntentId: paymentIntent.id,
          updatedAt: new Date().toISOString(),
        });

        // Increment provider booking count
        const providerId = metadata.providerId;
        if (providerId) {
          await ServerSubscriptionService.incrementBookingCount(providerId);
        }

        console.log('Booking payment confirmed:', bookingId);
      }
    }

    // Handle tip payments
    else if (metadata.type === 'tip_payment') {
      const bookingId = metadata.bookingId;
      if (bookingId) {
        await updateDoc(doc(db, COLLECTIONS.BOOKINGS, bookingId), {
          tipAmount: parseFloat(metadata.tipAmount),
          tipPaid: true,
          tipPaymentIntentId: paymentIntent.id,
          updatedAt: new Date().toISOString(),
        });

        console.log('Tip payment confirmed for booking:', bookingId);
      }
    }

    // Update transaction status
    const transactionsQuery = query(
      collection(db, COLLECTIONS.TRANSACTIONS),
      where('stripePaymentIntentId', '==', paymentIntent.id)
    );
    const transactionDocs = await getDocs(transactionsQuery);

    for (const transactionDoc of transactionDocs.docs) {
      await updateDoc(transactionDoc.ref, {
        status: 'succeeded',
        stripeChargeId: paymentIntent.latest_charge,
        updatedAt: new Date().toISOString(),
      });
    }

    // Handle wallet top-up
    if (metadata.type === 'wallet_topup') {
      const userId = metadata.userId;
      const amount = paymentIntent.amount / 100; // Convert from cents
      
      // Get current user balance
      const userRef = doc(db, COLLECTIONS.USERS, userId);
      const userDoc = await getDocs(query(collection(db, COLLECTIONS.USERS), where('id', '==', userId)));
      
      if (!userDoc.empty) {
        const userData = userDoc.docs[0].data();
        const currentBalance = userData.fetchlyBalance || 0;
        
        await updateDoc(userDoc.docs[0].ref, {
          fetchlyBalance: currentBalance + amount,
          updatedAt: new Date().toISOString(),
        });

        console.log(`Wallet topped up: User ${userId}, Amount: $${amount}`);
      }
    }

    // Handle service payment
    if (metadata.type === 'service_payment') {
      // Update booking status, send notifications, etc.
      console.log(`Service payment succeeded: ${paymentIntent.id}`);
      
      // You can add booking status updates here
      // await updateBookingStatus(metadata.serviceId, 'paid');
    }

    console.log(`Payment intent succeeded: ${paymentIntent.id}`);
  } catch (error) {
    console.error('Error handling payment intent succeeded:', error);
  }
}

/**
 * Handle failed payment intent
 */
async function handlePaymentIntentFailed(paymentIntent: any) {
  try {
    // Update transaction status
    const transactionsQuery = query(
      collection(db, COLLECTIONS.TRANSACTIONS),
      where('stripePaymentIntentId', '==', paymentIntent.id)
    );
    const transactionDocs = await getDocs(transactionsQuery);
    
    for (const transactionDoc of transactionDocs.docs) {
      await updateDoc(transactionDoc.ref, {
        status: 'failed',
        failureReason: paymentIntent.last_payment_error?.message || 'Payment failed',
        updatedAt: new Date().toISOString(),
      });
    }

    console.log(`Payment intent failed: ${paymentIntent.id}`);
  } catch (error) {
    console.error('Error handling payment intent failed:', error);
  }
}

/**
 * Handle successful transfer to provider
 */
async function handleTransferPaid(transfer: any) {
  try {
    // Update transaction status
    const transactionsQuery = query(
      collection(db, COLLECTIONS.TRANSACTIONS),
      where('stripeTransferId', '==', transfer.id)
    );
    const transactionDocs = await getDocs(transactionsQuery);
    
    for (const transactionDoc of transactionDocs.docs) {
      await updateDoc(transactionDoc.ref, {
        status: 'succeeded',
        transferredAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }

    console.log(`Transfer paid: ${transfer.id}`);
  } catch (error) {
    console.error('Error handling transfer paid:', error);
  }
}

/**
 * Handle payout to provider's bank account
 */
async function handlePayoutPaid(payout: any) {
  try {
    // Log payout in our system
    await addDoc(collection(db, COLLECTIONS.PAYOUTS), {
      stripePayoutId: payout.id,
      amount: payout.amount / 100,
      currency: payout.currency,
      status: 'paid',
      arrivalDate: new Date(payout.arrival_date * 1000).toISOString(),
      createdAt: new Date().toISOString(),
    });

    console.log(`Payout paid: ${payout.id}`);
  } catch (error) {
    console.error('Error handling payout paid:', error);
  }
}

/**
 * Handle Connect account updates
 */
async function handleAccountUpdated(account: any) {
  try {
    const providerId = account.metadata?.providerId;
    
    if (providerId) {
      const isOnboarded = account.details_submitted && 
                         account.charges_enabled && 
                         account.payouts_enabled;

      await updateDoc(doc(db, COLLECTIONS.PROVIDERS, providerId), {
        stripeOnboardingStatus: isOnboarded ? 'completed' : 'pending',
        stripeChargesEnabled: account.charges_enabled,
        stripePayoutsEnabled: account.payouts_enabled,
        stripeDetailsSubmitted: account.details_submitted,
        updatedAt: new Date().toISOString(),
      });

      console.log(`Account updated: ${account.id}, Onboarded: ${isOnboarded}`);
    }
  } catch (error) {
    console.error('Error handling account updated:', error);
  }
}

/**
 * Handle dispute created
 */
async function handleDisputeCreated(dispute: any) {
  try {
    // Log dispute for admin review
    await addDoc(collection(db, COLLECTIONS.DISPUTES), {
      stripeDisputeId: dispute.id,
      chargeId: dispute.charge,
      amount: dispute.amount / 100,
      reason: dispute.reason,
      status: dispute.status,
      createdAt: new Date().toISOString(),
    });

    console.log(`Dispute created: ${dispute.id}`);
  } catch (error) {
    console.error('Error handling dispute created:', error);
  }
}



/**
 * GET handler for testing webhook endpoint
 */
export async function GET() {
  return NextResponse.json({
    message: 'Stripe webhook endpoint is active',
    timestamp: new Date().toISOString(),
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET ? 'configured' : 'missing'
  });
}
