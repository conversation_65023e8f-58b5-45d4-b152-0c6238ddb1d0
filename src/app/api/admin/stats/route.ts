import { NextRequest, NextResponse } from 'next/server';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  getCountFromServer, 
  Timestamp, 
  orderBy, 
  limit 
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { verifyIdToken } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);

    try {
      // Verify admin token
      const decodedToken = await verifyIdToken(token);
      
      if (!decodedToken) {
        return NextResponse.json(
          { error: 'Invalid token' },
          { status: 401 }
        );
      }

      // Verify admin role (this should be checked in the token or database)
      // For now, we'll assume the token verification is sufficient

      // Get current date for monthly calculations
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Fetch total users count
      const usersQuery = query(collection(db, 'users'));
      const usersSnapshot = await getCountFromServer(usersQuery);
      const totalUsers = usersSnapshot.data().count;

      // Fetch active providers count
      const providersQuery = query(
        collection(db, 'users'),
        where('role', '==', 'provider')
      );
      const providersSnapshot = await getCountFromServer(providersQuery);
      const activeProviders = providersSnapshot.data().count;

      // Fetch monthly bookings count
      const bookingsQuery = query(
        collection(db, 'bookings'),
        where('createdAt', '>=', Timestamp.fromDate(startOfMonth))
      );
      const bookingsSnapshot = await getCountFromServer(bookingsQuery);
      const monthlyBookings = bookingsSnapshot.data().count;

      // Fetch total revenue (this would need to be calculated from transactions)
      // For now, we'll use a placeholder calculation
      let totalRevenue = 0;
      try {
        const transactionsQuery = query(
          collection(db, 'transactions'),
          where('status', '==', 'completed')
        );
        const transactionsSnapshot = await getDocs(transactionsQuery);
        
        transactionsSnapshot.forEach((doc) => {
          const data = doc.data();
          if (data.amount && typeof data.amount === 'number') {
            totalRevenue += data.amount;
          }
        });
      } catch (revenueError) {
        console.warn('Error calculating revenue:', revenueError);
        // Continue with totalRevenue = 0
      }

      // Fetch recent activity (placeholder - you might want to create an activity log collection)
      const recentActivity: any[] = [];
      try {
        // Try to get recent bookings as activity
        const recentBookingsQuery = query(
          collection(db, 'bookings'),
          orderBy('createdAt', 'desc'),
          limit(5)
        );
        const recentBookingsSnapshot = await getDocs(recentBookingsQuery);
        
        recentBookingsSnapshot.forEach((doc) => {
          const data = doc.data();
          recentActivity.push({
            id: doc.id,
            description: `New booking: ${data.serviceName || 'Service'}`,
            timestamp: data.createdAt?.toDate() || new Date(),
            type: 'booking'
          });
        });
      } catch (activityError) {
        console.warn('Error fetching recent activity:', activityError);
      }

      // Fetch pending approvals (placeholder - you might want to create an approvals collection)
      const pendingApprovals: any[] = [];
      try {
        // Try to get pending provider applications
        const pendingProvidersQuery = query(
          collection(db, 'users'),
          where('role', '==', 'provider'),
          where('approved', '==', false)
        );
        const pendingProvidersSnapshot = await getDocs(pendingProvidersQuery);
        
        pendingProvidersSnapshot.forEach((doc) => {
          const data = doc.data();
          pendingApprovals.push({
            id: doc.id,
            type: 'Provider Application',
            description: `${data.name || data.displayName || 'Provider'} pending approval`,
            timestamp: data.createdAt?.toDate() || new Date()
          });
        });
      } catch (approvalsError) {
        console.warn('Error fetching pending approvals:', approvalsError);
      }

      return NextResponse.json({
        success: true,
        data: {
          totalUsers,
          activeProviders,
          monthlyBookings,
          totalRevenue: Math.round(totalRevenue * 100) / 100, // Round to 2 decimal places
          recentActivity,
          pendingApprovals,
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (verificationError) {
      console.error('Token verification error:', verificationError);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Admin stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
