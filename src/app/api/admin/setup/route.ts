import { NextRequest, NextResponse } from 'next/server';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase/config';

export async function POST(request: NextRequest) {
  try {
    // Only allow this in development or with proper authorization
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Admin setup not allowed in production' },
        { status: 403 }
      );
    }

    const adminEmail = process.env.ADMIN_EMAIL;
    const adminPassword = process.env.ADMIN_PASSWORD;
    const adminName = process.env.ADMIN_NAME || 'Fetchly Administrator';

    if (!adminEmail || !adminPassword) {
      return NextResponse.json(
        { error: 'Admin credentials not configured in environment variables' },
        { status: 500 }
      );
    }

    try {
      // Try to create the admin user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
      const user = userCredential.user;

      // Create admin user document in Firestore
      const adminData = {
        email: adminEmail,
        name: adminName,
        displayName: adminName,
        role: 'admin',
        isAdmin: true,
        superAdmin: true,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        permissions: ['all']
      };

      await setDoc(doc(db, 'users', user.uid), adminData);

      return NextResponse.json({
        success: true,
        message: 'Admin user created successfully',
        user: {
          id: user.uid,
          email: adminEmail,
          name: adminName,
          role: 'admin'
        }
      });

    } catch (authError: any) {
      // If user already exists, try to update their role
      if (authError.code === 'auth/email-already-in-use') {
        try {
          // Sign in to get the user
          const { signInWithEmailAndPassword } = await import('firebase/auth');
          const userCredential = await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
          const user = userCredential.user;

          // Check if user document exists
          const userDoc = await getDoc(doc(db, 'users', user.uid));
          
          const adminData = {
            email: adminEmail,
            name: adminName,
            displayName: adminName,
            role: 'admin',
            isAdmin: true,
            superAdmin: true,
            isActive: true,
            updatedAt: new Date(),
            permissions: ['all']
          };

          if (userDoc.exists()) {
            // Update existing user to admin
            await setDoc(doc(db, 'users', user.uid), {
              ...userDoc.data(),
              ...adminData
            }, { merge: true });
          } else {
            // Create new user document
            await setDoc(doc(db, 'users', user.uid), {
              ...adminData,
              createdAt: new Date()
            });
          }

          return NextResponse.json({
            success: true,
            message: 'Admin user updated successfully',
            user: {
              id: user.uid,
              email: adminEmail,
              name: adminName,
              role: 'admin'
            }
          });

        } catch (updateError) {
          console.error('Error updating admin user:', updateError);
          return NextResponse.json(
            { error: 'Failed to update existing admin user' },
            { status: 500 }
          );
        }
      } else {
        console.error('Firebase auth error:', authError);
        return NextResponse.json(
          { error: `Failed to create admin user: ${authError.message}` },
          { status: 500 }
        );
      }
    }

  } catch (error) {
    console.error('Admin setup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to setup admin user.' },
    { status: 405 }
  );
}
