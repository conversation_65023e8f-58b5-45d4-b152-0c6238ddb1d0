import { NextRequest, NextResponse } from 'next/server';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase/config';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Check if credentials match admin environment variables
    const adminEmail = process.env.ADMIN_EMAIL;
    const adminPassword = process.env.ADMIN_PASSWORD;

    if (!adminEmail || !adminPassword) {
      console.error('Admin credentials not configured in environment variables');
      return NextResponse.json(
        { error: 'Admin authentication not configured' },
        { status: 500 }
      );
    }

    // Verify admin credentials
    if (email !== adminEmail || password !== adminPassword) {
      return NextResponse.json(
        { error: 'Invalid admin credentials' },
        { status: 401 }
      );
    }

    try {
      // Sign in with Firebase Auth
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Get user document from Firestore
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      
      if (!userDoc.exists()) {
        return NextResponse.json(
          { error: 'Admin user not found in database' },
          { status: 404 }
        );
      }

      const userData = userDoc.data();

      // Verify admin role
      if (userData.role !== 'admin') {
        return NextResponse.json(
          { error: 'Access denied. Admin role required.' },
          { status: 403 }
        );
      }

      // Get ID token
      const idToken = await user.getIdToken();

      return NextResponse.json({
        success: true,
        user: {
          id: user.uid,
          email: user.email,
          name: userData.displayName || userData.name || 'Admin User',
          role: userData.role,
          isAdmin: true,
          superAdmin: userData.superAdmin || false
        },
        token: idToken
      });

    } catch (firebaseError: any) {
      console.error('Firebase authentication error:', firebaseError);
      
      // Handle specific Firebase auth errors
      if (firebaseError.code === 'auth/user-not-found') {
        return NextResponse.json(
          { error: 'Admin account not found' },
          { status: 404 }
        );
      } else if (firebaseError.code === 'auth/wrong-password') {
        return NextResponse.json(
          { error: 'Invalid admin credentials' },
          { status: 401 }
        );
      } else if (firebaseError.code === 'auth/too-many-requests') {
        return NextResponse.json(
          { error: 'Too many failed attempts. Please try again later.' },
          { status: 429 }
        );
      }

      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Admin authentication error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
