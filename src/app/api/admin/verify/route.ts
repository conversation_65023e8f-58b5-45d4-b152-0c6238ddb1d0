import { NextRequest, NextResponse } from 'next/server';
import { verifyIdToken } from '@/lib/firebase-admin';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    try {
      // Verify the ID token
      const decodedToken = await verifyIdToken(token);
      
      if (!decodedToken) {
        return NextResponse.json(
          { error: 'Invalid token' },
          { status: 401 }
        );
      }

      // Get user document from Firestore
      const userDoc = await getDoc(doc(db, 'users', decodedToken.uid));
      
      if (!userDoc.exists()) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      const userData = userDoc.data();

      // Verify admin role
      if (userData.role !== 'admin') {
        return NextResponse.json(
          { error: 'Access denied. Admin role required.' },
          { status: 403 }
        );
      }

      return NextResponse.json({
        success: true,
        user: {
          id: decodedToken.uid,
          email: decodedToken.email,
          name: userData.displayName || userData.name || 'Admin User',
          role: userData.role,
          isAdmin: true,
          superAdmin: userData.superAdmin || false
        }
      });

    } catch (verificationError) {
      console.error('Token verification error:', verificationError);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Admin verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
