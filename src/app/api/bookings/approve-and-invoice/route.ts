import { NextRequest, NextResponse } from 'next/server';
import { adminAuth } from '@/lib/firebase/admin-config';
import { doc, updateDoc, getDoc, addDoc, collection } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

/**
 * Provider approves booking and creates invoice
 * POST /api/bookings/approve-and-invoice
 */
export async function POST(request: NextRequest) {
  try {
    console.log('📋 Provider approving booking and creating invoice...');
    
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ 
        success: false,
        error: 'Authentication required' 
      }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    
    // Verify the token
    let decodedToken;
    try {
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error) {
      console.error('Token verification failed:', error);
      return NextResponse.json({ 
        success: false,
        error: 'Invalid authentication token' 
      }, { status: 401 });
    }

    const { bookingId, approvalNotes, adjustedPrice } = await request.json();
    
    if (!bookingId) {
      return NextResponse.json({
        success: false,
        error: 'Booking ID is required'
      }, { status: 400 });
    }

    // Get booking details
    const bookingRef = doc(db, 'bookings', bookingId);
    const bookingDoc = await getDoc(bookingRef);
    
    if (!bookingDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Booking not found'
      }, { status: 404 });
    }

    const booking = bookingDoc.data();
    
    // Verify provider owns this booking
    if (booking.providerId !== decodedToken.uid) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized - not your booking'
      }, { status: 403 });
    }

    // Update booking status
    const updatedBooking = {
      status: 'approved_awaiting_payment',
      approvedAt: new Date(),
      approvalNotes: approvalNotes || '',
      finalPrice: adjustedPrice || booking.totalPrice,
      updatedAt: new Date(),
      workflow: {
        step: 2,
        description: 'Approved - Invoice sent to customer',
        nextAction: 'Customer needs to complete payment'
      }
    };

    await updateDoc(bookingRef, updatedBooking);
    console.log('✅ Booking approved and updated');

    // Create invoice for customer
    const invoice = {
      bookingId: bookingId,
      providerId: decodedToken.uid,
      providerName: booking.providerName,
      customerId: booking.userId,
      customerName: booking.userName,
      customerEmail: booking.userEmail,
      
      // Service details
      serviceName: booking.serviceName,
      petName: booking.petName,
      scheduledDate: booking.scheduledDate,
      scheduledTime: booking.scheduledTime,
      duration: booking.duration,
      
      // Payment details
      amount: adjustedPrice || booking.totalPrice,
      currency: 'usd',
      status: 'pending',
      
      // Invoice details
      invoiceNumber: `INV-${Date.now()}`,
      description: `${booking.serviceName} for ${booking.petName} on ${booking.scheduledDate} at ${booking.scheduledTime}`,
      notes: approvalNotes || '',
      
      // Timestamps
      createdAt: new Date(),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      
      // Payment link (will be generated)
      paymentUrl: `${process.env.NEXT_PUBLIC_APP_URL}/pay/${bookingId}`,
      
      // Metadata
      metadata: {
        bookingId: bookingId,
        serviceType: booking.serviceId,
        petId: booking.petId
      }
    };

    // Save invoice
    const invoicesRef = collection(db, 'invoices');
    const invoiceDoc = await addDoc(invoicesRef, invoice);
    console.log('✅ Invoice created with ID:', invoiceDoc.id);

    // Create notification for customer
    const customerNotification = {
      type: 'booking_approved',
      title: 'Booking Approved! 🎉',
      message: `${booking.providerName} approved your booking. Complete payment to confirm.`,
      bookingId: bookingId,
      invoiceId: invoiceDoc.id,
      userId: booking.userId,
      read: false,
      createdAt: new Date(),
      data: {
        bookingId: bookingId,
        invoiceId: invoiceDoc.id,
        providerName: booking.providerName,
        serviceName: booking.serviceName,
        amount: invoice.amount,
        paymentUrl: invoice.paymentUrl
      }
    };

    // Save customer notification
    try {
      const notificationsRef = collection(db, 'notifications');
      await addDoc(notificationsRef, customerNotification);
      console.log('✅ Customer notification created');
    } catch (notificationError) {
      console.error('⚠️ Failed to create customer notification:', notificationError);
    }

    // Create provider notification
    const providerNotification = {
      type: 'invoice_sent',
      title: 'Invoice Sent',
      message: `Invoice sent to ${booking.userName} for $${invoice.amount}`,
      bookingId: bookingId,
      invoiceId: invoiceDoc.id,
      userId: decodedToken.uid,
      read: false,
      createdAt: new Date(),
      data: {
        bookingId: bookingId,
        invoiceId: invoiceDoc.id,
        customerName: booking.userName,
        amount: invoice.amount
      }
    };

    try {
      const notificationsRef = collection(db, 'notifications');
      await addDoc(notificationsRef, providerNotification);
      console.log('✅ Provider notification created');
    } catch (notificationError) {
      console.error('⚠️ Failed to create provider notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      message: 'Booking approved and invoice sent to customer',
      bookingId: bookingId,
      invoiceId: invoiceDoc.id,
      invoice: {
        id: invoiceDoc.id,
        amount: invoice.amount,
        customerName: invoice.customerName,
        serviceName: invoice.serviceName,
        paymentUrl: invoice.paymentUrl,
        dueDate: invoice.dueDate
      }
    });

  } catch (error: any) {
    console.error('❌ Error approving booking and creating invoice:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to approve booking and create invoice',
      details: error.message
    }, { status: 500 });
  }
}
