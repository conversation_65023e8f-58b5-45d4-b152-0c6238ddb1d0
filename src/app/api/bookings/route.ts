import { NextRequest, NextResponse } from 'next/server';
import { withMiddleware } from '@/lib/middleware';
import { BookingService } from '@/lib/services';

// Get user's bookings
async function getBookingsHandler(request: NextRequest) {
  try {
    const user = (request as any).user;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as any;
    
    const bookings = await BookingService.getUserBookings(user.id, status);
    
    return NextResponse.json({
      success: true,
      bookings
    });
  } catch (error) {
    console.error('Get bookings error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get bookings'
    }, { status: 500 });
  }
}

// Create new booking
async function createBookingHandler(request: NextRequest) {
  try {
    const user = (request as any).user;

    // Parse request body
    const body = await request.json();
    console.log('🔔 Received booking request:', body);

    // Validate required fields
    const requiredFields = ['providerId', 'serviceName', 'petName', 'petType', 'scheduledDate', 'contactPhone', 'contactEmail'];
    const missingFields = requiredFields.filter(field => !body[field]);

    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      }, { status: 400 });
    }

    // Prepare booking data with proper structure
    const bookingData = {
      userId: user.id,
      providerId: body.providerId,
      providerName: body.providerName || 'Provider',
      serviceId: body.serviceId || 'general',
      serviceName: body.serviceName,
      petId: body.petId || null,
      petName: body.petName,
      petType: body.petType,
      scheduledDate: new Date(body.scheduledDate),
      scheduledTime: body.scheduledTime || '10:00',
      duration: body.duration || 60,
      totalPrice: body.totalPrice || 0,
      notes: body.notes || '',
      contactPhone: body.contactPhone,
      contactEmail: body.contactEmail,
      status: 'pending_provider_approval', // New status - no payment charged
      paymentStatus: 'pending',
      paymentAuthorized: false,
      paidAmount: 0
    };

    console.log('🔔 Creating booking with data:', bookingData);

    const booking = await BookingService.createBooking(bookingData);

    console.log('✅ Booking created successfully:', booking.id);

    return NextResponse.json({
      success: true,
      booking,
      message: 'Booking request sent! Provider will confirm and send invoice - no payment charged yet.'
    }, { status: 201 });

  } catch (error: any) {
    console.error('❌ Create booking error:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create booking'
    }, { status: 500 });
  }
}

// Validation schema for creating bookings
const createBookingSchema = {
  petId: { required: true, type: 'uuid' as const },
  providerId: { required: true, type: 'uuid' as const },
  serviceId: { required: true, type: 'uuid' as const },
  serviceName: { required: true, type: 'string' as const, maxLength: 200 },
  providerName: { required: true, type: 'string' as const, maxLength: 200 },
  petName: { required: true, type: 'string' as const, maxLength: 100 },
  scheduledDate: { required: true, type: 'date' as const },
  scheduledTime: { required: true, type: 'string' as const },
  duration: { required: true, type: 'number' as const, min: 15, max: 480 },
  totalPrice: { required: true, type: 'number' as const, min: 0, max: 10000 },
  paymentMethod: { required: false, type: 'string' as const },
  notes: { required: false, type: 'string' as const, maxLength: 1000 },
  specialRequests: { required: false, type: 'string' as const, maxLength: 500 }
};

export const GET = withMiddleware(getBookingsHandler, {
  auth: true,
  requireVerified: true
});

export const POST = withMiddleware(createBookingHandler, {
  auth: true,
  requireVerified: true,
  validation: createBookingSchema
});
