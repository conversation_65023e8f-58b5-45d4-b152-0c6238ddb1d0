import { NextRequest, NextResponse } from 'next/server';
import { emailNotificationService } from '@/lib/services/email-service';

export async function POST(request: NextRequest) {
  try {
    // Test the email service
    await emailNotificationService.testEmail();
    
    return NextResponse.json({ 
      success: true, 
      message: 'Test email sent successfully! Check your <NAME_EMAIL>' 
    });
  } catch (error: any) {
    console.error('Test email failed:', error);
    
    return NextResponse.json({ 
      success: false, 
      error: error.message || 'Failed to send test email',
      details: error.toString()
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Email test endpoint. Use POST to send a test email.' 
  });
}
