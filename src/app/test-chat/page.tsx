'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useChatStore } from '@/stores/chatStore';
import { subscriptionService } from '@/lib/services/subscription-service';
import { emailNotificationService } from '@/lib/services/email-service';

export default function TestChatPage() {
  const { user } = useAuth();
  const { chats, isLoading, loadUserChats, createChat } = useChatStore();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runChatTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    
    try {
      addResult('🧪 Starting Chat System Tests...');
      
      // Test 1: User Authentication
      addResult(`👤 User: ${user ? `${user.email} (ID: ${user.uid || user.id})` : 'Not authenticated'}`);
      
      if (!user) {
        addResult('❌ User not authenticated - please sign in first');
        setIsRunningTests(false);
        return;
      }

      // Test 2: Load User Chats
      addResult('📱 Loading user chats...');
      const userId = user.uid || user.id;
      if (userId) {
        loadUserChats(userId);
        addResult(`✅ Chat loading initiated for user: ${userId}`);
      }

      // Test 3: Wait for chats to load
      await new Promise(resolve => setTimeout(resolve, 2000));
      addResult(`📊 Loaded ${chats.length} chats`);
      
      if (chats.length > 0) {
        chats.forEach((chat, index) => {
          addResult(`  Chat ${index + 1}: ${chat.id} (${chat.participantIds.length} participants)`);
        });
      }

      // Test 4: Test Chat Creation (if we have other users)
      addResult('🔍 Testing chat creation...');
      
      // For testing, we'll try to create a chat with a test user
      const testParticipants = [userId, 'test-user-123'];
      
      try {
        const chatId = await createChat({
          participantIds: testParticipants,
          isGroupChat: false,
          initialMessage: 'Test chat created successfully!'
        });
        addResult(`✅ Chat created successfully: ${chatId}`);
      } catch (error) {
        addResult(`❌ Chat creation failed: ${error.message}`);
      }

      addResult('🎉 Chat tests completed!');
      
    } catch (error) {
      addResult(`❌ Test error: ${error.message}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  const runSubscriptionTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    
    try {
      addResult('📧 Starting Subscription System Tests...');
      
      // Test subscription service
      const testEmail = `test-${Date.now()}@example.com`;
      addResult(`Testing subscription with email: ${testEmail}`);
      
      const result = await subscriptionService.subscribe({
        email: testEmail,
        source: 'test-page'
      });
      
      if (result.success) {
        addResult('✅ Subscription test successful!');
        addResult(`Message: ${result.message}`);
      } else {
        addResult(`❌ Subscription test failed: ${result.message}`);
      }

      // Test email notification
      addResult('📬 Testing email notification system...');
      await emailNotificationService.testEmail();
      addResult('✅ Email notification test sent!');
      
      addResult('🎉 Subscription tests completed!');
      
    } catch (error) {
      addResult(`❌ Test error: ${error.message}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-8">
            Fetchly System Tests
          </h1>

          {/* User Info */}
          <div className="bg-gray-50 rounded-xl p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Current User</h2>
            {user ? (
              <div className="space-y-2">
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>UID:</strong> {user.uid}</p>
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Name:</strong> {user.displayName || user.name || 'Not set'}</p>
              </div>
            ) : (
              <p className="text-red-600">Not authenticated - please sign in first</p>
            )}
          </div>

          {/* Test Buttons */}
          <div className="flex flex-wrap gap-4 mb-8">
            <button
              onClick={runChatTests}
              disabled={isRunningTests || !user}
              className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunningTests ? 'Running...' : 'Test Chat System'}
            </button>
            
            <button
              onClick={runSubscriptionTests}
              disabled={isRunningTests}
              className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunningTests ? 'Running...' : 'Test Subscription System'}
            </button>
            
            <button
              onClick={clearResults}
              disabled={isRunningTests}
              className="px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Results
            </button>
          </div>

          {/* Current Chat Status */}
          <div className="bg-blue-50 rounded-xl p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Chat System Status</h2>
            <div className="space-y-2">
              <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
              <p><strong>Chats Loaded:</strong> {chats.length}</p>
              {chats.length > 0 && (
                <div className="mt-4">
                  <h3 className="font-semibold mb-2">Chat List:</h3>
                  <div className="space-y-2">
                    {chats.map((chat, index) => (
                      <div key={chat.id} className="bg-white p-3 rounded-lg border">
                        <p><strong>Chat {index + 1}:</strong> {chat.id}</p>
                        <p><strong>Participants:</strong> {chat.participantIds.join(', ')}</p>
                        <p><strong>Last Message:</strong> {chat.lastMessage || 'No messages'}</p>
                        <p><strong>Updated:</strong> {chat.updatedAt.toLocaleString()}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p>No tests run yet. Click a test button above to start.</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
