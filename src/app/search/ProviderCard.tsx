'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api-client';
import {
  Star,
  MapPin,
  Calendar,
  Phone,
  MessageCircle,
  Mail,
  Loader2,
  X,
  User,
  AlertCircle,
  Heart,
  Share2,
  Verified,
  Award,
  PawPrint,
  Clock
} from 'lucide-react';

export interface Pet {
  id: string;
  name: string;
  type: string;
  breed: string;
}

export interface Provider {
  id: string;
  businessName: string;
  serviceType: string;
  price?: number;
  profilePhoto?: string;
  rating?: number;
  reviewCount?: number;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  verified?: boolean;
  featured?: boolean;
  ownerName?: string;
  phone?: string;
  email?: string;
  bio?: string;
  services?: string[];
}

interface ProviderCardProps {
  provider: Provider;
  showSimpleCard: boolean;
}

export default function ProviderCard({ provider, showSimpleCard }: ProviderCardProps) {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [bookingDate, setBookingDate] = useState('');
  const [bookingTime, setBookingTime] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedPetId, setSelectedPetId] = useState('');
  const [selectedDuration, setSelectedDuration] = useState(60); // Default 60 minutes
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bookingError, setBookingError] = useState('');
  const [pets, setPets] = useState<Pet[]>([]);
  const [isLoadingPets, setIsLoadingPets] = useState(false);

  // Available time slots
  const timeSlots = [
    '09:00', '10:00', '11:00', '12:00', 
    '13:00', '14:00', '15:00', '16:00'
  ];

  // Available durations in minutes
  const durations = [
    { value: 30, label: '30 min' },
    { value: 60, label: '1 hour' },
    { value: 90, label: '1.5 hours' },
    { value: 120, label: '2 hours' },
  ];

  // Fetch user's pets when modal opens
  useEffect(() => {
    const fetchPets = async () => {
      if (showBookingModal && isAuthenticated) {
        try {
          setIsLoadingPets(true);
          const data = await apiClient.get('/api/pets');
          setPets(data.pets || []);
          if (data.pets?.length > 0) {
            setSelectedPetId(data.pets[0].id);
          }
        } catch (error) {
          console.error('Error fetching pets:', error);
          toast.error('Failed to load your pets');
        } finally {
          setIsLoadingPets(false);
        }
      }
    };

    fetchPets();
  }, [showBookingModal, isAuthenticated]);

  const handleBookNow = () => {
    if (!isAuthenticated) {
      router.push(`/auth/signin?redirect=/search`);
      return;
    }
    setShowBookingModal(true);
  };

  const handleBookingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!bookingDate || !bookingTime) {
      setBookingError('Please select both date and time');
      return;
    }

    if (!selectedPetId && pets.length > 0) {
      setBookingError('Please select a pet for this booking');
      return;
    }

    // If no pets, we'll create a default one
    const selectedPet = pets.find(pet => pet.id === selectedPetId) || {
      id: 'default-pet-id',
      name: 'My Pet',
      type: 'Dog',
      breed: 'Mixed'
    };

    setIsSubmitting(true);
    setBookingError('');

    try {
      const data = await apiClient.post('/api/bookings', {
        providerId: provider.id,
        serviceId: provider.serviceType.toLowerCase(),
        serviceName: provider.serviceType,
        petId: selectedPet.id,
        petName: selectedPet.name,
        scheduledDate: bookingDate,
        scheduledTime: bookingTime,
        duration: selectedDuration,
        totalPrice: provider.price || 0,
        notes,
        providerName: provider.businessName,
        providerPhoto: provider.profilePhoto,
        userId: user?.id,
        userName: user?.name,
        userEmail: user?.email,
      });

      toast.success('Booking request sent successfully!');
      setShowBookingModal(false);
      
      // Reset form
      setBookingDate('');
      setBookingTime('');
      setNotes('');
      
    } catch (error) {
      console.error('Error creating booking:', error);
      setBookingError(
        error instanceof Error 
          ? error.message 
          : 'Failed to create booking. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const BookingModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b sticky top-0 bg-white z-10">
          <h3 className="text-xl font-bold">Book Appointment</h3>
          <button 
            onClick={() => !isSubmitting && setShowBookingModal(false)}
            className={`text-gray-400 hover:text-gray-600 ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={isSubmitting}
            type="button"
          >
            <X className="w-6 h-6" />
          </button>
        </div>
        
        <div className="p-6">
          {bookingError && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg text-sm flex items-start">
              <AlertCircle className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
              <span>{bookingError}</span>
            </div>
          )}

          <form onSubmit={handleBookingSubmit} className="space-y-5">
            {/* Pet Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Pet
              </label>
              {isLoadingPets ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="animate-spin h-5 w-5 text-gray-500" />
                  <span className="ml-2 text-gray-600">Loading pets...</span>
                </div>
              ) : pets.length > 0 ? (
                <select
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  value={selectedPetId}
                  onChange={(e) => setSelectedPetId(e.target.value)}
                  required
                >
                  {pets.map((pet) => (
                    <option key={pet.id} value={pet.id}>
                      {pet.name} ({pet.type})
                    </option>
                  ))}
                </select>
              ) : (
                <div className="p-3 border border-gray-200 rounded-lg bg-gray-50 text-sm text-gray-600">
                  <PawPrint className="w-4 h-4 inline mr-1" />
                  No pets found. 
                  <button 
                    type="button"
                    onClick={() => router.push('/pets/add')}
                    className="text-green-600 hover:text-green-700 font-medium ml-1"
                  >
                    Add a pet
                  </button>
                </div>
              )}
            </div>

            {/* Date Picker */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date
              </label>
              <div className="relative">
                <input
                  type="date"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  min={new Date().toISOString().split('T')[0]}
                  value={bookingDate}
                  onChange={(e) => setBookingDate(e.target.value)}
                  required
                  disabled={isSubmitting}
                />
                <Calendar className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>
            </div>

            {/* Time Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time
              </label>
              <select
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                value={bookingTime}
                onChange={(e) => setBookingTime(e.target.value)}
                required
                disabled={isSubmitting}
              >
                <option value="">Select a time</option>
                {timeSlots.map((time) => (
                  <option key={time} value={time}>
                    {time}
                  </option>
                ))}
              </select>
            </div>

            {/* Duration Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Duration
              </label>
              <div className="grid grid-cols-2 gap-3">
                {durations.map((duration) => (
                  <button
                    key={duration.value}
                    type="button"
                    className={`p-3 border rounded-lg text-center transition-colors ${
                      selectedDuration === duration.value
                        ? 'border-green-500 bg-green-50 text-green-700'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={() => setSelectedDuration(duration.value)}
                    disabled={isSubmitting}
                  >
                    <div className="flex items-center justify-center">
                      <Clock className="w-4 h-4 mr-1.5" />
                      <span>{duration.label}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Price Display */}
            {provider.price !== undefined && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Estimated Price:</span>
                  <span className="font-semibold text-lg">
                    ${provider.price.toFixed(2)}
                  </span>
                </div>
              </div>
            )}

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Special Requests (Optional)
              </label>
              <textarea
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                rows={3}
                placeholder="Any special instructions or notes for the provider"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                disabled={isSubmitting}
              />
            </div>

            <div className="pt-2">
              <button
                type="submit"
                className="w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center"
                disabled={isSubmitting || (pets.length === 0 && !isLoadingPets)}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="animate-spin h-5 w-5 mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Calendar className="w-5 h-5 mr-2" />
                    {provider.price !== undefined 
                      ? `Book for $${provider.price.toFixed(2)}` 
                      : 'Book Appointment'}
                  </>
                )}
              </button>
              
              {pets.length === 0 && !isLoadingPets && (
                <p className="mt-2 text-sm text-gray-500 text-center">
                  You need to add a pet before booking
                </p>
              )}
              
              <button
                type="button"
                onClick={() => setShowBookingModal(false)}
                className="w-full mt-3 py-2.5 text-sm text-gray-600 hover:text-gray-800 font-medium"
                disabled={isSubmitting}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );

  // Simple card for non-logged users
  if (showSimpleCard) {
    return (
      <div className="relative">
        {showBookingModal && <BookingModal />}
        
        <div className="backdrop-blur-xl bg-white/90 border border-green-200/30 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
          {/* Provider Image & Name */}
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center shadow-lg">
              {provider.profilePhoto ? (
                <Image
                  src={provider.profilePhoto}
                  alt={provider.businessName}
                  width={64}
                  height={64}
                  className="w-16 h-16 rounded-2xl object-cover"
                />
              ) : (
                <span className="text-white font-bold text-lg">
                  {provider.businessName?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                </span>
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-bold text-lg text-gray-900 group-hover:text-green-600 transition-colors">
                {provider.businessName}
              </h3>
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(provider.rating || 0)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
                <span className="text-sm text-gray-600 ml-1">
                  {provider.rating?.toFixed(1) || 'New'}
                </span>
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="flex items-center gap-2 mb-3">
            <MapPin className="w-4 h-4 text-green-500" />
            <span className="text-gray-700">{provider.city}, {provider.state} {provider.zipCode}</span>
          </div>

          {/* Type of Service */}
          <div className="mb-3">
            <span className="px-3 py-1 bg-gradient-to-r from-green-100 to-blue-100 text-green-700 rounded-full text-sm font-medium">
              {provider.serviceType}
            </span>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-2">
            <button
              onClick={handleBookNow}
              className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
            >
              <Calendar className="w-4 h-4" />
              Book Now
            </button>
            <a
              href={`/provider/public/${provider.id}`}
              className="w-full text-center bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 py-3 px-4 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2"
            >
              <User className="w-4 h-4" />
              View Profile
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Full card for logged-in users
  return (
    <div className="relative">
      {showBookingModal && <BookingModal />}
      
      <div className="backdrop-blur-xl bg-white/90 border border-green-200/30 rounded-3xl p-6 hover:shadow-2xl hover:bg-white/95 transition-all duration-300 group hover:scale-[1.02]">
        {/* Header with provider info */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center shadow-lg">
              {provider.profilePhoto ? (
                <Image
                  src={provider.profilePhoto}
                  alt={provider.businessName}
                  width={64}
                  height={64}
                  className="w-16 h-16 rounded-2xl object-cover"
                />
              ) : (
                <span className="text-white font-bold text-lg">
                  {provider.businessName?.split(' ').map((n: string) => n[0]).join('') || 'P'}
                </span>
              )}
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-xl font-bold text-gray-900 group-hover:text-green-700 transition-colors">
                  {provider.businessName}
                </h3>
                {provider.verified && (
                  <Verified className="w-5 h-5 text-green-500" />
                )}
                {provider.featured && (
                  <Award className="w-5 h-5 text-yellow-500" />
                )}
              </div>
              <p className="text-gray-600 font-medium">{provider.ownerName}</p>
              <p className="text-sm text-gray-500">{provider.serviceType}</p>
            </div>
          </div>

          <div className="flex gap-2">
            <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
              <Heart className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <Share2 className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Rating and Reviews */}
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(provider.rating || 0)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
            <span className="ml-1 text-gray-700 font-medium">
              {provider.rating?.toFixed(1) || 'New'}
            </span>
            <span className="text-gray-400 ml-1">•</span>
            <a href="#reviews" className="text-green-600 hover:underline">
              {provider.reviewCount || 0} reviews
            </a>
          </div>
        </div>

        {/* Location and Contact */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center gap-2 text-gray-700">
            <MapPin className="w-4 h-4 text-green-500 flex-shrink-0" />
            <p>{provider.address}, {provider.city}, {provider.state} {provider.zipCode}</p>
          </div>
          
          {provider.phone && (
            <div className="flex items-center gap-2 text-gray-700">
              <Phone className="w-4 h-4 text-green-500 flex-shrink-0" />
              <a href={`tel:${provider.phone}`} className="hover:underline">
                {provider.phone}
              </a>
            </div>
          )}
          
          {provider.email && (
            <div className="flex items-center gap-2 text-gray-700">
              <Mail className="w-4 h-4 text-green-500 flex-shrink-0" />
              <a href={`mailto:${provider.email}`} className="hover:underline">
                {provider.email}
              </a>
            </div>
          )}
        </div>

        {/* Description */}
        <p className="text-gray-600 mb-6 line-clamp-3">
          {provider.bio || 'No description available.'}
        </p>

        {/* Services */}
        <div className="mb-6">
          <h4 className="font-semibold text-gray-800 mb-2">Services</h4>
          <div className="flex flex-wrap gap-2">
            {provider.services?.map((service: string, i: number) => (
              <span key={i} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                {service}
              </span>
            )) || (
              <p className="text-gray-500">No services listed</p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3">
          <div className="flex gap-3">
            <button 
              onClick={handleBookNow}
              className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
            >
              <Calendar className="w-5 h-5" />
              Book Appointment
            </button>
            <button className="p-3 bg-white border border-gray-200 hover:bg-gray-50 text-gray-700 rounded-xl transition-colors">
              <MessageCircle className="w-5 h-5" />
            </button>
          </div>
          <a
            href={`/provider/public/${provider.id}`}
            className="w-full text-center bg-white border border-gray-200 hover:bg-gray-50 text-gray-700 py-3 px-4 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2"
          >
            <User className="w-4 h-4" />
            View Full Profile
          </a>
        </div>
      </div>
    </div>
  );
}
