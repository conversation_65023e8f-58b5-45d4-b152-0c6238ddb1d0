import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { AuthProvider } from "@/contexts/AuthContext";
import { DataProvider } from "@/contexts/DataContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { QuickChatButton } from "@/components/chat/ChatInterface";
import { MobileNavigation } from "@/components/layout/MobileNavigation";
import OnlineStatusProvider from "@/components/OnlineStatusProvider";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const nunito = Nunito({
  subsets: ["latin"],
  variable: "--font-nunito",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Fetchly - Pet Services Ecosystem",
  description: "Find the perfect care for your pet. Book grooming, vet appointments, pet hotels and more.",
  keywords: ["pet services", "pet grooming", "veterinary", "pet hotels", "pet care"],
  authors: [{ name: "Fetchly Team" }],
  creator: "Fetchly",
  publisher: "Fetchly",
  icons: {
    icon: "/favicon.png",
    shortcut: "/favicon.png",
    apple: "/favicon.png",
  },
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://fetchly.app"),
  openGraph: {
    title: "Fetchly - Pet Services Ecosystem",
    description: "Find the perfect care for your pet. Book grooming, vet appointments, pet hotels and more.",
    url: "https://fetchly.app",
    siteName: "Fetchly",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Fetchly - Pet Services Ecosystem",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Fetchly - Pet Services Ecosystem",
    description: "Find the perfect care for your pet. Book grooming, vet appointments, pet hotels and more.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${nunito.variable}`} suppressHydrationWarning>
      <body className="font-sans antialiased min-h-screen bg-gradient-main" suppressHydrationWarning>
        <AuthProvider>
          <DataProvider>
            <NotificationProvider>
              <OnlineStatusProvider>
                <div className="relative min-h-screen flex flex-col">
                  <Header />
                  <main className="flex-1 pb-16 lg:pb-0">
                    {children}
                  </main>
                  <Footer />
                  <MobileNavigation />
                  <QuickChatButton />
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: 'var(--glass-bg)',
                      backdropFilter: 'blur(12px)',
                      border: '1px solid var(--glass-border)',
                      borderRadius: '1rem',
                      color: 'var(--foreground)',
                      fontFamily: 'var(--font-sans)',
                      boxShadow: 'var(--glass-shadow)',
                    },
                    success: {
                      iconTheme: {
                        primary: 'var(--accent)',
                        secondary: 'var(--text-white)',
                      },
                    },
                    error: {
                      iconTheme: {
                        primary: '#ef4444',
                        secondary: 'var(--text-white)',
                      },
                    },
                  }}
                />
              </div>
              </OnlineStatusProvider>
            </NotificationProvider>
          </DataProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
