'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminRedirect() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      console.log('Admin redirect - User:', user?.email, 'Role:', user?.role);

      if (!user) {
        // Not authenticated, redirect to admin sign-in
        console.log('No user found, redirecting to admin signin');
        router.push('/admin/signin');
      } else if (user.role === 'admin') {
        // Authenticated admin, redirect to dashboard
        console.log('Admin user found, redirecting to admin dashboard');
        router.push('/admin/dashboard');
      } else {
        // Not an admin, redirect to regular dashboard
        console.log('Non-admin user found, redirecting to regular dashboard');
        router.push('/dashboard');
      }
    }
  }, [user, isLoading, router]);

  // Show loading state while checking authentication
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
      <div className="glass-card p-8 text-center">
        <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-600">Checking admin access...</p>
      </div>
    </div>
  );
}
