'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminRedirect() {
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    // Force redirect to admin signin immediately
    console.log('ADMIN ROUTE ACCESSED - Current user:', user?.email, 'Role:', user?.role);

    // Always redirect to admin signin first, regardless of current user state
    router.push('/admin/signin');
  }, [router]);

  // Show loading state while checking authentication
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
      <div className="glass-card p-8 text-center">
        <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-600">Checking admin access...</p>
      </div>
    </div>
  );
}
