'use client';

import { useState } from 'react';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { Shield, Eye, EyeOff, AlertCircle } from 'lucide-react';

export default function AdminPortal() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      console.log('🔐 Admin login attempt');

      // ADMIN-ONLY AUTHENTICATION - NO PROFILES NEEDED
      // Check credentials against environment variables
      const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
      const adminPassword = process.env.NEXT_PUBLIC_ADMIN_PASSWORD || 'P0l@r!$2025';

      if (email === adminEmail && password === adminPassword) {
        console.log('✅ Admin credentials verified against environment variables');

        // Try Firebase Auth but don't require it
        try {
          await signInWithEmailAndPassword(auth, email, password);
          console.log('✅ Firebase Auth also successful');
        } catch (authError) {
          console.log('⚠️ Firebase Auth failed, but continuing with admin access:', authError);
          // Continue anyway - we don't need Firebase Auth for admin
        }

        console.log('🎉 ADMIN ACCESS GRANTED - NO PROFILE REQUIRED');
        setIsAuthenticated(true);
        setError('');

      } else {
        console.log('❌ Invalid admin credentials');
        setError('Invalid admin credentials.');
      }

    } catch (error: any) {
      console.error('❌ Admin login error:', error);
      setError('Admin authentication failed.');
    } finally {
      setIsLoading(false);
    }
  };

  // Admin Dashboard Component
  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
        {/* Header */}
        <div className="bg-gradient-to-r from-gray-900 to-blue-900 text-white shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-white/10 rounded-lg">
                  <Shield className="w-8 h-8" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Admin Dashboard</h1>
                  <p className="text-gray-300">Fetchly Administration Portal</p>
                </div>
              </div>
              <button
                onClick={() => {
                  auth.signOut();
                  setIsAuthenticated(false);
                }}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Stats Cards */}
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
              <p className="text-3xl font-bold text-blue-600 mt-2">1,247</p>
            </div>
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-gray-900">Active Providers</h3>
              <p className="text-3xl font-bold text-green-600 mt-2">89</p>
            </div>
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-gray-900">Monthly Bookings</h3>
              <p className="text-3xl font-bold text-purple-600 mt-2">156</p>
            </div>
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-gray-900">Revenue</h3>
              <p className="text-3xl font-bold text-yellow-600 mt-2">$12,450</p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="glass-card p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Admin Controls</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <button className="p-4 bg-blue-100 hover:bg-blue-200 rounded-lg text-blue-800 font-medium transition-colors">
                Manage Users
              </button>
              <button className="p-4 bg-green-100 hover:bg-green-200 rounded-lg text-green-800 font-medium transition-colors">
                View Bookings
              </button>
              <button className="p-4 bg-purple-100 hover:bg-purple-200 rounded-lg text-purple-800 font-medium transition-colors">
                Provider Management
              </button>
              <button className="p-4 bg-yellow-100 hover:bg-yellow-200 rounded-lg text-yellow-800 font-medium transition-colors">
                Financial Reports
              </button>
              <button className="p-4 bg-pink-100 hover:bg-pink-200 rounded-lg text-pink-800 font-medium transition-colors">
                Content Moderation
              </button>
              <button className="p-4 bg-indigo-100 hover:bg-indigo-200 rounded-lg text-indigo-800 font-medium transition-colors">
                Analytics
              </button>
              <button className="p-4 bg-red-100 hover:bg-red-200 rounded-lg text-red-800 font-medium transition-colors">
                System Settings
              </button>
              <button className="p-4 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-800 font-medium transition-colors">
                Support Tickets
              </button>
            </div>
          </div>

          {/* Success Message */}
          <div className="mt-8 p-6 bg-green-100 border border-green-300 rounded-xl">
            <div className="flex items-center space-x-3">
              <Shield className="w-8 h-8 text-green-600" />
              <div>
                <h3 className="text-lg font-bold text-green-800">✅ Admin Dashboard Active</h3>
                <p className="text-green-700">You have successfully accessed the Fetchly admin portal.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Login Form
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl shadow-lg">
              <Shield className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Admin Portal</h1>
          <p className="text-gray-600">Secure access to Fetchly administration</p>
        </div>

        <div className="glass-card p-8">
          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin Email
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white/80 backdrop-blur-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white/80 backdrop-blur-sm"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {error && (
              <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="w-5 h-5 text-red-500" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full glass-button py-3 px-4 text-white font-semibold rounded-xl disabled:opacity-50"
            >
              {isLoading ? 'Authenticating...' : 'Access Admin Portal'}
            </button>
          </form>

          {/* Debug Info */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm text-blue-800 font-medium">Debug Information</p>
                <p className="text-xs text-blue-700 mt-1">
                  Check browser console for detailed authentication logs.
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  Expected: <EMAIL> with role "admin" in Firestore
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
