'use client';

import { useState } from 'react';
import { Shield, CheckCircle, AlertCircle, Settings } from 'lucide-react';

export default function AdminSetup() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleSetup = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok) {
        setResult({
          success: true,
          message: data.message || 'Admin user setup completed successfully!'
        });
      } else {
        setResult({
          success: false,
          message: data.error || 'Failed to setup admin user'
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error occurred while setting up admin user'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl shadow-lg">
              <Shield className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Admin Setup</h1>
          <p className="text-gray-600">Initialize the admin user for Fetchly</p>
        </div>

        {/* Setup Card */}
        <div className="glass-card p-8">
          <div className="space-y-6">
            {/* Information */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <Settings className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-blue-800 font-medium">Setup Information</p>
                  <p className="text-xs text-blue-700 mt-1">
                    This will create or update the admin user using credentials from your environment variables.
                  </p>
                </div>
              </div>
            </div>

            {/* Environment Variables Display */}
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Admin Email</label>
                <div className="px-3 py-2 bg-gray-100 rounded-lg text-sm text-gray-600">
                  {process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Admin Password</label>
                <div className="px-3 py-2 bg-gray-100 rounded-lg text-sm text-gray-600">
                  ••••••••••••
                </div>
              </div>
            </div>

            {/* Setup Button */}
            <button
              onClick={handleSetup}
              disabled={isLoading}
              className="w-full glass-button py-3 px-4 text-white font-semibold rounded-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Setting up admin...</span>
                </>
              ) : (
                <>
                  <Shield className="w-5 h-5" />
                  <span>Setup Admin User</span>
                </>
              )}
            </button>

            {/* Result Message */}
            {result && (
              <div className={`flex items-center space-x-2 p-3 rounded-lg ${
                result.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                {result.success ? (
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                )}
                <p className={`text-sm ${
                  result.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {result.message}
                </p>
              </div>
            )}

            {/* Success Actions */}
            {result?.success && (
              <div className="space-y-3">
                <div className="border-t border-gray-200 pt-4">
                  <p className="text-sm text-gray-600 mb-3">Next steps:</p>
                  <div className="space-y-2">
                    <a
                      href="/admin/signin"
                      className="block w-full text-center px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Go to Admin Sign In
                    </a>
                    <a
                      href="/admin/dashboard"
                      className="block w-full text-center px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg text-sm hover:from-blue-600 hover:to-blue-700 transition-colors"
                    >
                      Access Admin Dashboard
                    </a>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Warning Notice */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm text-yellow-800 font-medium">Development Only</p>
                <p className="text-xs text-yellow-700 mt-1">
                  This setup page is only available in development mode for security reasons.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            Need help? Contact{' '}
            <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
