'use client';

import React from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { ToggleLanguageSwitcher, CompactLanguageSwitcher } from '@/components/LanguageSwitcher';
import { Globe, Heart, MessageCircle, Calendar, DollarSign } from 'lucide-react';

export default function LanguageDemoPage() {
  const { 
    t, 
    language, 
    getCurrentLanguage, 
    formatDate, 
    formatCurrency,
    getNavTranslations,
    getServiceTranslations,
    getFormTranslations
  } = useTranslation();

  const currentLang = getCurrentLanguage();
  const navTranslations = getNavTranslations();
  const serviceTranslations = getServiceTranslations();
  const formTranslations = getFormTranslations();

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Globe className="w-8 h-8 text-green-600" />
            <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
              {language === 'en' ? 'Language Demo' : 'Demostración de Idioma'}
            </h1>
          </div>
          <p className="text-gray-600 mb-6">
            {language === 'en' 
              ? 'Switch between English and Spanish to see the entire website change language!'
              : '¡Cambia entre inglés y español para ver todo el sitio web cambiar de idioma!'
            }
          </p>
          
          {/* Language Switchers */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">Toggle Style</p>
              <ToggleLanguageSwitcher />
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">Compact Style</p>
              <CompactLanguageSwitcher />
            </div>
          </div>
        </div>

        {/* Current Language Info */}
        <div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">{currentLang.flag}</span>
            {language === 'en' ? 'Current Language' : 'Idioma Actual'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p><strong>{language === 'en' ? 'Language' : 'Idioma'}:</strong> {currentLang.name}</p>
              <p><strong>{language === 'en' ? 'Code' : 'Código'}:</strong> {currentLang.code}</p>
              <p><strong>{language === 'en' ? 'Flag' : 'Bandera'}:</strong> {currentLang.flag}</p>
            </div>
            <div>
              <p><strong>{language === 'en' ? 'Date Format' : 'Formato de Fecha'}:</strong> {formatDate(new Date())}</p>
              <p><strong>{language === 'en' ? 'Currency Format' : 'Formato de Moneda'}:</strong> {formatCurrency(29.99)}</p>
            </div>
          </div>
        </div>

        {/* Navigation Translations */}
        <div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <MessageCircle className="w-5 h-5 text-blue-600" />
            {t('nav.home')} - {language === 'en' ? 'Navigation' : 'Navegación'}
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(navTranslations).map(([key, value]) => (
              <div key={key} className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-500 capitalize">{key}</p>
                <p className="font-medium">{value}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Service Translations */}
        <div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <Heart className="w-5 h-5 text-red-500" />
            {language === 'en' ? 'Pet Services' : 'Servicios para Mascotas'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(serviceTranslations).map(([key, value]) => (
              <div key={key} className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                <p className="text-sm text-gray-500 capitalize">{key}</p>
                <p className="font-semibold text-green-700">{value}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Form Translations */}
        <div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <Calendar className="w-5 h-5 text-purple-600" />
            {language === 'en' ? 'Common Form Elements' : 'Elementos Comunes de Formulario'}
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {Object.entries(formTranslations).map(([key, value]) => (
              <button 
                key={key} 
                className="p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200 text-sm"
              >
                {value}
              </button>
            ))}
          </div>
        </div>

        {/* Chat System Demo */}
        <div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <MessageCircle className="w-5 h-5 text-green-600" />
            {t('chat.title')}
          </h2>
          <div className="space-y-3">
            <div className="flex gap-3">
              <button className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 rounded-lg hover:from-green-600 hover:to-blue-600 transition-all duration-300">
                {t('chat.startNewChat')}
              </button>
              <button className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all duration-300">
                {t('chat.contactSupport')}
              </button>
            </div>
            <input 
              type="text" 
              placeholder={t('chat.searchConversations')}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Newsletter Demo */}
        <div className="bg-white rounded-2xl p-6 shadow-lg">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-yellow-600" />
            {t('newsletter.title')}
          </h2>
          <p className="text-gray-600 mb-4">{t('newsletter.description')}</p>
          <div className="flex gap-3">
            <input 
              type="email" 
              placeholder={t('newsletter.placeholder')}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <button className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-2 rounded-lg hover:from-green-600 hover:to-blue-600 transition-all duration-300">
              {t('newsletter.subscribe')}
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">{t('newsletter.privacy')}</p>
        </div>

        {/* Instructions */}
        <div className="mt-8 text-center">
          <p className="text-gray-600">
            {language === 'en' 
              ? 'Use the language switchers above to see all content change instantly!'
              : '¡Usa los selectores de idioma arriba para ver todo el contenido cambiar instantáneamente!'
            }
          </p>
        </div>
      </div>
    </div>
  );
}
