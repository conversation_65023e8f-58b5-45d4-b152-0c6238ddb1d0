'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Search,
  Filter,
  Users,
  Heart,
  MessageCircle,
  Share,
  Bookmark,
  TrendingUp,
  Sparkles,
  Globe,
  Lock,
  Camera,
  Video,
  Smile,
  MapPin,
  Calendar,
  Star,
  Flame,
  Zap,
  Award,
  Gift
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import AwesomeStories from '@/components/AwesomeStories';
import CreatePost from '@/components/CreatePost';
import PostCard from '@/components/PostCard';
import toast from 'react-hot-toast';

export default function CommunityPage() {
  const { user } = useAuth();
  const { publicPosts, loading: dataLoading } = useData();
  
  // States
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [showCreatePost, setShowCreatePost] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<any[]>([]);
  const [trendingTopics, setTrendingTopics] = useState([
    { tag: '#PetCare', count: 1234, trending: true },
    { tag: '#DogTraining', count: 892, trending: true },
    { tag: '#CatLife', count: 756, trending: false },
    { tag: '#VetTips', count: 543, trending: true },
    { tag: '#PetPhotography', count: 432, trending: false }
  ]);

  // Filter options
  const filterOptions = [
    { id: 'all', label: 'All Posts', icon: Globe, color: 'from-blue-500 to-purple-600' },
    { id: 'following', label: 'Following', icon: Users, color: 'from-green-500 to-blue-500' },
    { id: 'trending', label: 'Trending', icon: TrendingUp, color: 'from-red-500 to-pink-500' },
    { id: 'photos', label: 'Photos', icon: Camera, color: 'from-purple-500 to-indigo-500' },
    { id: 'videos', label: 'Videos', icon: Video, color: 'from-orange-500 to-red-500' }
  ];

  // Mock users data
  const mockUsers = [
    { id: '1', name: 'Sarah Johnson', avatar: '/favicon.png', isOnline: true },
    { id: '2', name: 'Mike Chen', avatar: '/favicon.png', isOnline: true },
    { id: '3', name: 'Emma Wilson', avatar: '/favicon.png', isOnline: false },
    { id: '4', name: 'David Brown', avatar: '/favicon.png', isOnline: true },
    { id: '5', name: 'Lisa Garcia', avatar: '/favicon.png', isOnline: true },
  ];

  // Load online users
  useEffect(() => {
    setOnlineUsers(mockUsers);
  }, []);

  // Filter posts based on selected filter
  const filteredPosts = publicPosts.filter((post: any) => {
    const matchesSearch = post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.userName.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (!matchesSearch) return false;
    
    switch (selectedFilter) {
      case 'following':
        return user && post.userId !== user.id; // Mock following logic
      case 'trending':
        return (post.likes || 0) > 5; // Mock trending logic
      case 'photos':
        return post.image;
      case 'videos':
        return post.video || false; // Add video support later
      default:
        return true;
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      {/* Enhanced Header */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo & Title */}
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent">
                  Pet Community
                </h1>
                <p className="text-sm text-gray-500">Connect with pet lovers worldwide</p>
              </div>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search posts, people, topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 shadow-lg"
                />
              </div>
            </div>

            {/* User Actions */}
            <div className="flex items-center space-x-3">
              {user && (
                <>
                  <button
                    onClick={() => setShowCreatePost(true)}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-2xl font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center space-x-2"
                  >
                    <Sparkles className="w-5 h-5" />
                    <span>Create Post</span>
                  </button>
                  
                  <div className="relative">
                    <img
                      src={user.avatar || '/favicon.png'}
                      alt={user.name}
                      className="w-12 h-12 rounded-2xl object-cover border-2 border-white shadow-lg"
                    />
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white" />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Filter Tabs */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Filter className="w-5 h-5 text-purple-500" />
                <span>Explore</span>
              </h3>
              <div className="space-y-2">
                {filterOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <button
                      key={option.id}
                      onClick={() => setSelectedFilter(option.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl transition-all duration-300 ${
                        selectedFilter === option.id
                          ? `bg-gradient-to-r ${option.color} text-white shadow-lg transform scale-105`
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{option.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Trending Topics */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Flame className="w-5 h-5 text-red-500" />
                <span>Trending</span>
              </h3>
              <div className="space-y-3">
                {trendingTopics.map((topic, index) => (
                  <div key={topic.tag} className="flex items-center justify-between p-3 rounded-2xl hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        topic.trending ? 'bg-red-100 text-red-500' : 'bg-gray-100 text-gray-500'
                      }`}>
                        {topic.trending ? <TrendingUp className="w-4 h-4" /> : <span className="text-xs font-bold">{index + 1}</span>}
                      </div>
                      <div>
                        <p className="font-semibold text-gray-800">{topic.tag}</p>
                        <p className="text-xs text-gray-500">{topic.count.toLocaleString()} posts</p>
                      </div>
                    </div>
                    {topic.trending && <Zap className="w-4 h-4 text-yellow-500" />}
                  </div>
                ))}
              </div>
            </div>

            {/* Online Users */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Users className="w-5 h-5 text-green-500" />
                <span>Online Now</span>
                <span className="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full font-bold">
                  {onlineUsers.filter(u => u.isOnline).length}
                </span>
              </h3>
              <div className="space-y-3">
                {onlineUsers.filter(u => u.isOnline).slice(0, 5).map((onlineUser) => (
                  <div key={onlineUser.id} className="flex items-center space-x-3 p-2 rounded-2xl hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="relative">
                      <img
                        src={onlineUser.avatar || '/favicon.png'}
                        alt={onlineUser.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
                    </div>
                    <div className="flex-1">
                      <p className="font-semibold text-gray-800 text-sm">{onlineUser.name}</p>
                      <p className="text-xs text-green-500">Online now</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stories Section */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 overflow-hidden">
              <AwesomeStories />
            </div>

            {/* Create Post Section */}
            {user && (
              <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
                <CreatePost
                  placeholder="What's happening in the pet community? 🐾"
                  onPostCreated={() => {
                    toast.success('🎉 Post shared with the community!');
                  }}
                />
              </div>
            )}

            {/* Posts Feed */}
            <div className="space-y-6">
              {dataLoading ? (
                // Loading skeletons
                [...Array(3)].map((_, i) => (
                  <div key={i} className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
                    <div className="animate-pulse">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-full" />
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2" />
                          <div className="h-3 bg-gray-200 rounded w-1/6" />
                        </div>
                      </div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                      <div className="h-4 bg-gray-200 rounded w-1/2" />
                    </div>
                  </div>
                ))
              ) : filteredPosts.length > 0 ? (
                filteredPosts.map((post) => (
                  <div key={post.id} className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 overflow-hidden hover:shadow-2xl transition-all duration-300">
                    <PostCard post={post} />
                  </div>
                ))
              ) : (
                <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-12 text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl mx-auto mb-6 flex items-center justify-center">
                    <Search className="w-10 h-10 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-700 mb-2">No posts found</h3>
                  <p className="text-gray-500">Try adjusting your search or filters</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Community Stats */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Award className="w-5 h-5 text-yellow-500" />
                <span>Community</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl">
                  <div className="flex items-center space-x-3">
                    <Users className="w-5 h-5 text-blue-500" />
                    <span className="font-medium text-gray-700">Members</span>
                  </div>
                  <span className="font-bold text-blue-600">{mockUsers.length.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl">
                  <div className="flex items-center space-x-3">
                    <MessageCircle className="w-5 h-5 text-green-500" />
                    <span className="font-medium text-gray-700">Posts</span>
                  </div>
                  <span className="font-bold text-green-600">{publicPosts.length.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl">
                  <div className="flex items-center space-x-3">
                    <Heart className="w-5 h-5 text-purple-500" />
                    <span className="font-medium text-gray-700">Likes</span>
                  </div>
                  <span className="font-bold text-purple-600">
                    {publicPosts.reduce((sum: number, post: any) => sum + (post.likes || 0), 0).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Suggested Users */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Star className="w-5 h-5 text-yellow-500" />
                <span>Suggested</span>
              </h3>
              <div className="space-y-3">
                {mockUsers.slice(0, 4).map((suggestedUser) => (
                  <div key={suggestedUser.id} className="flex items-center justify-between p-3 rounded-2xl hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-3">
                      <img
                        src={suggestedUser.avatar || '/favicon.png'}
                        alt={suggestedUser.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div>
                        <p className="font-semibold text-gray-800 text-sm">{suggestedUser.name}</p>
                        <p className="text-xs text-gray-500">Pet lover</p>
                      </div>
                    </div>
                    <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300">
                      Follow
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Gift className="w-5 h-5 text-pink-500" />
                <span>Quick Actions</span>
              </h3>
              <div className="grid grid-cols-2 gap-3">
                <button className="flex flex-col items-center p-3 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl hover:from-blue-100 hover:to-purple-100 transition-colors">
                  <Camera className="w-6 h-6 text-blue-500 mb-2" />
                  <span className="text-xs font-medium text-gray-700">Photo</span>
                </button>
                <button className="flex flex-col items-center p-3 bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl hover:from-red-100 hover:to-pink-100 transition-colors">
                  <Video className="w-6 h-6 text-red-500 mb-2" />
                  <span className="text-xs font-medium text-gray-700">Video</span>
                </button>
                <button className="flex flex-col items-center p-3 bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl hover:from-green-100 hover:to-blue-100 transition-colors">
                  <MapPin className="w-6 h-6 text-green-500 mb-2" />
                  <span className="text-xs font-medium text-gray-700">Location</span>
                </button>
                <button className="flex flex-col items-center p-3 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl hover:from-yellow-100 hover:to-orange-100 transition-colors">
                  <Calendar className="w-6 h-6 text-yellow-500 mb-2" />
                  <span className="text-xs font-medium text-gray-700">Event</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
