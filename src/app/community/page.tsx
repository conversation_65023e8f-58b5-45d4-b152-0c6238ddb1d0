'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import {
  collection,
  query,
  where,
  getDocs,
  doc,
  updateDoc,
  addDoc,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  Search,
  UserPlus,
  PawPrint,
  X
} from 'lucide-react';
import Link from 'next/link';
import CreatePost from '@/components/CreatePost';
import PostCard from '@/components/PostCard';
import AwesomeStories from '@/components/AwesomeStories';
import toast from 'react-hot-toast';

// Type for the search result (currently unused but kept for future features)
// interface SearchResult {
//   id: string;
//   name: string;
//   email: string;
//   avatar?: string;
//   role?: string;
// }







export default function CommunityPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const { publicPosts, loading: dataLoading, updatePost, deletePost } = useData();

  // Debug user state
  useEffect(() => {
    console.log('🔍 Community page - User state:', { user: user?.id, isLoading });
  }, [user, isLoading]);

  // State management
  const [activeFilter] = useState('all');
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [visiblePosts, setVisiblePosts] = useState(5);
  const [searchTerm, setSearchTerm] = useState('');
  const [friends, setFriends] = useState<any[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<any[]>([]);
  const [friendRequests, setFriendRequests] = useState<any[]>([]);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [suggestedProviders, setSuggestedProviders] = useState<any[]>([]);
  const [editingPost, setEditingPost] = useState<any>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  // Filter posts based on active filter - only show public posts in community
  const filteredPosts = publicPosts.filter(post => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'friends') return post.userName && !post.userName.includes('Provider');
    if (activeFilter === 'providers') return post.userName && post.userName.includes('Provider');
    if (activeFilter === 'groups') return false; // No groups in current data structure
    return true;
  }).slice(0, user ? publicPosts.length : visiblePosts);

  // Filter posts based on search term
  const searchFilteredPosts = filteredPosts.filter(post => {
    if (!searchTerm.trim()) return true;
    return post.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           post.userName?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Check if user is logged in and set visible posts
  useEffect(() => {
    if (!isLoading && user) {
      // User is logged in, show full community
      setVisiblePosts(publicPosts.length);
    } else if (!isLoading && !user) {
      // User is not logged in, show limited posts with call-to-action
      setVisiblePosts(Math.min(5, publicPosts.length));
    }
  }, [user, isLoading, publicPosts.length]);
  // Load friends and online users
  useEffect(() => {
    if (user) {
      loadFriends();
      loadOnlineUsers();
      loadFriendRequests();
      loadSuggestedProviders();
    }

  }, [user]);

  // Search users when search term changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm.trim()) {
        searchUsers(searchTerm);
      } else {
        setSearchResults([]);
      }
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Update online status when user visits/leaves the page
  useEffect(() => {
    if (user) {
      // Set user as online when they visit
      updateOnlineStatus(true);

      // Set up interval to refresh online users every 30 seconds
      const interval = setInterval(() => {
        loadOnlineUsers();
        updateOnlineStatus(true); // Keep user marked as online
      }, 30000);

      // Set user as offline when they leave
      const handleBeforeUnload = () => {
        updateOnlineStatus(false);
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        clearInterval(interval);
        window.removeEventListener('beforeunload', handleBeforeUnload);
        updateOnlineStatus(false);
      };
    }
  }, [user]);

  const loadFriends = async () => {
    if (!user?.id) return;
    try {
      const friendshipsQuery = query(
        collection(db, 'friendships'),
        where('user1Id', '==', user.id)
      );
      const friendshipsSnapshot = await getDocs(friendshipsQuery);

      const friendIds = friendshipsSnapshot.docs.map(doc => doc.data().user2Id);

      if (friendIds.length > 0) {
        const usersQuery = query(
          collection(db, 'users'),
          where('__name__', 'in', friendIds)
        );
        const usersSnapshot = await getDocs(usersQuery);
        const friendsData = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setFriends(friendsData);
      }
    } catch (error) {
      console.error('Error loading friends:', error);
    }
  };

  const loadOnlineUsers = async () => {
    try {
      const usersQuery = query(
        collection(db, 'users'),
        where('isOnline', '==', true),
        limit(20)
      );
      const usersSnapshot = await getDocs(usersQuery);
      const onlineUsersData = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      // Filter out current user from online users list
      const filteredOnlineUsers = onlineUsersData.filter(onlineUser => onlineUser.id !== user?.id);
      setOnlineUsers(filteredOnlineUsers);
    } catch (error) {
      console.error('Error loading online users:', error);
    }
  };

  // Update user's online status
  const updateOnlineStatus = async (isOnline: boolean) => {
    if (!user?.id) return;
    try {
      await updateDoc(doc(db, 'users', user.id), {
        isOnline,
        lastSeen: new Date()
      });
    } catch (error) {
      console.error('Error updating online status:', error);
    }
  };

  const loadFriendRequests = async () => {
    if (!user?.id) return;
    try {
      const requestsQuery = query(
        collection(db, 'friendRequests'),
        where('toUserId', '==', user.id),
        where('status', '==', 'pending')
      );
      const requestsSnapshot = await getDocs(requestsQuery);
      const requestsData = requestsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setFriendRequests(requestsData);
    } catch (error) {
      console.error('Error loading friend requests:', error);
    }
  };



  const loadSuggestedProviders = async () => {
    if (!user?.location) return;
    try {
      const providersQuery = query(
        collection(db, 'providers'),
        where('location.city', '==', user.city || ''),
        where('status', '==', 'approved'),
        limit(5)
      );
      const providersSnapshot = await getDocs(providersQuery);
      const providersData = providersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setSuggestedProviders(providersData);
    } catch (error) {
      console.error('Error loading suggested providers:', error);
    }
  };

  const searchUsers = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }
    try {
      const usersQuery = query(
        collection(db, 'users'),
        orderBy('name'),
        limit(10)
      );
      const usersSnapshot = await getDocs(usersQuery);
      const users = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as any[];

      const filtered = users.filter((userData: any) =>
        userData.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        userData.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );

      // For providers, get their profile photos from the providers collection
      const enrichedResults = await Promise.all(
        filtered.map(async (userData: any) => {
          if (userData.role === 'provider') {
            try {
              // Get provider profile photo
              const providerQuery = query(
                collection(db, 'providers'),
                where('userId', '==', userData.id),
                limit(1)
              );
              const providerSnapshot = await getDocs(providerQuery);
              if (!providerSnapshot.empty) {
                const providerData = providerSnapshot.docs[0].data();
                return {
                  ...userData,
                  profilePhoto: providerData.profilePhoto || '/favicon.ico'
                };
              }
            } catch (error) {
              console.error('Error fetching provider photo:', error);
            }
          }
          return userData;
        })
      );

      setSearchResults(enrichedResults);
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    }
  };

  const sendFriendRequest = async (toUserId: string) => {
    if (!user) return;
    try {
      await addDoc(collection(db, 'friendRequests'), {
        fromUserId: user.id,
        toUserId,
        status: 'pending',
        createdAt: new Date()
      });
      toast.success('Friend request sent!');
    } catch (error) {
      console.error('Error sending friend request:', error);
      toast.error('Failed to send friend request');
    }
  };

  const acceptFriendRequest = async (requestId: string, fromUserId: string) => {
    if (!user) return;
    try {
      // Update request status
      await updateDoc(doc(db, 'friendRequests', requestId), {
        status: 'accepted'
      });

      // Create friendship
      await addDoc(collection(db, 'friendships'), {
        user1Id: user.id,
        user2Id: fromUserId,
        createdAt: new Date()
      });

      // Create reverse friendship
      await addDoc(collection(db, 'friendships'), {
        user1Id: fromUserId,
        user2Id: user.id,
        createdAt: new Date()
      });

      toast.success('Friend request accepted!');
      loadFriends();
      loadFriendRequests();
    } catch (error) {
      console.error('Error accepting friend request:', error);
      toast.error('Failed to accept friend request');
    }
  };

  const declineFriendRequest = async (requestId: string) => {
    try {
      await updateDoc(doc(db, 'friendRequests', requestId), {
        status: 'declined'
      });
      toast.success('Friend request declined');
      loadFriendRequests();
    } catch (error) {
      console.error('Error declining friend request:', error);
      toast.error('Failed to decline friend request');
    }
  };

  // Handle edit post
  const handleEditPost = (post: any) => {
    setEditingPost(post);
    setShowEditModal(true);
  };

  // Handle delete post
  const handleDeletePost = async (postId: string) => {
    if (!confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
      return;
    }

    try {
      await deletePost(postId);
      toast.success('Post deleted successfully');
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('Failed to delete post');
    }
  };





  // Handle scroll for non-logged users (currently unused but kept for future features)
  // const handleScroll = () => {
  //   if (!user && visiblePosts >= 5) {
  //     setShowLoginPrompt(true);
  //   }
  // };







  // Render Left Sidebar
  const renderLeftSidebar = () => (
    <div className="w-80 space-y-6">
      {/* User Profile Quick Access */}
      {user && (
        <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-full overflow-hidden">
              <img
                src={user.avatar || '/favicon.png'}
                alt={user.name || 'User'}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  const nextSibling = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextSibling) nextSibling.style.display = 'flex';
                }}
              />
              <div className="w-full h-full bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center" style={{ display: 'none' }}>
                <span className="text-white font-bold text-sm">
                  {user.name?.charAt(0) || 'U'}
                </span>
              </div>
            </div>
            <div>
              <h3 className="font-bold text-gray-800">{user.name}</h3>
              <p className="text-sm text-gray-600">{user.role === 'provider' ? 'Provider' : 'Pet Owner'}</p>
            </div>
          </div>
          <Link
            href={user.role === 'provider' ? '/provider/dashboard' : '/dashboard'}
            className="block w-full text-center py-2 bg-gradient-to-r from-green-100 to-blue-100 hover:from-green-200 hover:to-blue-200 text-green-700 rounded-lg transition-colors"
          >
            Go to Dashboard
          </Link>
        </div>
      )}


      {/* Search Friends */}
      <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
        <h3 className="font-bold text-gray-800 mb-4">Find Friends</h3>
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search for friends..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
        {searchTerm && (
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {searchResults.length > 0 ? (
              searchResults.map(searchUser => (
                <div key={searchUser.id} className="flex items-center gap-3 p-2 hover:bg-white/50 rounded-lg cursor-pointer">
                  <div
                    className="flex items-center gap-3 flex-1 cursor-pointer"
                    onClick={() => {
                      console.log('🔍 Clicking on user profile:', {
                        id: searchUser.id,
                        name: searchUser.name,
                        role: searchUser.role
                      });

                      if (searchUser.role === 'provider') {
                        console.log('🏢 Navigating to provider profile:', `/provider/public/${searchUser.id}`);
                        router.push(`/provider/public/${searchUser.id}`);
                      } else {
                        console.log('🐕 Navigating to pet owner profile:', `/profile?id=${searchUser.id}`);
                        router.push(`/profile?id=${searchUser.id}`);
                      }
                    }}
                  >
                    <div className="w-8 h-8 rounded-full overflow-hidden">
                      <img
                        src={
                          searchUser.role === 'provider'
                            ? (searchUser.profilePhoto || '/favicon.ico')
                            : (searchUser.avatar || '/favicon.png')
                        }
                        alt={searchUser.name || 'User'}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback to initials if image fails to load
                          e.currentTarget.style.display = 'none';
                          const nextSibling = e.currentTarget.nextElementSibling as HTMLElement;
                          if (nextSibling) nextSibling.style.display = 'flex';
                        }}
                      />
                      <div className="w-full h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center" style={{ display: 'none' }}>
                        <span className="text-white font-bold text-xs">
                          {searchUser.name?.charAt(0) || 'U'}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-cool-800 text-sm">{searchUser.name}</h4>
                      <p className="text-xs text-cool-600">{searchUser.role === 'provider' ? 'Provider' : 'Pet Owner'}</p>
                    </div>
                  </div>
                  {searchUser.id !== user?.id && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        sendFriendRequest(searchUser.id);
                      }}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      <UserPlus className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">No users found</p>
            )}
          </div>
        )}
      </div>

      {/* My Friends */}
      <div className="glass-card rounded-xl p-4">
        <h3 className="font-bold text-cool-800 mb-4">My Friends</h3>
        <div className="space-y-3">
          {friends.length > 0 ? friends.slice(0, 5).map(friend => (
            <div
              key={friend.id}
              className="flex items-center gap-3 p-2 hover:bg-white/50 rounded-lg cursor-pointer"
              onClick={() => {
                console.log('👥 Clicking on friend profile:', {
                  id: friend.id,
                  name: friend.name,
                  role: friend.role
                });

                if (friend.role === 'provider') {
                  console.log('🏢 Navigating to friend provider profile:', `/provider/public/${friend.id}`);
                  router.push(`/provider/public/${friend.id}`);
                } else {
                  console.log('🐕 Navigating to friend pet owner profile:', `/profile?id=${friend.id}`);
                  router.push(`/profile?id=${friend.id}`);
                }
              }}
            >
              <div className="relative">
                <div className="w-10 h-10 rounded-full overflow-hidden">
                  <img
                    src={friend.avatar || '/favicon.png'}
                    alt={friend.displayName || 'Friend'}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      const nextSibling = e.currentTarget.nextElementSibling as HTMLElement;
                      if (nextSibling) nextSibling.style.display = 'flex';
                    }}
                  />
                  <div className="w-full h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center" style={{ display: 'none' }}>
                    <span className="text-white font-bold text-sm">
                      {friend.displayName?.charAt(0) || 'U'}
                    </span>
                  </div>
                </div>
                {friend.isOnline && (
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                )}
              </div>
              <div>
                <h4 className="font-medium text-cool-800 text-sm">{friend.displayName}</h4>
                <p className="text-xs text-cool-600">{friend.isOnline ? 'Online' : 'Offline'}</p>
              </div>
            </div>
          )) : (
            <p className="text-sm text-gray-500">No friends yet. Start connecting!</p>
          )}
        </div>
      </div>
    </div>
  );

  // Render Right Sidebar
  const renderRightSidebar = () => (
    <div className="w-80 space-y-6">
      {/* Online Users */}
      {user && (
        <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
          <h3 className="font-bold text-gray-800 mb-4">Online Users</h3>
          <div className="space-y-3">
            {onlineUsers.length > 0 ? onlineUsers.slice(0, 8).map(onlineUser => (
              <div key={onlineUser.id} className="flex items-center gap-3 p-2 hover:bg-white/50 rounded-lg cursor-pointer">
                <div
                  className="flex items-center gap-3 flex-1 cursor-pointer"
                  onClick={() => {
                    console.log('🔍 Clicking on online user profile:', {
                      id: onlineUser.id,
                      name: onlineUser.name,
                      role: onlineUser.role
                    });

                    if (onlineUser.role === 'provider') {
                      console.log('🏢 Navigating to online provider profile:', `/provider/public/${onlineUser.id}`);
                      router.push(`/provider/public/${onlineUser.id}`);
                    } else {
                      console.log('🐕 Navigating to online pet owner profile:', `/profile?id=${onlineUser.id}`);
                      router.push(`/profile?id=${onlineUser.id}`);
                    }
                  }}
                >
                  <div className="relative">
                    <div className="w-10 h-10 rounded-full overflow-hidden">
                      <img
                        src={onlineUser.avatar || '/favicon.png'}
                        alt={onlineUser.displayName || 'User'}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          const nextSibling = e.currentTarget.nextElementSibling as HTMLElement;
                          if (nextSibling) nextSibling.style.display = 'flex';
                        }}
                      />
                      <div className="w-full h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center" style={{ display: 'none' }}>
                        <span className="text-white font-bold text-sm">
                          {onlineUser.displayName?.charAt(0) || 'U'}
                        </span>
                      </div>
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-cool-800 text-sm">{onlineUser.displayName}</h4>
                    <p className="text-xs text-cool-600">{onlineUser.role === 'provider' ? 'Provider' : 'Pet Owner'}</p>
                  </div>
                </div>
                {onlineUser.id !== user.id && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      sendFriendRequest(onlineUser.id);
                    }}
                    className="text-primary-600 hover:text-primary-700"
                  >
                    <UserPlus className="w-4 h-4" />
                  </button>
                )}
              </div>
            )) : (
              <p className="text-sm text-gray-500">No users online</p>
            )}
          </div>
        </div>
      )}

      {/* Friend Requests */}
      {user && friendRequests.length > 0 && (
        <div className="glass-card rounded-xl p-4">
          <h3 className="font-bold text-cool-800 mb-4">Friend Requests</h3>
          <div className="space-y-3">
            {friendRequests.slice(0, 3).map(request => (
              <div key={request.id} className="p-3 border border-white/20 rounded-lg">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-12 h-12 rounded-full overflow-hidden">
                    <img
                      src={request.fromUserAvatar || '/favicon.png'}
                      alt={request.fromUserName || 'User'}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        const nextSibling = e.currentTarget.nextElementSibling as HTMLElement;
                        if (nextSibling) nextSibling.style.display = 'flex';
                      }}
                    />
                    <div className="w-full h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center" style={{ display: 'none' }}>
                      <span className="text-white font-bold">
                        {request.fromUserName?.charAt(0) || 'U'}
                      </span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-cool-800 text-sm">{request.fromUserName}</h4>
                    <p className="text-xs text-cool-600">Wants to be friends</p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => acceptFriendRequest(request.id, request.fromUserId)}
                    className="flex-1 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg text-sm transition-colors"
                  >
                    Accept
                  </button>
                  <button
                    onClick={() => declineFriendRequest(request.id)}
                    className="flex-1 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg text-sm transition-colors"
                  >
                    Decline
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Suggested Providers */}
      <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
        <h3 className="font-bold text-gray-800 mb-4">Suggested Providers</h3>
        <div className="space-y-3">
          {suggestedProviders.length > 0 ? (
            suggestedProviders.map(provider => (
              <div key={provider.id} className="p-3 border border-gray-100 rounded-lg">
                <div
                  className="flex items-center gap-3 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2"
                  onClick={() => router.push(`/provider/public/${provider.id}`)}
                >
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                    <PawPrint className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-800 text-sm">{provider.businessName || provider.ownerName}</h4>
                    <p className="text-xs text-gray-600">{provider.serviceType} • {provider.location?.city}, {provider.location?.state}</p>
                  </div>
                </div>
                <button
                  className="w-full py-2 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white rounded-lg text-sm transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Follow provider logic here
                    toast.success('Following provider!');
                  }}
                >
                  Follow Provider
                </button>
              </div>
            ))
          ) : (
            <p className="text-sm text-gray-500">No providers found in your area</p>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-green-50 via-blue-50 to-green-50">
      {/* Community Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 py-8 mb-6">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
              Pet Community
            </h1>
            <p className="text-green-100 text-lg">
              Connect, share, and discover with fellow pet lovers
            </p>
          </div>
        </div>
      </div>

      {/* Facebook-style Layout */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex gap-6">
          {/* Left Sidebar */}
          <div className="hidden lg:block">
            {renderLeftSidebar()}
          </div>

          {/* Main Content */}
          <div className="flex-1 max-w-2xl mx-auto">


            {/* Stories Section */}
            <AwesomeStories />

            {/* Create Post - Only for logged in users */}
            {user && (
              <CreatePost
                placeholder="What's happening in the pet community?"
                defaultPublic={true}
              />
            )}

            {/* Posts Feed */}
            <div className="space-y-6">
              {dataLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
                  <p className="text-cool-600 mt-2">Loading community posts...</p>
                </div>
              ) : searchFilteredPosts.length > 0 ? (
                searchFilteredPosts.map(post => (
                  <PostCard
                    key={post.id}
                    post={post}
                    showPrivacyIndicator={true}
                    onEdit={handleEditPost}
                    onDelete={handleDeletePost}
                  />
                ))
              ) : (
                <div className="glass-card rounded-xl p-8 text-center">
                  <PawPrint className="w-16 h-16 text-primary-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-cool-800 mb-2">No Public Posts Yet</h3>
                  <p className="text-cool-600 mb-6">
                    Be the first to share something with the community!
                  </p>
                </div>
              )}

              {/* Login Prompt for Non-Users */}
              {!user && visiblePosts >= 5 && (
                <div className="glass-card rounded-xl p-8 text-center">
                  <PawPrint className="w-16 h-16 text-primary-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-cool-800 mb-2">Join the Community!</h3>
                  <p className="text-cool-600 mb-6">
                    Sign up to see more posts, connect with pet owners and providers, and share your own pet stories!
                  </p>
                  <div className="flex gap-4 justify-center">
                    <Link
                      href="/auth/signin"
                      className="px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors"
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth/signup"
                      className="px-6 py-3 bg-white/50 hover:bg-white/70 text-cool-700 rounded-lg transition-colors"
                    >
                      Sign Up
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="hidden xl:block">
            {renderRightSidebar()}
          </div>
        </div>
      </div>



      {/* Login Prompt Modal */}
      {showLoginPrompt && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="glass-card rounded-xl p-6 w-full max-w-md mx-4">
            <div className="text-center">
              <PawPrint className="w-16 h-16 text-primary-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-cool-800 mb-2">Join the Community!</h3>
              <p className="text-cool-600 mb-6">
                Sign in to interact with posts, share your pet stories, and connect with other pet lovers!
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowLoginPrompt(false)}
                  className="flex-1 px-4 py-2 bg-white/50 hover:bg-white/70 text-cool-700 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <Link
                  href="/auth/signin"
                  className="flex-1 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors text-center"
                >
                  Sign In
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Post Modal */}
      {showEditModal && editingPost && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">Edit Post</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPost(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <textarea
                defaultValue={editingPost.content}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={4}
                placeholder="What's on your mind?"
                id="edit-content"
              />

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingPost(null);
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={async () => {
                    const textarea = document.getElementById('edit-content') as HTMLTextAreaElement;
                    const newContent = textarea.value.trim();

                    if (!newContent) {
                      toast.error('Post content cannot be empty');
                      return;
                    }

                    try {
                      await updatePost(editingPost.id, { content: newContent });
                      toast.success('Post updated successfully');
                      setShowEditModal(false);
                      setEditingPost(null);
                    } catch (error) {
                      console.error('Error updating post:', error);
                      toast.error('Failed to update post');
                    }
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}
