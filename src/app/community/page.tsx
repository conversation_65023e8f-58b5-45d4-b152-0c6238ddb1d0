'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Search,
  Filter,
  Users,
  Heart,
  MessageCircle,
  TrendingUp,
  Sparkles,
  Globe,
  Camera,
  Video,
  MapPin,
  Calendar,
  Star,
  Flame,
  Zap,
  Award,
  Gift,
  Home,
  BarChart3,
  Settings,
  MessageSquare
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { contentModerationService } from '@/lib/services/content-moderation';
import AwesomeStories from '@/components/AwesomeStories';
import CreatePost from '@/components/CreatePost';
import PostCard from '@/components/PostCard';
import toast from 'react-hot-toast';

export default function CommunityPage() {
  const { user } = useAuth();
  const { publicPosts, loading: dataLoading, users } = useData();
  const { t } = useLanguage();

  // States
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [showCreatePost, setShowCreatePost] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<any[]>([]);
  const [trendingTopics, setTrendingTopics] = useState<any[]>([]);
  const [suggestedUsers, setSuggestedUsers] = useState<any[]>([]);

  // Filter options with green/blue color scheme
  const filterOptions = [
    { id: 'all', label: 'All Posts', icon: Globe, color: 'from-green-500 to-blue-500' },
    { id: 'following', label: 'Following', icon: Users, color: 'from-blue-500 to-green-500' },
    { id: 'trending', label: 'Trending', icon: TrendingUp, color: 'from-green-600 to-blue-600' },
    { id: 'photos', label: 'Photos', icon: Camera, color: 'from-blue-600 to-green-600' },
    { id: 'videos', label: 'Videos', icon: Video, color: 'from-green-700 to-blue-700' }
  ];

  // Load real data from Firebase
  useEffect(() => {
    // Extract trending hashtags from posts
    const hashtags = new Map();
    publicPosts.forEach((post: any) => {
      const content = post.content || '';
      const matches = content.match(/#\w+/g);
      if (matches) {
        matches.forEach((tag: string) => {
          hashtags.set(tag, (hashtags.get(tag) || 0) + 1);
        });
      }
    });

    // Convert to trending topics array
    const trending = Array.from(hashtags.entries())
      .map(([tag, count]) => ({ tag, count, trending: count > 1 }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    setTrendingTopics(trending);

    // TODO: Load online friends (users who follow each other and are currently online)
    // For now, this will be empty until follow system is implemented
    setOnlineUsers([]);

    // TODO: Load suggested users from Firebase active users
    // For now, this will be empty until we have real user data
    setSuggestedUsers([]);
  }, [publicPosts]);

  // Filter posts based on selected filter
  const filteredPosts = publicPosts.filter((post: any) => {
    const matchesSearch = (post.content || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (post.userName || '').toLowerCase().includes(searchQuery.toLowerCase());

    if (!matchesSearch) return false;

    switch (selectedFilter) {
      case 'all':
        return true;
      case 'following':
        // TODO: Implement following logic when follow system is ready
        return user && post.userId !== user.id; // Temporary logic
      case 'trending':
        // Posts with hashtags or high engagement
        const hasHashtags = (post.content || '').includes('#');
        const isPopular = (post.likes || 0) > 3;
        return hasHashtags || isPopular;
      case 'photos':
        return post.image && post.image.trim() !== '';
      case 'videos':
        // TODO: Add video support when video posts are implemented
        return false;
      default:
        return true;
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-green-100">
      {/* Enhanced Header - Mobile Optimized */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo & Title - Mobile Responsive */}
            <div className="flex items-center space-x-3 md:space-x-4">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-5 h-5 md:w-6 md:h-6 text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                  {t('community.title')}
                </h1>
                <p className="text-xs md:text-sm text-gray-500 hidden md:block">{t('community.subtitle')}</p>
              </div>
            </div>

            {/* Search Bar - Mobile Responsive */}
            <div className="flex-1 max-w-xs md:max-w-md mx-4 md:mx-8">
              <div className="relative">
                <Search className="absolute left-3 md:left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 md:w-5 md:h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder={t('community.search')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 md:pl-12 pr-3 md:pr-4 py-2 md:py-3 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-xl md:rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 shadow-lg text-sm md:text-base"
                />
              </div>
            </div>

            {/* User Actions - Mobile Responsive */}
            <div className="flex items-center space-x-2 md:space-x-3">
              {user && (
                <>
                  <button
                    onClick={() => setShowCreatePost(true)}
                    className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-3 md:px-6 py-2 md:py-3 rounded-xl md:rounded-2xl font-semibold hover:from-green-600 hover:to-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center space-x-1 md:space-x-2"
                  >
                    <Sparkles className="w-4 h-4 md:w-5 md:h-5" />
                    <span className="hidden sm:inline text-sm md:text-base">{t('community.createPost')}</span>
                    <span className="sm:hidden text-xs">{t('community.createPost')}</span>
                  </button>

                  <div className="relative">
                    <img
                      src={user.avatar || '/favicon.png'}
                      alt={user.name}
                      className="w-10 h-10 md:w-12 md:h-12 rounded-xl md:rounded-2xl object-cover border-2 border-white shadow-lg"
                    />
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 md:w-4 md:h-4 bg-green-500 rounded-full border-2 border-white" />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-3 md:px-4 py-4 md:py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 md:gap-6">
          {/* Left Sidebar - Mobile Hidden, Tablet+ Visible */}
          <div className="hidden lg:block lg:col-span-1 space-y-4 md:space-y-6">
            {/* Filter Tabs */}
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl md:rounded-3xl shadow-xl border border-gray-200/50 p-4 md:p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Filter className="w-5 h-5 text-purple-500" />
                <span>Explore</span>
              </h3>
              <div className="space-y-2">
                {filterOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <button
                      key={option.id}
                      onClick={() => setSelectedFilter(option.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-2xl transition-all duration-300 ${
                        selectedFilter === option.id
                          ? `bg-gradient-to-r ${option.color} text-white shadow-lg transform scale-105`
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{option.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Trending Topics */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Flame className="w-5 h-5 text-green-500" />
                <span>Trending</span>
              </h3>
              <div className="space-y-3">
                {trendingTopics.length > 0 ? (
                  trendingTopics.map((topic, index) => (
                    <div key={topic.tag} className="flex items-center justify-between p-3 rounded-2xl hover:bg-gray-50 transition-colors cursor-pointer">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          topic.trending ? 'bg-green-100 text-green-500' : 'bg-gray-100 text-gray-500'
                        }`}>
                          {topic.trending ? <TrendingUp className="w-4 h-4" /> : <span className="text-xs font-bold">{index + 1}</span>}
                        </div>
                        <div>
                          <p className="font-semibold text-gray-800">{topic.tag}</p>
                          <p className="text-xs text-gray-500">{topic.count.toLocaleString()} posts</p>
                        </div>
                      </div>
                      {topic.trending && <Zap className="w-4 h-4 text-blue-500" />}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500 text-sm">No trending topics yet</p>
                    <p className="text-gray-400 text-xs mt-1">Start using hashtags in your posts!</p>
                  </div>
                )}
              </div>
            </div>

            {/* Online Users */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Users className="w-5 h-5 text-green-500" />
                <span>Online Now</span>
                <span className="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full font-bold">
                  {onlineUsers.filter(u => u.isOnline).length}
                </span>
              </h3>
              <div className="space-y-3">
                {onlineUsers.filter(u => u.isOnline).slice(0, 5).map((onlineUser) => (
                  <div key={onlineUser.id} className="flex items-center space-x-3 p-2 rounded-2xl hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="relative">
                      <img
                        src={onlineUser.avatar || '/favicon.png'}
                        alt={onlineUser.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
                    </div>
                    <div className="flex-1">
                      <p className="font-semibold text-gray-800 text-sm">{onlineUser.name}</p>
                      <p className="text-xs text-green-500">Online now</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content - Mobile Full Width, Desktop 2 Columns */}
          <div className="lg:col-span-2 space-y-4 md:space-y-6">
            {/* Mobile Filter Tabs - Only visible on mobile */}
            <div className="lg:hidden bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 p-4">
              <div className="flex space-x-2 overflow-x-auto scrollbar-hide">
                {filterOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <button
                      key={option.id}
                      onClick={() => setSelectedFilter(option.id)}
                      className={`flex-shrink-0 flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-300 text-sm font-medium ${
                        selectedFilter === option.id
                          ? `bg-gradient-to-r ${option.color} text-white shadow-lg`
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{option.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Stories Section */}
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl md:rounded-3xl shadow-xl border border-gray-200/50 overflow-hidden">
              <AwesomeStories />
            </div>

            {/* Create Post Section */}
            {user && (
              <div className="bg-white/80 backdrop-blur-xl rounded-2xl md:rounded-3xl shadow-xl border border-gray-200/50 p-4 md:p-6">
                <CreatePost
                  placeholder="What's happening in the pet community? 🐾"
                  onPostCreated={() => {
                    toast.success('🎉 Post shared with the community!');
                  }}
                />
              </div>
            )}

            {/* Posts Feed - Mobile Optimized */}
            <div className="space-y-4 md:space-y-6">
              {dataLoading ? (
                // Loading skeletons
                [...Array(3)].map((_, i) => (
                  <div key={i} className="bg-white/80 backdrop-blur-xl rounded-2xl md:rounded-3xl shadow-xl border border-gray-200/50 p-4 md:p-6">
                    <div className="animate-pulse">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-full" />
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2" />
                          <div className="h-3 bg-gray-200 rounded w-1/6" />
                        </div>
                      </div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                      <div className="h-4 bg-gray-200 rounded w-1/2" />
                    </div>
                  </div>
                ))
              ) : filteredPosts.length > 0 ? (
                filteredPosts.map((post) => (
                  <div key={post.id} className="bg-white/80 backdrop-blur-xl rounded-2xl md:rounded-3xl shadow-xl border border-gray-200/50 overflow-hidden hover:shadow-2xl transition-all duration-300">
                    <PostCard post={post} />
                  </div>
                ))
              ) : (
                <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-12 text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl mx-auto mb-6 flex items-center justify-center">
                    <Search className="w-10 h-10 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-700 mb-2">No posts found</h3>
                  <p className="text-gray-500">Try adjusting your search or filters</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Mobile Hidden, Desktop Visible */}
          <div className="hidden lg:block lg:col-span-1 space-y-4 md:space-y-6">
            {/* Community Stats */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Award className="w-5 h-5 text-green-500" />
                <span>Community</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl">
                  <div className="flex items-center space-x-3">
                    <Users className="w-5 h-5 text-green-500" />
                    <span className="font-medium text-gray-700">Members</span>
                  </div>
                  <span className="font-bold text-green-600">{users.length.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl">
                  <div className="flex items-center space-x-3">
                    <MessageCircle className="w-5 h-5 text-blue-500" />
                    <span className="font-medium text-gray-700">Posts</span>
                  </div>
                  <span className="font-bold text-blue-600">{publicPosts.length.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl">
                  <div className="flex items-center space-x-3">
                    <Heart className="w-5 h-5 text-green-500" />
                    <span className="font-medium text-gray-700">Likes</span>
                  </div>
                  <span className="font-bold text-green-600">
                    {publicPosts.reduce((sum: number, post: any) => sum + (post.likes || 0), 0).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Suggested Users */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Star className="w-5 h-5 text-yellow-500" />
                <span>Suggested</span>
              </h3>
              <div className="space-y-3">
                {suggestedUsers.length > 0 ? (
                  suggestedUsers.slice(0, 4).map((suggestedUser: any) => (
                    <div key={suggestedUser.id} className="flex items-center justify-between p-3 rounded-2xl hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-3">
                        <img
                          src={suggestedUser.avatar || '/favicon.png'}
                          alt={suggestedUser.name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                        <div>
                          <p className="font-semibold text-gray-800 text-sm">{suggestedUser.name}</p>
                          <p className="text-xs text-gray-500">Pet lover</p>
                        </div>
                      </div>
                      <button className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold hover:from-green-600 hover:to-blue-600 transition-all duration-300">
                        Follow
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500 text-sm">No suggestions available</p>
                    <p className="text-gray-400 text-xs mt-1">Check back later!</p>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Zap className="w-5 h-5 text-green-500" />
                <span>Quick Actions</span>
              </h3>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => window.location.href = '/'}
                  className="flex flex-col items-center p-3 bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl hover:from-green-100 hover:to-blue-100 transition-colors"
                >
                  <Home className="w-6 h-6 text-green-500 mb-2" />
                  <span className="text-xs font-medium text-gray-700">Home</span>
                </button>
                <button
                  onClick={() => window.location.href = '/dashboard'}
                  className="flex flex-col items-center p-3 bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl hover:from-blue-100 hover:to-green-100 transition-colors"
                >
                  <BarChart3 className="w-6 h-6 text-blue-500 mb-2" />
                  <span className="text-xs font-medium text-gray-700">Dashboard</span>
                </button>
                <button
                  onClick={() => window.location.href = '/messages'}
                  className="flex flex-col items-center p-3 bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl hover:from-green-100 hover:to-blue-100 transition-colors"
                >
                  <MessageSquare className="w-6 h-6 text-green-600 mb-2" />
                  <span className="text-xs font-medium text-gray-700">Messages</span>
                </button>
                <button
                  onClick={() => window.location.href = '/profile'}
                  className="flex flex-col items-center p-3 bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl hover:from-blue-100 hover:to-green-100 transition-colors"
                >
                  <Settings className="w-6 h-6 text-blue-600 mb-2" />
                  <span className="text-xs font-medium text-gray-700">Profile</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
