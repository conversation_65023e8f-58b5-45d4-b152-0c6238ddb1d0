'use client';

import { useState } from 'react';
import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

export default function AddTestDataPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  const addTestPet = async () => {
    if (!user) {
      toast.error('Please sign in first');
      return;
    }

    setLoading(true);
    try {
      const petData = {
        userId: user.id,
        name: 'Buddy',
        type: 'dog',
        breed: 'Golden Retriever',
        age: 3,
        gender: 'male',
        weight: 65,
        size: 'large',
        description: 'Friendly and energetic golden retriever who loves playing fetch!',
        profileImage: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=400&fit=crop&crop=face',
        photo: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=400&fit=crop&crop=face',
        avatar: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=400&fit=crop&crop=face',
        isActive: true,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await addDoc(collection(db, 'pets'), petData);
      toast.success('Test pet added successfully!');
    } catch (error) {
      console.error('Error adding test pet:', error);
      toast.error('Failed to add test pet');
    } finally {
      setLoading(false);
    }
  };

  const addTestPost = async () => {
    if (!user) {
      toast.error('Please sign in first');
      return;
    }

    setLoading(true);
    try {
      const postData = {
        userId: user.id,
        userName: user.name || 'Test User',
        userAvatar: user.avatar || '/favicon.png',
        content: 'Just took my dog Buddy to the park! He had so much fun playing with other dogs. 🐕 #DogLife #PetOwner',
        image: 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=600&h=400&fit=crop',
        likes: 0,
        likedBy: [],
        comments: 0,
        views: 0,
        shares: 0,
        isPublic: true,
        isStory: false,
        timestamp: Timestamp.now(),
        createdAt: Timestamp.now()
      };

      await addDoc(collection(db, 'posts'), postData);
      toast.success('Test post added successfully!');
    } catch (error) {
      console.error('Error adding test post:', error);
      toast.error('Failed to add test post');
    } finally {
      setLoading(false);
    }
  };

  const addMultipleTestPets = async () => {
    if (!user) {
      toast.error('Please sign in first');
      return;
    }

    setLoading(true);
    try {
      const pets = [
        {
          name: 'Luna',
          type: 'cat',
          breed: 'Persian',
          age: 2,
          gender: 'female',
          weight: 8,
          size: 'medium',
          description: 'Beautiful Persian cat with long fluffy fur.',
          profileImage: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=400&fit=crop&crop=face',
          photo: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=400&fit=crop&crop=face',
          avatar: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=400&fit=crop&crop=face'
        },
        {
          name: 'Max',
          type: 'dog',
          breed: 'German Shepherd',
          age: 5,
          gender: 'male',
          weight: 75,
          size: 'large',
          description: 'Loyal and intelligent German Shepherd.',
          profileImage: 'https://images.unsplash.com/photo-1589941013453-ec89f33b5e95?w=400&h=400&fit=crop&crop=face',
          photo: 'https://images.unsplash.com/photo-1589941013453-ec89f33b5e95?w=400&h=400&fit=crop&crop=face',
          avatar: 'https://images.unsplash.com/photo-1589941013453-ec89f33b5e95?w=400&h=400&fit=crop&crop=face'
        },
        {
          name: 'Bella',
          type: 'dog',
          breed: 'Labrador',
          age: 4,
          gender: 'female',
          weight: 55,
          size: 'medium',
          description: 'Sweet and gentle Labrador who loves swimming.',
          profileImage: 'https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=400&fit=crop&crop=face',
          photo: 'https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=400&fit=crop&crop=face',
          avatar: 'https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=400&fit=crop&crop=face'
        }
      ];

      for (const pet of pets) {
        const petData = {
          userId: user.id,
          ...pet,
          isActive: true,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        };
        await addDoc(collection(db, 'pets'), petData);
      }

      toast.success(`Added ${pets.length} test pets successfully!`);
    } catch (error) {
      console.error('Error adding test pets:', error);
      toast.error('Failed to add test pets');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Please Sign In</h1>
          <p className="text-gray-600 mb-6">You need to be signed in to add test data.</p>
          <a 
            href="/auth/signin"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Sign In
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Add Test Data</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current User</h2>
          <div className="bg-gray-50 p-4 rounded-lg mb-6">
            <p><strong>ID:</strong> {user.id}</p>
            <p><strong>Name:</strong> {user.name}</p>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Role:</strong> {user.role}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <button
              onClick={addTestPet}
              disabled={loading}
              className="px-6 py-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Adding...' : 'Add Test Pet'}
            </button>

            <button
              onClick={addTestPost}
              disabled={loading}
              className="px-6 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Adding...' : 'Add Test Post'}
            </button>

            <button
              onClick={addMultipleTestPets}
              disabled={loading}
              className="px-6 py-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Adding...' : 'Add Multiple Pets'}
            </button>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">Instructions:</h3>
            <ol className="list-decimal list-inside text-yellow-700 space-y-1">
              <li>Add some test pets and posts using the buttons above</li>
              <li>Go to <a href="/test-users" className="underline">Test Users page</a> to see all data</li>
              <li>Click "View Profile" next to your user to test the public profile</li>
              <li>Test the image popup functionality by clicking on pet images and post images</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
