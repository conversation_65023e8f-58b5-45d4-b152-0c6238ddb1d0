'use client';

import { Newspaper, Calendar, Users, Award, TrendingUp, Mail, Phone, Download, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export default function PressPage() {
  const upcomingAnnouncements = [
    {
      title: "Fetchly Launches in Puerto Rico",
      date: "Q2 2024",
      description: "Revolutionary pet care platform brings trusted services to Puerto Rican pet families"
    },
    {
      title: "Provider Partnership Program",
      date: "Q3 2024", 
      description: "Expanding our network of certified pet care professionals across the island"
    },
    {
      title: "Pet Insurance Innovation",
      date: "Q4 2024",
      description: "Introducing the most comprehensive and affordable pet insurance solution"
    }
  ];

  const mediaKit = [
    {
      title: "Company Logo Pack",
      description: "High-resolution logos in various formats",
      type: "ZIP",
      size: "2.4 MB"
    },
    {
      title: "Brand Guidelines",
      description: "Complete brand identity and usage guidelines",
      type: "PDF",
      size: "1.8 MB"
    },
    {
      title: "Product Screenshots",
      description: "High-quality app and platform screenshots",
      type: "ZIP",
      size: "5.2 MB"
    },
    {
      title: "Executive Bios",
      description: "Leadership team biographies and headshots",
      type: "PDF",
      size: "890 KB"
    }
  ];

  const stats = [
    {
      icon: Users,
      number: "10,000+",
      label: "Pet Families Served",
      description: "Growing community across Puerto Rico"
    },
    {
      icon: Award,
      number: "500+",
      label: "Verified Providers",
      description: "Trusted pet care professionals"
    },
    {
      icon: TrendingUp,
      number: "98%",
      label: "Customer Satisfaction",
      description: "Consistently high ratings"
    }
  ];

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-green-500 via-blue-500 to-cyan-500 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="p-4 bg-white/20 rounded-full">
                <Newspaper className="w-16 h-16" />
              </div>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              Press Center
            </h1>
            <p className="text-2xl text-blue-100 max-w-3xl mx-auto mb-8">
              Stay updated with the latest news, announcements, and milestones from Fetchly
            </p>
            <div className="bg-yellow-400 text-yellow-900 px-8 py-4 rounded-2xl inline-block font-bold text-xl">
              🚀 Coming Soon - Exciting Updates Ahead!
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon Message */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-12 border border-white/30">
              <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-8 shadow-xl">
                <Calendar className="w-12 h-12 text-white" />
              </div>
              <h2 className="text-4xl font-black bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">
                Press Resources Coming Soon
              </h2>
              <p className="text-xl text-gray-700 mb-8 max-w-2xl mx-auto font-medium">
                We're preparing comprehensive press materials, media kits, and newsroom resources.
                Our press center will be launching soon with exciting announcements about Fetchly's
                expansion in Puerto Rico and our innovative pet care solutions.
              </p>
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 mb-8 border border-green-200/50">
                <h3 className="text-xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4">What's Coming:</h3>
                <ul className="text-left space-y-3 text-gray-700 max-w-md mx-auto">
                  <li className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
                    <span className="font-medium">Press releases and company announcements</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
                    <span className="font-medium">High-resolution media assets and brand kit</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
                    <span className="font-medium">Executive team bios and photos</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
                    <span className="font-medium">Company milestones and achievements</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
                    <span className="font-medium">Industry insights and thought leadership</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Announcements */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Upcoming Announcements
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {upcomingAnnouncements.map((announcement, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white mx-auto mb-6">
                    <Newspaper className="w-8 h-8" />
                  </div>
                  <div className="text-sm font-semibold text-blue-600 mb-2">{announcement.date}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{announcement.title}</h3>
                  <p className="text-gray-600">{announcement.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Company Stats Preview */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Fetchly by the Numbers
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white mx-auto mb-6">
                    <stat.icon className="w-8 h-8" />
                  </div>
                  <div className="text-4xl font-bold text-gray-900 mb-2">{stat.number}</div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">{stat.label}</h3>
                  <p className="text-gray-600">{stat.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Media Kit Preview */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Media Kit (Coming Soon)
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {mediaKit.map((item, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg p-6 flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center text-white">
                    <Download className="w-6 h-6" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-gray-900">{item.title}</h3>
                    <p className="text-gray-600 text-sm">{item.description}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">{item.type}</span>
                      <span className="text-xs text-gray-500">{item.size}</span>
                    </div>
                  </div>
                  <button className="text-gray-400 hover:text-gray-600 transition-colors" disabled>
                    <Download className="w-5 h-5" />
                  </button>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-8">
              <p className="text-gray-600 mb-4">Media assets will be available for download soon</p>
              <button className="bg-gray-300 text-gray-500 px-8 py-3 rounded-lg font-semibold cursor-not-allowed">
                Download Media Kit (Coming Soon)
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Section */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center">
              <h2 className="text-3xl font-bold mb-4">Media Inquiries</h2>
              <p className="text-xl text-blue-100 mb-8">
                For press inquiries, interviews, or additional information about Fetchly
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                <div className="bg-white/20 rounded-xl p-6">
                  <Mail className="w-8 h-8 mx-auto mb-3" />
                  <h3 className="font-bold mb-2">Email</h3>
                  <a href="mailto:<EMAIL>" className="text-blue-100 hover:text-white">
                    <EMAIL>
                  </a>
                </div>
                <div className="bg-white/20 rounded-xl p-6">
                  <Phone className="w-8 h-8 mx-auto mb-3" />
                  <h3 className="font-bold mb-2">Phone</h3>
                  <a href="tel:+17875550123" className="text-blue-100 hover:text-white">
                    +****************
                  </a>
                </div>
              </div>
              
              <div className="mt-8">
                <p className="text-blue-100 text-sm">
                  We typically respond to media inquiries within 24 hours
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Signup */}
      <div className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              Stay Updated
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Subscribe to receive press releases and company updates
            </p>
            
            <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300">
                  Subscribe
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                We respect your privacy. Unsubscribe at any time.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
