'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  User, Wallet, Gift, Calendar, Bell, Settings, PawPrint,
  TrendingUp, Award, CreditCard, MessageCircle, Plus, ArrowUpRight,
  Heart, Star, MapPin, Clock, Phone, Mail, Camera, Edit3,
  Users, Activity, Zap, Shield, Home, BookOpen, Search,
  Filter, ChevronRight, ExternalLink, Sparkles, Crown
} from 'lucide-react';

import QuickActions from '@/components/QuickActions';
import PostCard from '@/components/PostCard';
import WalletManager from '@/components/payments/WalletManager';

export default function DashboardPage() {
  const { user } = useAuth();
  const { stats, userPosts, loading } = useData();
  const router = useRouter();
  const [showWalletModal, setShowWalletModal] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showAppointmentModal, setShowAppointmentModal] = useState(false);

  // Navigation items for the sidebar
  const navigationItems = [
    { id: 'dashboard', name: 'Dashboard', icon: Home, description: 'Overview and quick stats' },
    { id: 'profile', name: 'My Profile', icon: User, description: 'Manage your profile and pets' },
    { id: 'wallet', name: 'Wallet', icon: Wallet, description: 'Manage payments and balance' },
    { id: 'community', name: 'Community', icon: Users, description: 'Connect with other pet parents' },
    { id: 'appointments', name: 'Appointments', icon: Calendar, description: 'Book and manage appointments' },
    { id: 'rewards', name: 'Rewards', icon: Gift, description: 'Coming Soon', disabled: true },
  ];

  // Redirect based on authentication and user role
  useEffect(() => {
    if (!user) {
      router.push('/auth/signin');
      return;
    }

    // Redirect providers to their dedicated dashboard
    if (user.role === 'provider') {
      router.push('/provider/dashboard');
      return;
    }
  }, [user, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  const renderDashboardContent = () => (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-3xl p-8 text-white relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
        <div className="relative">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Welcome back, {user?.name?.split(' ')[0] || 'Pet Parent'}! 🐾</h1>
              <p className="text-green-100 text-lg">Your pets are waiting for some love and care</p>
            </div>
            <div className="hidden md:block">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <PawPrint className="w-10 h-10 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">My Pets</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalPets}</p>
              <p className="text-xs text-green-600 mt-1">All healthy & happy</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center">
              <PawPrint className="w-6 h-6 text-white" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Appointments</p>
              <p className="text-3xl font-bold text-gray-900">{stats.upcomingAppointments}</p>
              <p className="text-xs text-green-600 mt-1">This month</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-white" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Reward Points</p>
              <p className="text-3xl font-bold text-gray-900">{stats.rewardPoints.toLocaleString()}</p>
              <p className="text-xs text-blue-600 mt-1 flex items-center">
                <Sparkles className="w-3 h-3 mr-1" />
                Coming Soon
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl flex items-center justify-center">
              <Gift className="w-6 h-6 text-white" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 cursor-pointer"
          onClick={() => setActiveTab('wallet')}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Wallet Balance</p>
              <p className="text-3xl font-bold text-gray-900">${stats.walletBalance.toFixed(2)}</p>
              <p className="text-xs text-blue-600 mt-1">Click to manage</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center">
              <Wallet className="w-6 h-6 text-white" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900">Recent Activity</h3>
          <button
            onClick={() => setActiveTab('community')}
            className="text-green-600 hover:text-green-700 font-medium flex items-center space-x-1"
          >
            <span>View All</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>

        {userPosts.length > 0 ? (
          <div className="space-y-4">
            {userPosts.slice(0, 3).map((post) => (
              <PostCard key={post.id} post={post} showPrivacyIndicator={true} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MessageCircle className="w-8 h-8 text-gray-400" />
            </div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h4>
            <p className="text-gray-500 mb-4">Share your first post to connect with the community!</p>
            <button
              onClick={() => setActiveTab('community')}
              className="bg-gradient-to-r from-green-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:from-green-600 hover:to-blue-700 transition-colors"
            >
              Create Your First Post
            </button>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setActiveTab('appointments')}
          className="bg-gradient-to-r from-green-500 to-blue-600 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <div className="text-left">
              <h4 className="text-lg font-semibold mb-1">Book Appointment</h4>
              <p className="text-green-100 text-sm">Find nearby pet services</p>
            </div>
            <Calendar className="w-8 h-8 text-white" />
          </div>
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setActiveTab('profile')}
          className="bg-gradient-to-r from-green-400 to-blue-500 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <div className="text-left">
              <h4 className="text-lg font-semibold mb-1">Manage Pets</h4>
              <p className="text-green-100 text-sm">Update pet profiles</p>
            </div>
            <PawPrint className="w-8 h-8 text-white" />
          </div>
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setActiveTab('community')}
          className="bg-gradient-to-r from-blue-500 to-green-600 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <div className="flex items-center justify-between">
            <div className="text-left">
              <h4 className="text-lg font-semibold mb-1">Join Community</h4>
              <p className="text-blue-100 text-sm">Connect with pet parents</p>
            </div>
            <Users className="w-8 h-8 text-white" />
          </div>
        </motion.button>
      </div>
    </div>
  );

  const renderProfileContent = () => (
    <div className="space-y-8">
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <h3 className="text-2xl font-bold text-gray-900 mb-6">My Profile</h3>
        <div className="text-center py-12">
          <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">Profile Management</h4>
          <p className="text-gray-500 mb-6">Manage your personal information and pet profiles</p>
          <button
            onClick={() => router.push('/profile')}
            className="bg-gradient-to-r from-green-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:from-green-600 hover:to-blue-700 transition-colors flex items-center space-x-2 mx-auto"
          >
            <span>Go to Profile</span>
            <ExternalLink className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );

  const renderWalletContent = () => (
    <div className="space-y-4 sm:space-y-8">
      <div className="bg-white rounded-2xl p-4 sm:p-8 shadow-lg border border-gray-100">
        <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6">Fetchly Wallet</h3>
        <WalletManager />
      </div>
    </div>
  );

  const renderCommunityContent = () => (
    <div className="space-y-8">
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <h3 className="text-2xl font-bold text-gray-900 mb-6">Community</h3>
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">Join the Community</h4>
          <p className="text-gray-500 mb-6">Connect with other pet parents, share photos, and get advice</p>
          <button
            onClick={() => router.push('/community')}
            className="bg-gradient-to-r from-blue-500 to-green-600 text-white px-6 py-3 rounded-lg hover:from-blue-600 hover:to-green-700 transition-colors flex items-center space-x-2 mx-auto"
          >
            <span>Go to Community</span>
            <ExternalLink className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );

  const renderAppointmentsContent = () => (
    <div className="space-y-8">
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <h3 className="text-2xl font-bold text-gray-900 mb-6">Appointments</h3>
        <div className="text-center py-12">
          <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">Book & Manage Appointments</h4>
          <p className="text-gray-500 mb-6">Find nearby pet services and book appointments for your pets</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => router.push('/search')}
              className="bg-gradient-to-r from-green-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:from-green-600 hover:to-blue-700 transition-colors flex items-center space-x-2"
            >
              <Search className="w-4 h-4" />
              <span>Find Services</span>
            </button>
            <button
              onClick={() => router.push('/appointments')}
              className="bg-gradient-to-r from-blue-500 to-green-600 text-white px-6 py-3 rounded-lg hover:from-blue-600 hover:to-green-700 transition-colors flex items-center space-x-2"
            >
              <Calendar className="w-4 h-4" />
              <span>My Appointments</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderRewardsContent = () => (
    <div className="space-y-8">
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <h3 className="text-2xl font-bold text-gray-900 mb-6">Rewards Program</h3>
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">Coming Soon!</h4>
          <p className="text-gray-500 mb-6">Earn points for every booking and redeem them for amazing rewards</p>
          <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4 max-w-md mx-auto">
            <p className="text-green-800 text-sm">
              🎉 We're working on an amazing rewards program that will let you earn points for every service you book!
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboardContent();
      case 'profile':
        return renderProfileContent();
      case 'wallet':
        return renderWalletContent();
      case 'community':
        return renderCommunityContent();
      case 'appointments':
        return renderAppointmentsContent();
      case 'rewards':
        return renderRewardsContent();
      default:
        return renderDashboardContent();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto p-6">

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden sticky top-6">
              <div className="p-6 bg-gradient-to-r from-green-500 to-blue-600">
                <h3 className="text-white font-semibold text-lg">Navigation</h3>
                <p className="text-green-100 text-sm mt-1">Choose a section</p>
              </div>

              <nav className="p-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.id}
                      onClick={() => !item.disabled && setActiveTab(item.id)}
                      disabled={item.disabled}
                      className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-xl transition-all duration-200 mb-1 ${
                        activeTab === item.id
                          ? 'bg-gradient-to-r from-green-500 to-blue-600 text-white shadow-lg transform scale-105'
                          : item.disabled
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon className={`w-5 h-5 ${
                        activeTab === item.id ? 'text-white' : item.disabled ? 'text-gray-400' : 'text-gray-500'
                      }`} />
                      <div className="flex-1">
                        <span className="font-medium">{item.name}</span>
                        <p className={`text-xs mt-0.5 ${
                          activeTab === item.id ? 'text-green-100' : item.disabled ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {item.description}
                        </p>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Content Area */}
          <div className="lg:col-span-3">
            <div className="min-h-[600px]">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>

    </div>
  );
}
