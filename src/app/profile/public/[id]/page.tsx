'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, getDocs, doc, getDoc, addDoc, deleteDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  MapPin, Heart, Share2, MessageCircle, ArrowLeft, PawPrint,
  Users, Star, Calendar, Camera, Trophy, Crown, Sparkles
} from 'lucide-react';
import toast from 'react-hot-toast';
import PostCard from '@/components/PostCard';

export default function PublicPetOwnerProfile({ params }: { params: Promise<{ id: string }> }) {
  const { user } = useAuth();
  const router = useRouter();
  const resolvedParams = use(params);
  const userId = resolvedParams.id;
  
  const [profileUser, setProfileUser] = useState<any>(null);
  const [pets, setPets] = useState<any[]>([]);
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);
  const [followingCount, setFollowingCount] = useState(0);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoading(true);
        console.log('🔍 Loading public pet owner profile for userId:', userId);

        // Load user data
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (!userDoc.exists()) {
          throw new Error('User not found');
        }

        const userData = userDoc.data();
        setProfileUser({
          id: userId,
          ...userData
        });

        // Load user's pets
        const petsQuery = query(
          collection(db, 'pets'),
          where('userId', '==', userId)
        );
        const petsSnapshot = await getDocs(petsQuery);
        const userPets = petsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setPets(userPets);

        // Load user's public posts
        const postsQuery = query(
          collection(db, 'posts'),
          where('userId', '==', userId),
          where('isPublic', '==', true)
        );
        const postsSnapshot = await getDocs(postsQuery);
        const userPosts = postsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setPosts(userPosts);

        // Check if current user is following this profile
        if (user) {
          const followQuery = query(
            collection(db, 'followers'),
            where('followedUserId', '==', userId),
            where('followerUserId', '==', user.id)
          );
          const followSnapshot = await getDocs(followQuery);
          setIsFollowing(!followSnapshot.empty);
        }

        // Get follower count
        const followerQuery = query(
          collection(db, 'followers'),
          where('followedUserId', '==', userId)
        );
        const followerSnapshot = await getDocs(followerQuery);
        setFollowerCount(followerSnapshot.docs.length);

        // Get following count
        const followingQuery = query(
          collection(db, 'followers'),
          where('followerUserId', '==', userId)
        );
        const followingSnapshot = await getDocs(followingQuery);
        setFollowingCount(followingSnapshot.docs.length);

      } catch (error) {
        console.error('Error loading profile:', error);
        toast.error('Failed to load profile');
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, [userId, user]);

  const handleFollow = async () => {
    if (!user) {
      toast.error('Please sign in to follow users');
      return;
    }

    try {
      if (isFollowing) {
        // Unfollow
        const followQuery = query(
          collection(db, 'followers'),
          where('followedUserId', '==', userId),
          where('followerUserId', '==', user.id)
        );
        const followSnapshot = await getDocs(followQuery);
        if (!followSnapshot.empty) {
          await deleteDoc(followSnapshot.docs[0].ref);
          setIsFollowing(false);
          setFollowerCount(prev => prev - 1);
          toast.success('Unfollowed user');
        }
      } else {
        // Follow
        await addDoc(collection(db, 'followers'), {
          followedUserId: userId,
          followerUserId: user.id,
          createdAt: new Date()
        });
        setIsFollowing(true);
        setFollowerCount(prev => prev + 1);
        toast.success('Following user');
      }
    } catch (error) {
      console.error('Error following/unfollowing:', error);
      toast.error('Failed to update follow status');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!profileUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center p-6 max-w-md">
          <PawPrint className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Profile Not Found</h2>
          <p className="text-gray-600 mb-4">This user profile doesn't exist or isn't available.</p>
          <button
            onClick={() => router.push('/community')}
            className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-2 rounded-lg hover:from-green-600 hover:to-blue-600"
          >
            Back to Community
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-green-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="space-y-8">

          {/* Profile Header */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden mb-8">
            <div className="relative w-full h-80 bg-gradient-to-r from-green-600 to-blue-600">
              {profileUser.banner ? (
                <div className="relative w-full h-full overflow-hidden">
                  <img
                    src={profileUser.banner}
                    alt="Profile banner"
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="absolute inset-0 bg-black bg-opacity-20"></div>
              )}
            </div>

            <div className="relative px-6 pb-6">
              <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                {/* Profile Picture */}
                <div className="relative -mt-16 mb-4 sm:mb-0">
                  <img
                    src={profileUser.avatar || '/favicon.png'}
                    alt={profileUser.name}
                    className="w-32 h-32 rounded-full border-4 border-white shadow-lg object-cover"
                  />
                </div>

                {/* User Info */}
                <div className="flex-1">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">{profileUser.name}</h1>
                    <div className="text-gray-600 mt-1">
                      {pets.length > 0 ? (
                        <div className="flex items-center space-x-1">
                          <PawPrint className="w-4 h-4" />
                          <span>Pet parent to {pets.slice(0, 3).map(pet => pet.name).join(', ')}{pets.length > 3 ? ` and ${pets.length - 3} more` : ''}</span>
                        </div>
                      ) : (
                        <span>Pet lover</span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      {profileUser.location && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{profileUser.location}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>Member since {profileUser.joinedDate ? new Date(profileUser.joinedDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Recently'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Follow Button */}
                {user && user.id !== userId && (
                  <div className="flex space-x-3 mt-4 sm:mt-0">
                    <button
                      onClick={handleFollow}
                      className={`px-6 py-3 rounded-lg flex items-center space-x-2 transition-all duration-300 font-medium ${
                        isFollowing
                          ? 'bg-green-600 text-white hover:bg-green-700'
                          : 'bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white'
                      }`}
                    >
                      <Users className="w-5 h-5" />
                      <span>{isFollowing ? 'Following' : 'Follow'}</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* User Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center hover:shadow-xl transition-all duration-300">
              <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent mb-2">
                {pets.length}
              </div>
              <div className="text-gray-600">Pets</div>
            </div>
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center hover:shadow-xl transition-all duration-300">
              <div className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
                {posts.length}
              </div>
              <div className="text-gray-600">Posts</div>
            </div>
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center hover:shadow-xl transition-all duration-300">
              <div className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">{followerCount}</div>
              <div className="text-gray-600">Followers</div>
            </div>
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center hover:shadow-xl transition-all duration-300">
              <div className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2">{followingCount}</div>
              <div className="text-gray-600">Following</div>
            </div>
          </div>
          {/* Pet Management Section */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                {profileUser.name}'s Pets
              </h2>
            </div>

            {pets.length === 0 ? (
              <div className="text-center py-12">
                <PawPrint className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No pets to show</h3>
                <p className="text-gray-500 mb-6">This user hasn't added any pets yet</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {pets.map((pet) => (
                  <div key={pet.id} className="bg-white rounded-xl p-4 border border-gray-200 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center space-x-4 mb-4">
                      <img
                        src={pet.avatar || '/favicon.png'}
                        alt={pet.name}
                        className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                      />
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{pet.name}</h3>
                        <p className="text-gray-600">{pet.breed} • {pet.age} years old</p>
                      </div>
                    </div>
                    {pet.description && (
                      <p className="text-gray-600 text-sm">{pet.description}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Posts Section */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">
              {profileUser.name}'s Posts
            </h3>

            {/* Posts Feed */}
            <div className="space-y-6">
              {posts.length > 0 ? (
                posts.map((post) => (
                  <PostCard
                    key={post.id}
                    post={post}
                    showPrivacyIndicator={false}
                  />
                ))
              ) : (
                <div className="text-center py-12">
                  <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
                  <p className="text-gray-500">This user hasn't shared any posts yet</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
