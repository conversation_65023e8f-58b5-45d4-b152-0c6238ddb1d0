'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { useAuth, type User as AuthUser } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  User, Camera, Edit, Save, PlusCircle,
  MapPin, Calendar, PawPrint, Upload, X
} from 'lucide-react';

import { Pet, VaccinationService, Vaccination } from '@/lib/pets';
import { uploadImage, generateStoragePath, validateImageFile, compressImage } from '@/lib/storage';
import { DatabaseService, COLLECTIONS } from '@/lib/database';
import PetCard from '@/components/PetCard';
import AddPetModal from '@/components/AddPetModal';

import CreatePost from '@/components/CreatePost';
import PostCard from '@/components/PostCard';
import ImageModal from '@/components/ImageModal';
import { db } from '@/lib/firebase/config';
import { doc, getDoc, getDocs, collection, query, where, orderBy, updateDoc, deleteDoc } from 'firebase/firestore';
import { RotateCcw, Maximize2, ZoomIn, MessageCircle } from 'lucide-react';

type ProfileData = Pick<AuthUser, 'name' | 'location' | 'avatar' | 'banner' | 'city'>;

interface EditingStates {
  profile: boolean;
  pet: string | null;
}

export default function ProfilePage() {
  const { user, updateProfile: updateAuthProfile, isLoading: authLoading, forceCreateUserDocument } = useAuth();
  const { pets, userPosts, refreshPets, loading: dataLoading } = useData();
  const router = useRouter();
  const searchParams = useSearchParams();
  const userId = searchParams.get('id');

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [vaccinations, setVaccinations] = useState<Vaccination[]>([]);
  const [editing, setEditing] = useState<EditingStates>({ profile: false, pet: null });
  const [isOwnProfile, setIsOwnProfile] = useState(true);
  const [viewedUser, setViewedUser] = useState<AuthUser | null>(null);
  const [viewedUserPets, setViewedUserPets] = useState<Pet[]>([]);
  const [viewedUserPosts, setViewedUserPosts] = useState<any[]>([]);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);
  const [followingCount, setFollowingCount] = useState(0);
  const [selectedImage, setSelectedImage] = useState<{ url: string; title: string } | null>(null);
  const [profileData, setProfileData] = useState<ProfileData>({
    name: '',
    location: '',
    avatar: '',
    banner: '',
    city: '',
  });
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [showAddPet, setShowAddPet] = useState(false);
  const [showBannerModal, setShowBannerModal] = useState(false);
  const [bannerPosition, setBannerPosition] = useState({ x: 50, y: 50 });
  const [bannerFit, setBannerFit] = useState<'cover' | 'contain' | 'fill'>('cover');
  const [isDragging, setIsDragging] = useState(false);

  const loadUserData = useCallback(async () => {
    try {
      setLoading(true);
      const userId = searchParams.get('id');

      console.log('🔍 Profile page loading:', {
        currentUser: user?.name || 'Not authenticated',
        currentUserRole: user?.role || 'None',
        viewingUserId: userId,
        isOwnProfile: !userId || userId === user?.id
      });

      if (userId && userId !== user?.id) {
        // Viewing someone else's profile
        console.log('👤 Viewing another user profile:', userId, 'Current user:', user?.id || 'Not authenticated');
        setIsOwnProfile(false);

        // Load the viewed user's data
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (userDoc.exists()) {
          const userData = userDoc.data() as AuthUser;
          setViewedUser({
            ...userData,
            id: userId,
            email: userData.email || '',
            name: userData.name || '',
            role: userData.role || 'pet_owner',
            verified: userData.verified || false,
            joinedDate: userData.joinedDate || new Date().toISOString(),
          });
          setProfileData({
            name: userData.name || '',
            location: userData.location || '',
            avatar: userData.avatar || '/favicon.png',
            banner: userData.banner || '/fetchlylogo.png',
            city: userData.city || ''
          });
        }

        // Load their pets
        const petsQuery = query(collection(db, 'pets'), where('userId', '==', userId));
        const petsSnapshot = await getDocs(petsQuery);
        const userPets = petsSnapshot.docs.map(doc => ({
          id: doc.id,
          userId: doc.data().userId || userId,
          name: doc.data().name || '',
          type: doc.data().type || 'dog',
          breed: doc.data().breed || '',
          age: doc.data().age || 0,
          gender: doc.data().gender || 'male',
          weight: doc.data().weight || 0,
          size: doc.data().size || 'medium',
          description: doc.data().description || '',
          avatar: doc.data().avatar || '/favicon.png',
          isActive: doc.data().isActive !== undefined ? doc.data().isActive : true,
          createdAt: doc.data().createdAt || new Date().toISOString(),
          updatedAt: doc.data().updatedAt || new Date().toISOString(),
        } as unknown as Pet)); // Using unknown as an intermediate type to handle the conversion
        setViewedUserPets(userPets);
        console.log('🐾 Loaded viewed user pets:', userPets.length, userPets);

        // Load their posts
        const postsQuery = query(
          collection(db, 'posts'),
          where('userId', '==', userId),
          where('isPublic', '==', true),
          orderBy('createdAt', 'desc')
        );
        const postsSnapshot = await getDocs(postsQuery);
        const userPosts = postsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        setViewedUserPosts(userPosts);
        console.log('📝 Loaded viewed user posts:', userPosts.length, userPosts);

        // Load follower/following counts for viewed user
        const followerQuery = query(collection(db, 'followers'), where('followedUserId', '==', userId));
        const followerSnapshot = await getDocs(followerQuery);
        setFollowerCount(followerSnapshot.docs.length);

        const followingQuery = query(collection(db, 'followers'), where('followerUserId', '==', userId));
        const followingSnapshot = await getDocs(followingQuery);
        setFollowingCount(followingSnapshot.docs.length);

        // Check if current user is following this profile
        if (user) {
          const isFollowingQuery = query(
            collection(db, 'followers'),
            where('followedUserId', '==', userId),
            where('followerUserId', '==', user.id)
          );
          const isFollowingSnapshot = await getDocs(isFollowingQuery);
          setIsFollowing(!isFollowingSnapshot.empty);
        }

      } else {
        // Own profile - user must be authenticated for this
        if (!user) {
          console.error('❌ User not authenticated for own profile');
          return;
        }

        setIsOwnProfile(true);
        const userVaccinations = await VaccinationService.getUserVaccinations(user.id);
        setVaccinations(userVaccinations);
        setProfileData({
          name: user.name || '',
          location: user.location || '',
          avatar: user.avatar || '/favicon.png',
          banner: user.banner || '/fetchlylogo.png',
          city: user.city || ''
        });

        // Load follower/following counts for own profile
        const followerQuery = query(collection(db, 'followers'), where('followedUserId', '==', user.id));
        const followerSnapshot = await getDocs(followerQuery);
        setFollowerCount(followerSnapshot.docs.length);

        const followingQuery = query(collection(db, 'followers'), where('followerUserId', '==', user.id));
        const followingSnapshot = await getDocs(followingQuery);
        setFollowingCount(followingSnapshot.docs.length);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  }, [user, searchParams]);

  // Redirect if not authenticated and initialize data
  useEffect(() => {
    if (authLoading) return; // Wait for auth to load

    const userId = searchParams.get('id');

    // If trying to view another user's profile, redirect to public profile route
    if (userId && userId !== user?.id) {
      console.log('👤 Redirecting to public profile route for user:', userId);
      router.push(`/profile/public/${userId}`);
      return;
    }

    // For own profile, require authentication
    if (!user) {
      router.push('/auth/signin');
      return;
    }

    // Initialize profile data for own profile
    setProfileData({
      name: user?.name ?? '',
      location: user?.city ?? user?.location ?? '',
      avatar: user?.avatar ?? '',
      banner: user?.banner ?? '',
      city: user?.city ?? ''
    });

    loadUserData();
  }, [user, router, loadUserData, authLoading, searchParams]);

  // Toggle edit mode
  const toggleEdit = (section: keyof EditingStates) => {
    if (!user) return;
    
    // If we're entering edit mode, initialize the form data
    if (!editing[section]) {
      setProfileData({
        name: user?.name || '',
        location: user?.city || user?.location || '',
        avatar: user?.avatar || '',
        banner: user?.banner || '',
        city: user?.city || ''
      });
    }
    
    setEditing(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleProfileUpdate = async () => {
    if (!user) return;

    try {
      setSaving(true);

      // Update user profile in Firebase
      if (!user) return;
      
      // Create update data object with only the fields that have changed
      const updateData: Partial<AuthUser> = {
        name: profileData.name || user.name,
        location: profileData.location || user.location,
        avatar: profileData.avatar || user.avatar,
        banner: profileData.banner || user.banner,
        city: profileData.city || user.city,
      };
      
      // Update in Firestore
      const userRef = doc(db, 'users', user.id);
      await updateDoc(userRef, updateData);
      
      // Update in auth context
      await updateAuthProfile(updateData);
      
      // Update local state
      setEditing({ profile: false, pet: null });
      await DatabaseService.update(COLLECTIONS.USERS, user.id, {
        name: profileData.name,
        location: profileData.city,
        avatar: profileData.avatar,
        banner: profileData.banner,
        city: profileData.city
      });

      // Update auth context
      if (updateAuthProfile) {
        await updateAuthProfile({
          name: profileData.name,
          location: profileData.city,
          avatar: profileData.avatar,
          banner: profileData.banner,
          city: profileData.city
        });
      }

      setEditing({ ...editing, profile: false });
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  const handleAvatarUpload = async (file: File) => {
    if (!user) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      alert(validation.error);
      return;
    }

    try {
      setSaving(true);

      // Compress image
      const compressedFile = await compressImage(file, 400, 400, 0.8);

      // Generate storage path
      const path = generateStoragePath(user.id, 'avatar', file.name);

      // Upload image
      const result = await uploadImage(compressedFile, path, (progress) => {
        setUploadProgress(progress.progress);
      });

      if (result.success && result.url) {
        setProfileData({ ...profileData, avatar: result.url });
        setUploadProgress(0);
      } else {
        alert(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Error uploading avatar:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  const handleBannerUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      alert(validation.error);
      return;
    }

    try {
      setSaving(true);

      // Compress image for banner (larger dimensions)
      const compressedFile = await compressImage(file, 1200, 400, 0.8);

      // Generate storage path
      const path = generateStoragePath(user.id, 'banner', file.name);

      // Upload image
      const result = await uploadImage(compressedFile, path, (progress) => {
        setUploadProgress(progress.progress);
      });

      if (result.success && result.url) {
        setProfileData({ ...profileData, banner: result.url });
        setUploadProgress(0);
      } else {
        alert(result.error || 'Banner upload failed');
      }
    } catch (error) {
      console.error('Error uploading banner:', error);
      alert('Banner upload failed. Please try again.');
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle post deletion
  const handleDeletePost = async (postId: string) => {
    try {
      await deleteDoc(doc(db, 'posts', postId));
      // Refresh posts data
      window.location.reload(); // Simple refresh for now
    } catch (error) {
      console.error('Error deleting post:', error);
      alert('Failed to delete post. Please try again.');
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your profile...</p>
        </div>
      </div>
    );
  }

  // Debug section for permissions issues
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Profile Access Issue</h2>
            <p className="text-red-600 mb-4">
              Unable to load your profile. This might be a Firebase permissions issue.
            </p>
            <button
              onClick={async () => {
                try {
                  const result = await forceCreateUserDocument();
                  if (result.success) {
                    window.location.reload();
                  } else {
                    alert('Failed to create user document: ' + result.error);
                  }
                } catch (error) {
                  console.error('Error:', error);
                  alert('Error creating user document');
                }
              }}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Fix Profile Access
            </button>
          </div>
          <p className="text-sm text-gray-500">
            If this doesn't work, please contact support.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-cyan-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
            {/* Profile Header - Enhanced Hot Design */}
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 overflow-hidden mb-8 hover:shadow-3xl transition-all duration-500">
          <div className="relative w-full h-96 bg-gradient-to-r from-green-400 via-blue-500 to-cyan-500 overflow-hidden">
            {/* Animated background pattern */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-transparent via-white/10 to-transparent animate-pulse"></div>
              <div className="absolute top-10 left-10 w-32 h-32 bg-white/5 rounded-full blur-xl animate-bounce"></div>
              <div className="absolute bottom-10 right-10 w-24 h-24 bg-cyan-200/20 rounded-full blur-lg animate-pulse"></div>
              <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-green-200/10 rounded-full blur-2xl animate-ping"></div>
            </div>
            {profileData.banner ? (
              <div className="relative w-full h-full overflow-hidden">
                <Image
                  src={profileData.banner}
                  alt="Profile banner"
                  fill
                  sizes="100vw"
                  className={`w-full h-full cursor-pointer transition-all duration-300 ${
                    bannerFit === 'cover' ? 'object-cover' :
                    bannerFit === 'contain' ? 'object-contain' : 'object-fill'
                  }`}
                  style={{
                    objectPosition: `${bannerPosition.x}% ${bannerPosition.y}%`,
                    transform: isDragging ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onClick={() => setShowBannerModal(true)}
                  onMouseDown={(e: React.MouseEvent<HTMLImageElement | HTMLDivElement>) => {
                    if (editing.profile) {
                      setIsDragging(true);
                      const rect = e.currentTarget.getBoundingClientRect();
                      const x = ((e.clientX - rect.left) / rect.width) * 100;
                      const y = ((e.clientY - rect.top) / rect.height) * 100;
                      setBannerPosition({ x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) });
                    }
                  }}
                  onMouseUp={() => setIsDragging(false)}
                  onMouseLeave={() => setIsDragging(false)}
                  priority
                />

                {/* Banner Controls */}
                {editing.profile && (
                  <div className="absolute top-4 left-4 flex gap-2">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md">
                      <select
                        value={bannerFit}
                        onChange={(e) => setBannerFit(e.target.value as 'cover' | 'contain' | 'fill')}
                        className="text-sm border-none bg-transparent focus:outline-none"
                      >
                        <option value="cover">Fit to Cover</option>
                        <option value="contain">Fit to Screen</option>
                        <option value="fill">Fill Container</option>
                      </select>
                    </div>
                    <button
                      onClick={() => setBannerPosition({ x: 50, y: 50 })}
                      className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md hover:bg-white transition-colors"
                      title="Reset Position"
                    >
                      <RotateCcw className="w-4 h-4 text-gray-600" />
                    </button>
                    <button
                      onClick={() => setShowBannerModal(true)}
                      className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md hover:bg-white transition-colors"
                      title="View Full Image"
                    >
                      <Maximize2 className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            )}

            {/* Upload Button */}
            {editing.profile && (
              <label className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-lg p-2 shadow-md hover:bg-opacity-100 cursor-pointer">
                <Upload className="w-5 h-5 text-gray-600" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleBannerUpload}
                  className="hidden"
                />
              </label>
            )}

            {/* Position Indicator */}
            {editing.profile && profileData.banner && (
              <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 text-xs text-gray-600">
                Position: {Math.round(bannerPosition.x)}%, {Math.round(bannerPosition.y)}%
              </div>
            )}
          </div>

          <div className="relative px-6 pb-6">
            <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
              {/* Profile Picture */}
              <div className="relative -mt-20 mb-6 sm:mb-0">
                <div className="relative group">
                  <div className="w-40 h-40 rounded-full bg-gradient-to-r from-green-400 to-blue-500 p-1 shadow-2xl">
                    <img
                      src={profileData.avatar || '/favicon.png'}
                      alt={profileData.name}
                      className="w-full h-full rounded-full object-cover border-4 border-white shadow-lg group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  {isOwnProfile && (
                    <label className="absolute bottom-3 right-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-full p-3 shadow-xl hover:shadow-2xl cursor-pointer transform hover:scale-110 transition-all duration-300 border-2 border-white">
                      <Camera className="w-5 h-5 text-white" />
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleAvatarUpload(file);
                        }}
                        className="hidden"
                      />
                    </label>
                  )}
                  {uploadProgress > 0 && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm rounded-full">
                      <div className="text-white text-lg font-bold">{uploadProgress}%</div>
                    </div>
                  )}
                  {/* Online status indicator */}
                  <div className="absolute top-2 right-2 w-6 h-6 bg-green-400 rounded-full border-3 border-white shadow-lg animate-pulse"></div>
                </div>
              </div>

              {/* User Info */}
              <div className="flex-1">
                {editing.profile ? (
                  <div className="space-y-4">
                    <input
                      type="text"
                      value={profileData.name}
                      onChange={handleInputChange}
                      name="name"
                      className="text-3xl font-bold text-gray-900 bg-transparent border-b-2 border-blue-500 focus:outline-none"
                      placeholder="Your name"
                    />
                    <div className="text-gray-600">
                      {pets.length > 0 ? (
                        <div className="flex items-center space-x-1">
                          <PawPrint className="w-4 h-4" />
                          <span>Pet parent to {pets.slice(0, 3).map(pet => pet.name).join(', ')}{pets.length > 3 ? ` and ${pets.length - 3} more` : ''}</span>
                        </div>
                      ) : (
                        <span>Pet lover</span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      {profileData.location && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <input
                            type="text"
                            value={profileData.location}
                            onChange={handleInputChange}
                            name="location"
                            className="bg-transparent border-b border-gray-300 focus:outline-none focus:border-blue-500"
                            placeholder="City, State"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
                        {profileData.name}
                      </h1>
                      <div className="text-gray-600 text-lg">
                        {(isOwnProfile ? pets : viewedUserPets).length > 0 ? (
                          <div className="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-full inline-flex">
                            <PawPrint className="w-5 h-5 text-green-600" />
                            <span className="font-medium">Pet parent to {(isOwnProfile ? pets : viewedUserPets).slice(0, 3).map(pet => pet.name).join(', ')}{(isOwnProfile ? pets : viewedUserPets).length > 3 ? ` and ${(isOwnProfile ? pets : viewedUserPets).length - 3} more` : ''}</span>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-full inline-flex">
                            <PawPrint className="w-5 h-5 text-blue-600" />
                            <span className="font-medium">Pet lover</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-4 text-sm">
                      {profileData.location && (
                        <div className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-xl border border-white/20">
                          <MapPin className="w-4 h-4 text-green-600" />
                          <span className="text-gray-700 font-medium">{profileData.location}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-xl border border-white/20">
                        <Calendar className="w-4 h-4 text-blue-600" />
                        <span className="text-gray-700 font-medium">Member since {user?.joinedDate ? new Date(user.joinedDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Recently'}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons - Enhanced Hot Design */}
              {isOwnProfile && (
                <div className="flex flex-wrap gap-3 mt-6 sm:mt-0">
                  {editing.profile ? (
                    <>
                      <button
                        onClick={handleProfileUpdate}
                        disabled={saving}
                        className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-2xl shadow-lg hover:shadow-xl disabled:opacity-50 flex items-center space-x-2 transform hover:scale-105 transition-all duration-300 border border-green-400"
                      >
                        <Save className="w-5 h-5" />
                        <span className="font-semibold">{saving ? 'Saving...' : 'Save Changes'}</span>
                      </button>
                      <button
                        onClick={() => {
                          setEditing(prev => ({ ...prev, profile: false }));
                          if (user) {
                            setProfileData({
                              name: user.name || '',
                              location: user.city || user.location || '',
                              avatar: user.avatar || '',
                              banner: user.banner || '',
                              city: user.city || ''
                            });
                          }
                        }}
                        className="px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white rounded-2xl shadow-lg hover:shadow-xl flex items-center space-x-2 transform hover:scale-105 transition-all duration-300 border border-gray-400"
                      >
                        <X className="w-5 h-5" />
                        <span className="font-semibold">Cancel</span>
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => setEditing(prev => ({ ...prev, profile: true }))}
                      className="px-8 py-4 bg-gradient-to-r from-green-500 via-blue-500 to-cyan-500 hover:from-green-600 hover:via-blue-600 hover:to-cyan-600 text-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center space-x-3 transform hover:scale-105 border border-white/20 backdrop-blur-sm"
                    >
                      <Edit className="w-5 h-5" />
                      <span className="font-bold text-lg">Edit Profile</span>
                    </button>
                  )}
                </div>
              )}

              {/* Follow Button for viewed profiles - Enhanced */}
              {!isOwnProfile && (
                <div className="flex flex-wrap gap-3 mt-6 sm:mt-0">
                  <button
                    onClick={() => {
                      setIsFollowing(!isFollowing);
                      console.log(isFollowing ? 'Unfollowed user:' : 'Following user:', userId);
                    }}
                    className={`px-8 py-4 rounded-2xl flex items-center space-x-3 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl border backdrop-blur-sm font-bold text-lg ${
                      isFollowing
                        ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-green-400'
                        : 'bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white border-blue-400'
                    }`}
                  >
                    <User className="w-5 h-5" />
                    <span>{isFollowing ? 'Following' : 'Follow'}</span>
                  </button>
                  <button
                    className="px-6 py-4 bg-white/60 backdrop-blur-sm hover:bg-white/80 text-gray-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-white/30 flex items-center space-x-2 font-semibold"
                  >
                    <MessageCircle className="w-5 h-5" />
                    <span>Message</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* User Stats Section - Enhanced Hot Design */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-8">
          <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl border border-white/30 p-6 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group">
            <div className="relative">
              <div className="text-4xl font-black bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                {(isOwnProfile ? pets : viewedUserPets).length}
              </div>
              <div className="absolute -top-2 -right-2 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
            <div className="text-gray-700 font-semibold text-lg">Pets</div>
            <div className="w-full h-1 bg-gradient-to-r from-green-400 to-blue-400 rounded-full mt-3 opacity-60"></div>
          </div>
          <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl border border-white/30 p-6 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group">
            <div className="relative">
              <div className="text-4xl font-black bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                {(isOwnProfile ? userPosts : viewedUserPosts).length}
              </div>
              <div className="absolute -top-2 -right-2 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
            </div>
            <div className="text-gray-700 font-semibold text-lg">Posts</div>
            <div className="w-full h-1 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full mt-3 opacity-60"></div>
          </div>
          <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl border border-white/30 p-6 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group">
            <div className="relative">
              <div className="text-4xl font-black bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                {followerCount}
              </div>
              <div className="absolute -top-2 -right-2 w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
            </div>
            <div className="text-gray-700 font-semibold text-lg">Followers</div>
            <div className="w-full h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mt-3 opacity-60"></div>
          </div>
          <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl border border-white/30 p-6 text-center hover:shadow-2xl hover:scale-105 transition-all duration-500 group">
            <div className="relative">
              <div className="text-4xl font-black bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                {followingCount}
              </div>
              <div className="absolute -top-2 -right-2 w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
            </div>
            <div className="text-gray-700 font-semibold text-lg">Following</div>
            <div className="w-full h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full mt-3 opacity-60"></div>
          </div>
        </div>

        {/* Pet Management Section - Enhanced Hot Design */}
        <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 p-8 hover:shadow-3xl transition-all duration-500">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
            <div>
              <h2 className="text-3xl font-black bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent mb-2">
                {isOwnProfile ? 'My Furry Family' : `${profileData.name}'s Pets`}
              </h2>
              <p className="text-gray-600 font-medium">
                {isOwnProfile ? 'Manage your beloved companions' : 'Meet their adorable pets'}
              </p>
            </div>
            {isOwnProfile && (
              <button
                onClick={() => setShowAddPet(true)}
                className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-2xl shadow-lg hover:shadow-xl flex items-center space-x-3 transform hover:scale-105 transition-all duration-300 border border-white/20 backdrop-blur-sm font-semibold"
              >
                <PlusCircle className="w-5 h-5" />
                <span>Add Pet</span>
              </button>
            )}
          </div>

          {(isOwnProfile ? pets : viewedUserPets).length === 0 ? (
            <div className="text-center py-16">
              <div className="relative mb-8">
                <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center mx-auto shadow-2xl">
                  <PawPrint className="w-12 h-12 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-bounce"></div>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-3">
                {isOwnProfile ? 'No furry friends yet!' : 'No pets to show'}
              </h3>
              <p className="text-gray-600 mb-8 text-lg max-w-md mx-auto">
                {isOwnProfile
                  ? 'Add your first pet to unlock amazing Fetchly services and connect with other pet parents'
                  : 'This user hasn\'t added any pets to their profile yet'
                }
              </p>
              {isOwnProfile && (
                <button
                  onClick={() => setShowAddPet(true)}
                  className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-2xl shadow-xl hover:shadow-2xl flex items-center space-x-3 mx-auto transform hover:scale-105 transition-all duration-300 border border-white/20 backdrop-blur-sm font-bold text-lg"
                >
                  <PlusCircle className="w-6 h-6" />
                  <span>Add Your First Pet</span>
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {(isOwnProfile ? pets : viewedUserPets).map((pet) => (
                <div key={pet.id} className="transform hover:scale-105 transition-all duration-300">
                  <PetCard pet={pet} onUpdate={loadUserData} />
                </div>
              ))}
            </div>
          )}
        </div>

            {/* Posts Section */}
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <h3 className="text-lg font-semibold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">
                {isOwnProfile ? 'My Posts' : `${profileData.name}'s Posts`}
              </h3>

              {/* Create Post - only show for own profile */}
              {isOwnProfile && (
                <div className="mb-6">
                  <CreatePost
                    placeholder="Share something about your pets..."
                    defaultPublic={false}
                  />
                </div>
              )}

              {/* Posts Feed */}
              <div className="space-y-6">
                {(isOwnProfile ? userPosts : viewedUserPosts).length > 0 ? (
                  (isOwnProfile ? userPosts : viewedUserPosts).map((post) => (
                    <PostCard
                      key={post.id}
                      post={post}
                      showPrivacyIndicator={isOwnProfile}
                      onDelete={isOwnProfile ? handleDeletePost : undefined}
                    />
                  ))
                ) : (
                  <div className="text-center py-12">
                    <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
                    <p className="text-gray-500">
                      {isOwnProfile
                        ? 'Share your first post about your pets!'
                        : 'This user hasn\'t shared any posts yet'
                      }
                    </p>
                  </div>
                )}
              </div>
            </div>
        </div>
      </div>

      {/* Add Pet Modal */}
      {showAddPet && (
        <AddPetModal
          onClose={() => setShowAddPet(false)}
          onSuccess={() => {
            setShowAddPet(false);
            refreshPets();
          }}
        />
      )}

      {/* Banner Image Modal */}
      {showBannerModal && profileData.banner && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-7xl max-h-full">
            <button
              onClick={() => setShowBannerModal(false)}
              className="absolute top-4 right-4 z-10 bg-white/20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-white/30 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>

            <div className="relative">
              <img
                src={profileData.banner}
                alt="Profile banner - Full view"
                className="max-w-full max-h-[90vh] object-contain rounded-lg shadow-2xl"
              />

              {/* Image Info */}
              <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 text-white">
                <div className="flex items-center gap-4 text-sm">
                  <span className="flex items-center gap-1">
                    <ZoomIn className="w-4 h-4" />
                    Full Resolution
                  </span>
                  <span>•</span>
                  <span>Click outside to close</span>
                </div>
              </div>
            </div>
          </div>

          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={() => setShowBannerModal(false)}
          />
        </div>
      )}
    </div>
  );
}

