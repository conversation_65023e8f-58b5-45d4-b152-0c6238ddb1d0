'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import {
  collection,
  query,
  where,
  getCountFromServer,
  Timestamp,
  onSnapshot,
  orderBy,
  limit
} from 'firebase/firestore';
import { auth, db } from '@/lib/firebase/config';
import { useAuth } from './AuthContext';

interface AdminStats {
  totalUsers: number;
  activeProviders: number;
  monthlyBookings: number;
  totalRevenue: number;
  recentActivity: any[];
  pendingApprovals: any[];
  isLoading: boolean;
  error: string | null;
}

interface AdminContextType extends AdminStats {
  refreshData: () => Promise<void>;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

export function AdminProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [stats, setStats] = useState<Omit<AdminStats, 'isLoading' | 'error'>>({
    totalUsers: 0,
    activeProviders: 0,
    monthlyBookings: 0,
    totalRevenue: 0,
    recentActivity: [],
    pendingApprovals: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    if (!user || user.role !== 'admin') return;

    try {
      setIsLoading(true);
      setError(null);

      // Try to get user's ID token for API authentication
      let idToken = null;
      try {
        if (auth.currentUser) {
          idToken = await auth.currentUser.getIdToken();
        }
      } catch (tokenError) {
        console.warn('Could not get ID token, falling back to direct Firebase queries');
      }

      // If we have a token, use the API endpoint
      if (idToken) {
        try {
          const response = await fetch('/api/admin/stats', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${idToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const { data } = await response.json();
            setStats({
              totalUsers: data.totalUsers || 0,
              activeProviders: data.activeProviders || 0,
              monthlyBookings: data.monthlyBookings || 0,
              totalRevenue: data.totalRevenue || 0,
              recentActivity: data.recentActivity || [],
              pendingApprovals: data.pendingApprovals || []
            });
            setupRealtimeListeners();
            return;
          }
        } catch (apiError) {
          console.warn('API call failed, falling back to direct Firebase queries:', apiError);
        }
      }

      // Fallback to direct Firebase queries
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Fetch total users count
      const usersQuery = collection(db, 'users');
      const usersSnapshot = await getCountFromServer(usersQuery);
      const totalUsers = usersSnapshot.data().count;

      // Fetch active providers count (try both collections)
      let activeProviders = 0;
      try {
        const providersQuery = query(
          collection(db, 'users'),
          where('role', '==', 'provider')
        );
        const providersSnapshot = await getCountFromServer(providersQuery);
        activeProviders = providersSnapshot.data().count;
      } catch (providerError) {
        console.warn('Error fetching providers:', providerError);
      }

      // Fetch monthly bookings count
      let monthlyBookings = 0;
      try {
        const bookingsQuery = query(
          collection(db, 'bookings'),
          where('createdAt', '>=', Timestamp.fromDate(firstDayOfMonth))
        );
        const bookingsSnapshot = await getCountFromServer(bookingsQuery);
        monthlyBookings = bookingsSnapshot.data().count;
      } catch (bookingError) {
        console.warn('Error fetching bookings:', bookingError);
      }

      setStats({
        totalUsers,
        activeProviders,
        monthlyBookings,
        totalRevenue: 0, // Will be calculated from transactions
        recentActivity: [],
        pendingApprovals: []
      });

      setupRealtimeListeners();
    } catch (err) {
      console.error('Error fetching admin stats:', err);
      setError('Failed to load admin dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const setupRealtimeListeners = () => {
    if (!user || user.role !== 'admin') return;

    // Listen for recent activity
    const activityQuery = query(
      collection(db, 'activity'),
      orderBy('timestamp', 'desc'),
      limit(10)
    );
    
    const unsubscribeActivity = onSnapshot(activityQuery, (snapshot) => {
      const activities = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      }));
      
      setStats(prev => ({
        ...prev,
        recentActivity: activities
      }));
    });

    // Listen for pending approvals
    const approvalsQuery = query(
      collection(db, 'approvals'),
      where('status', '==', 'pending')
    );
    
    const unsubscribeApprovals = onSnapshot(approvalsQuery, (snapshot) => {
      const approvals = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setStats(prev => ({
        ...prev,
        pendingApprovals: approvals
      }));
    });

    // Cleanup function
    return () => {
      unsubscribeActivity();
      unsubscribeApprovals();
    };
  };

  // Initial data fetch
  useEffect(() => {
    fetchStats();
  }, [user]);

  return (
    <AdminContext.Provider
      value={{
        ...stats,
        isLoading,
        error,
        refreshData: fetchStats
      }}
    >
      {children}
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
}
