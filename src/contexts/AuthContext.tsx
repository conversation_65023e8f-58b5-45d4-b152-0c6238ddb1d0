'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as Firebase<PERSON>ser,
  GoogleAuthProvider,
  FacebookAuthProvider,
  signInWithPopup,
  updateProfile
} from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc, Timestamp } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';
import { createProvider } from '@/lib/firebase/providers';
import { emailNotificationService } from '@/lib/services/email-service';

export type UserRole = 'pet_owner' | 'provider' | 'admin';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  banner?: string;
  city?: string;
  phone?: string;
  location?: string;
  verified?: boolean;
  joinedDate?: string;
  fetchlyBalance?: number;
  rewardPoints?: number;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string; user?: User }>;
  signOut: () => Promise<void>;
  register: (userData: RegisterData) => Promise<{ success: boolean; error?: string; user?: User }>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string; user?: User }>;
  signInWithFacebook: () => Promise<{ success: boolean; error?: string; user?: User }>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  forceCreateUserDocument: () => Promise<{ success: boolean; error?: string; user?: User }>;
  getIdToken: () => Promise<string>;
  isAuthenticated: boolean;
  firebaseUser: FirebaseUser | null;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  location?: string;
  role?: UserRole;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Helper function to create user document in Firestore
async function createUserDocument(firebaseUser: FirebaseUser, additionalData: Partial<User> = {}) {
  try {
    console.log('🔧 Creating user document for:', firebaseUser.uid);
    console.log('🔧 Additional data provided:', additionalData);
    const userRef = doc(db, COLLECTIONS.USERS, firebaseUser.uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
    const userData: User = {
      id: firebaseUser.uid,
      email: firebaseUser.email || '',
      name: firebaseUser.displayName || additionalData.name || '',
      role: additionalData.role || 'pet_owner',
      verified: firebaseUser.emailVerified,
      joinedDate: new Date().toISOString(),
      fetchlyBalance: 0,
      rewardPoints: 0,
      avatar: '/favicon.png', // Default profile picture
      banner: '/bannerdefault.png', // Default banner
      ...additionalData
    };

    console.log('🔧 User data to be saved:', userData);

    // Only add fields that are not undefined
    if (firebaseUser.photoURL || additionalData.avatar) {
      userData.avatar = firebaseUser.photoURL || additionalData.avatar;
    }
    if (additionalData.phone) {
      userData.phone = additionalData.phone;
    }
    if (additionalData.location) {
      userData.location = additionalData.location;
    }

    // Filter out undefined values before saving to Firestore
    const cleanUserData = Object.fromEntries(
      Object.entries(userData).filter(([_, value]) => value !== undefined)
    );

    console.log('🔧 Clean user data to be saved:', cleanUserData);
      await setDoc(userRef, cleanUserData);
      console.log('✅ User document created successfully with role:', userData.role);

      // If user is a provider, create a provider profile
      if (userData.role === 'provider') {
        console.log('🏥 Creating provider profile for new provider user...');
        try {
          await createProvider({
            userId: firebaseUser.uid,
            businessName: userData.name + "'s Business", // Default business name
            ownerName: userData.name,
            email: userData.email,
            phone: userData.phone || '',
            serviceType: 'General Pet Services', // Default service type
            address: '',
            city: '',
            state: '',
            zipCode: '',
            description: 'Welcome to my pet services business!',
            experience: '0-1 years',
            specialties: [],
            status: 'pending',
            verified: false,
            featured: false,
            businessHours: {
              monday: { open: '09:00', close: '17:00', closed: false },
              tuesday: { open: '09:00', close: '17:00', closed: false },
              wednesday: { open: '09:00', close: '17:00', closed: false },
              thursday: { open: '09:00', close: '17:00', closed: false },
              friday: { open: '09:00', close: '17:00', closed: false },
              saturday: { open: '09:00', close: '17:00', closed: false },
              sunday: { open: '09:00', close: '17:00', closed: true }
            },
            documents: {},
            businessPhotos: [],
            rating: 0,
            reviewCount: 0,
            totalBookings: 0,
            totalRevenue: 0,
            completionRate: 0,
            responseTime: '< 1 hour',
            responseRate: 0,
            membershipTier: 'free',
            fetchPoints: 0,
            commissionsaved: 0,
            socialMedia: {},
            settings: {
              emailNotifications: true,
              smsNotifications: false,
              bookingNotifications: true,
              marketingEmails: false,
              autoAcceptBookings: false,
              requireDeposit: false,
              cancellationPolicy: 'flexible'
            }
          });
          console.log('✅ Provider profile created successfully');
        } catch (providerError) {
          console.error('❌ Error creating provider profile:', providerError);
          // Don't fail the user creation if provider profile creation fails
        }
      }

      return userData;
    } else {
      console.log('ℹ️ User document already exists');
      const existingData = { id: firebaseUser.uid, ...userSnap.data() } as User;
      console.log('ℹ️ Existing user role:', existingData.role);
      return existingData;
    }
  } catch (error) {
    console.error('Error creating user document:', error);
    throw error;
  }
}

// Helper function to get user data from Firestore
async function getUserData(firebaseUser: FirebaseUser): Promise<User | null> {
  try {
    console.log('🔍 Getting user data for UID:', firebaseUser.uid);
    console.log('🔍 Firebase user details:', {
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      emailVerified: firebaseUser.emailVerified,
      displayName: firebaseUser.displayName
    });

    try {
      const userRef = doc(db, COLLECTIONS.USERS, firebaseUser.uid);
      console.log('🔍 Firestore path:', `users/${firebaseUser.uid}`);

      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const userData = userSnap.data();
        console.log('✅ User document found:', userData);
        console.log('🔍 User role:', userData.role);
        return { id: firebaseUser.uid, ...userData } as User;
      } else {
        console.log('❌ User document does not exist for:', firebaseUser.uid);
        return null;
      }
    } catch (error) {
      console.error('❌ Error accessing user document:', error);
      // Return basic user data from Firebase Auth if Firestore fails
      const fallbackUser = {
        id: firebaseUser.uid,
        email: firebaseUser.email || '',
        name: firebaseUser.displayName || '',
        avatar: firebaseUser.photoURL || '',
        role: 'pet_owner' // Default role
      } as User;
      console.log('🔄 Returning fallback user:', fallbackUser);
      return fallbackUser;
    }
  } catch (error: any) {
    console.error('❌ Error getting user data:', error);
    console.error('❌ Error code:', error.code);
    console.error('❌ Error message:', error.message);
    return null;
  }
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setIsLoading(true);
      setFirebaseUser(firebaseUser);

      if (firebaseUser) {
        try {
          console.log('🔥 Firebase User:', {
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            displayName: firebaseUser.displayName,
            emailVerified: firebaseUser.emailVerified
          });

          // Get user data from Firestore
          const userData = await getUserData(firebaseUser);
          if (userData) {
            console.log('✅ User data found:', userData);
            setUser(userData);
          } else {
            // Create user document if it doesn't exist
            console.log('🆕 Creating new user document...');
            const newUser = await createUserDocument(firebaseUser);
            console.log('✅ New user created:', newUser);
            setUser(newUser);
          }
        } catch (error) {
          console.error('❌ Error in auth state change:', error);
          // Don't create a fallback user document here as it might override the correct role
          // Instead, let the user try to sign in again or contact support
          console.log('🚫 Not creating fallback user document to preserve role integrity');
          setUser(null);
        }
      } else {
        console.log('🚫 No Firebase user');
        setUser(null);
      }

      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signIn = async (email: string, password: string): Promise<{ success: boolean; error?: string; user?: User }> => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Check if this is the admin user first
      const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
      if (email === adminEmail) {
        // This is the admin user - get their data from Firestore
        const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, firebaseUser.uid));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const adminUser: User = {
            id: firebaseUser.uid,
            email: firebaseUser.email!,
            name: userData.name || userData.displayName || 'Admin User',
            role: 'admin',
            avatar: userData.avatar,
            phone: userData.phone,
            location: userData.location,
            verified: true,
            joinedDate: userData.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
            fetchlyBalance: 0,
            rewardPoints: 0
          };
          return { success: true, user: adminUser };
        } else {
          // Create admin user document if it doesn't exist
          const adminData = {
            email: firebaseUser.email,
            name: 'Admin User',
            displayName: 'Admin User',
            role: 'admin',
            isAdmin: true,
            superAdmin: true,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          await setDoc(doc(db, COLLECTIONS.USERS, firebaseUser.uid), adminData);

          const adminUser: User = {
            id: firebaseUser.uid,
            email: firebaseUser.email!,
            name: 'Admin User',
            role: 'admin',
            avatar: undefined,
            phone: undefined,
            location: undefined,
            verified: true,
            joinedDate: new Date().toISOString(),
            fetchlyBalance: 0,
            rewardPoints: 0
          };
          return { success: true, user: adminUser };
        }
      }

      // For non-admin users, use the existing logic
      let userData = await getUserData(firebaseUser);
      if (!userData) {
        userData = await createUserDocument(firebaseUser);
      }

      return { success: true, user: userData };
    } catch (error: any) {
      let errorMessage = 'Sign in failed';

      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email address';
          break;
        case 'auth/wrong-password':
          errorMessage = 'Incorrect password';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address';
          break;
        case 'auth/user-disabled':
          errorMessage = 'This account has been disabled';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later';
          break;
        default:
          errorMessage = error.message || 'Sign in failed';
      }

      return { success: false, error: errorMessage };
    }
  };

  const register = async (userData: RegisterData): Promise<{ success: boolean; error?: string; user?: User }> => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
      const firebaseUser = userCredential.user;

      // Update display name if provided
      if (userData.name) {
        await updateProfile(firebaseUser, {
          displayName: userData.name
        });
      }

      // Create user document in Firestore
      const newUser = await createUserDocument(firebaseUser, {
        name: userData.name,
        phone: userData.phone,
        location: userData.location,
        role: userData.role || 'pet_owner'
      });

      // Send email notification for new signup
      try {
        await emailNotificationService.notifyUserSignup({
          userId: newUser.id,
          email: newUser.email,
          name: newUser.name || 'Unknown',
          role: newUser.role === 'provider' ? 'provider' : 'petowner',
          timestamp: new Date()
        });
      } catch (emailError) {
        console.error('Failed to send signup notification email:', emailError);
        // Don't fail registration if email fails
      }

      return { success: true, user: newUser };
    } catch (error: any) {
      let errorMessage = 'Registration failed';

      switch (error.code) {
        case 'auth/email-already-in-use':
          errorMessage = 'An account with this email already exists';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address';
          break;
        case 'auth/weak-password':
          errorMessage = 'Password should be at least 6 characters';
          break;
        case 'auth/operation-not-allowed':
          errorMessage = 'Email/password accounts are not enabled';
          break;
        default:
          errorMessage = error.message || 'Registration failed';
      }

      return { success: false, error: errorMessage };
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
      setUser(null);
      setFirebaseUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const signInWithGoogle = async (): Promise<{ success: boolean; error?: string; user?: User }> => {
    try {
      const provider = new GoogleAuthProvider();
      const userCredential = await signInWithPopup(auth, provider);
      const firebaseUser = userCredential.user;

      // Get or create user data in Firestore
      let userData = await getUserData(firebaseUser);
      if (!userData) {
        userData = await createUserDocument(firebaseUser);
      }

      return { success: true, user: userData };
    } catch (error: any) {
      let errorMessage = 'Google sign in failed';

      if (error.code === 'auth/popup-closed-by-user') {
        errorMessage = 'Sign in was cancelled';
      } else if (error.code === 'auth/popup-blocked') {
        errorMessage = 'Popup was blocked by browser';
      }

      return { success: false, error: errorMessage };
    }
  };

  const signInWithFacebook = async (): Promise<{ success: boolean; error?: string; user?: User }> => {
    try {
      const provider = new FacebookAuthProvider();
      const userCredential = await signInWithPopup(auth, provider);
      const firebaseUser = userCredential.user;

      // Get or create user data in Firestore
      let userData = await getUserData(firebaseUser);
      if (!userData) {
        userData = await createUserDocument(firebaseUser);
      }

      return { success: true, user: userData };
    } catch (error: any) {
      let errorMessage = 'Facebook sign in failed';

      if (error.code === 'auth/popup-closed-by-user') {
        errorMessage = 'Sign in was cancelled';
      } else if (error.code === 'auth/popup-blocked') {
        errorMessage = 'Popup was blocked by browser';
      }

      return { success: false, error: errorMessage };
    }
  };

  // Update user profile in Firestore and local state
  const updateUserProfile = async (updates: Partial<User>): Promise<void> => {
    if (!user || !firebaseUser) return;
    
    try {
      // Update Firestore
      const userRef = doc(db, COLLECTIONS.USERS, user.id);
      await updateDoc(userRef, updates);
      
      // Update local state
      setUser(prev => prev ? { ...prev, ...updates } : null);
      
      // Update Firebase Auth profile if name or photoURL is being updated
      if (updates.name || updates.avatar) {
        await updateProfile(auth.currentUser!, {
          displayName: updates.name || user.name,
          photoURL: updates.avatar || user.avatar
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  };

  // Force create user document if it doesn't exist
  const forceCreateUserDocument = async () => {
    if (!firebaseUser) return { success: false, error: 'Not authenticated' };

    try {
      console.log('Force creating user document...');
      const newUser = await createUserDocument(firebaseUser);
      setUser(newUser);
      return { success: true, user: newUser };
    } catch (error: any) {
      console.error('Error force creating user document:', error);
      return { success: false, error: error.message };
    }
  };

  const getIdToken = async (): Promise<string> => {
    if (!firebaseUser) {
      throw new Error('Not authenticated');
    }
    return await firebaseUser.getIdToken();
  };

  const value: AuthContextType = {
    user,
    isLoading,
    signIn,
    signOut,
    register,
    signInWithGoogle,
    signInWithFacebook,
    updateProfile: updateUserProfile,
    forceCreateUserDocument,
    getIdToken,
    isAuthenticated: !!user,
    firebaseUser
  };

  return (
    <AuthContext.Provider value={value}>
      {isLoading ? null : children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// All user accounts are now managed through Firebase Authentication
// Register new accounts through the application interface
