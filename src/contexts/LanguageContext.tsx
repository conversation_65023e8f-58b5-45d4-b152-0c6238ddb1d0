'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'es';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Translation keys and values
const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.services': 'Services',
    'nav.providers': 'Providers',
    'nav.community': 'Community',
    'nav.dashboard': 'Dashboard',
    'nav.profile': 'Profile',
    'nav.wallet': 'Wallet',
    'nav.messages': 'Messages',
    'nav.settings': 'Settings',
    'nav.help': 'Help',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'nav.signin': 'Sign In',
    'nav.signup': 'Sign Up',
    'nav.signout': 'Sign Out',
    
    // Common
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.close': 'Close',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.submit': 'Submit',
    'common.confirm': 'Confirm',
    'common.yes': 'Yes',
    'common.no': 'No',
    
    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.welcome': 'Welcome back',
    'dashboard.overview': 'Overview',
    'dashboard.bookings': 'Bookings',
    'dashboard.earnings': 'Earnings',
    'dashboard.pets': 'My Pets',
    'dashboard.recent': 'Recent Activity',
    
    // Community
    'community.title': 'Pet Community',
    'community.subtitle': 'Connect with pet lovers worldwide',
    'community.search': 'Search posts, people, topics...',
    'community.createPost': 'Create Post',
    'community.stories': 'Stories',
    'community.trending': 'Trending',
    'community.members': 'Members',
    'community.posts': 'Posts',
    'community.likes': 'Likes',
    'community.noPostsFound': 'No posts found',
    'community.adjustFilters': 'Try adjusting your search or filters',
    
    // Wallet
    'wallet.title': 'Fetchly Wallet',
    'wallet.subtitle': 'Secure and convenient payments',
    'wallet.balance': 'Available Balance',
    'wallet.totalAdded': 'Total Added',
    'wallet.transactions': 'Transactions',
    'wallet.addFunds': 'Add Funds',
    'wallet.history': 'History',
    'wallet.quickPay': 'Quick Pay',
    'wallet.quickAdd': 'Quick Add',
    'wallet.securedBy': 'Secured by Stripe',
    
    // Services
    'services.veterinary': 'Veterinary',
    'services.grooming': 'Grooming',
    'services.training': 'Training',
    'services.daycare': 'Daycare',
    'services.hotels': 'Pet Hotels',
    'services.findServices': 'Find Services',
    'services.bookNow': 'Book Now',
    
    // Profile
    'profile.title': 'Profile',
    'profile.editProfile': 'Edit Profile',
    'profile.myPets': 'My Pets',
    'profile.addPet': 'Add Pet',
    'profile.settings': 'Settings',
    'profile.privacy': 'Privacy',
    'profile.petOwner': 'Pet Owner',
    'profile.serviceProvider': 'Service Provider',
    'profile.admin': 'Admin',
    
    // Language
    'language.english': 'English',
    'language.spanish': 'Español',
    'language.selectLanguage': 'Select Language',

    // Homepage
    'home.hero.title': 'Find Trusted Pet Care Services',
    'home.hero.subtitle': 'Connect with verified pet care professionals in your area',
    'home.hero.cta': 'Find Services',
    'home.features.title': 'Why Choose Fetchly?',
    'home.features.trusted': 'Trusted Providers',
    'home.features.trustedDesc': 'All providers are verified and reviewed',
    'home.features.convenient': 'Convenient Booking',
    'home.features.convenientDesc': 'Book services with just a few clicks',
    'home.features.secure': 'Secure Payments',
    'home.features.secureDesc': 'Safe and secure payment processing',

    // Chat System
    'chat.title': 'Messages',
    'chat.startNewChat': 'Start New Chat',
    'chat.contactSupport': 'Contact Support',
    'chat.searchConversations': 'Search conversations...',
    'chat.typeMessage': 'Type a message...',
    'chat.sendMessage': 'Send Message',
    'chat.online': 'Online',
    'chat.offline': 'Offline',
    'chat.typing': 'typing...',
    'chat.selectUsers': 'Select users to chat with',
    'chat.createChat': 'Create Chat',
    'chat.groupName': 'Group Name (optional)',
    'chat.supportCategory': 'Support Category',
    'chat.subject': 'Subject',
    'chat.message': 'Message',
    'chat.startSupportChat': 'Start Support Chat',

    // Newsletter
    'newsletter.title': 'Stay Updated',
    'newsletter.description': 'Get the latest pet care tips and updates delivered to your inbox.',
    'newsletter.placeholder': 'Enter your email address',
    'newsletter.subscribe': 'Subscribe',
    'newsletter.subscribing': 'Subscribing...',
    'newsletter.success': 'Successfully Subscribed!',
    'newsletter.successMessage': 'Thank you for joining our newsletter.',
    'newsletter.privacy': 'We respect your privacy. Unsubscribe at any time.',

    // Community
    'community.title': 'Pet Community',
    'community.createPost': 'Create Post',
    'community.whatsOnMind': "What's on your mind?",
    'community.sharePhoto': 'Share a photo',
    'community.createStory': 'Create Story',
    'community.viewStories': 'View Stories',
    'community.like': 'Like',
    'community.comment': 'Comment',
    'community.share': 'Share',
    'community.follow': 'Follow',
    'community.following': 'Following',

    // Booking
    'booking.title': 'Book Service',
    'booking.selectDate': 'Select Date',
    'booking.selectTime': 'Select Time',
    'booking.petDetails': 'Pet Details',
    'booking.specialRequests': 'Special Requests',
    'booking.confirmBooking': 'Confirm Booking',
    'booking.totalCost': 'Total Cost',
    'booking.bookNow': 'Book Now',

    // Provider
    'provider.dashboard': 'Provider Dashboard',
    'provider.bookings': 'My Bookings',
    'provider.services': 'My Services',
    'provider.earnings': 'Earnings',
    'provider.customers': 'Customers',
    'provider.analytics': 'Analytics',
    'provider.settings': 'Settings',
    'provider.addService': 'Add Service',
    'provider.editService': 'Edit Service',

    // Footer
    'footer.company': 'Company',
    'footer.services': 'Services',
    'footer.support': 'Support',
    'footer.legal': 'Legal',
    'footer.followUs': 'Follow Us',
    'footer.allRightsReserved': 'All rights reserved.',
    'footer.privacyPolicy': 'Privacy Policy',
    'footer.termsOfService': 'Terms of Service',
    'footer.cookiePolicy': 'Cookie Policy',
  },
  es: {
    // Navigation
    'nav.home': 'Inicio',
    'nav.services': 'Servicios',
    'nav.providers': 'Proveedores',
    'nav.community': 'Comunidad',
    'nav.dashboard': 'Panel',
    'nav.profile': 'Perfil',
    'nav.wallet': 'Billetera',
    'nav.messages': 'Mensajes',
    'nav.settings': 'Configuración',
    'nav.help': 'Ayuda',
    'nav.about': 'Acerca de',
    'nav.contact': 'Contacto',
    'nav.signin': 'Iniciar Sesión',
    'nav.signup': 'Registrarse',
    'nav.signout': 'Cerrar Sesión',
    
    // Common
    'common.loading': 'Cargando...',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar',
    'common.delete': 'Eliminar',
    'common.edit': 'Editar',
    'common.view': 'Ver',
    'common.search': 'Buscar',
    'common.filter': 'Filtrar',
    'common.close': 'Cerrar',
    'common.back': 'Atrás',
    'common.next': 'Siguiente',
    'common.previous': 'Anterior',
    'common.submit': 'Enviar',
    'common.confirm': 'Confirmar',
    'common.yes': 'Sí',
    'common.no': 'No',
    
    // Dashboard
    'dashboard.title': 'Panel de Control',
    'dashboard.welcome': 'Bienvenido de nuevo',
    'dashboard.overview': 'Resumen',
    'dashboard.bookings': 'Reservas',
    'dashboard.earnings': 'Ganancias',
    'dashboard.pets': 'Mis Mascotas',
    'dashboard.recent': 'Actividad Reciente',
    
    // Community
    'community.title': 'Comunidad de Mascotas',
    'community.subtitle': 'Conecta con amantes de las mascotas en todo el mundo',
    'community.search': 'Buscar publicaciones, personas, temas...',
    'community.createPost': 'Crear Publicación',
    'community.stories': 'Historias',
    'community.trending': 'Tendencias',
    'community.members': 'Miembros',
    'community.posts': 'Publicaciones',
    'community.likes': 'Me Gusta',
    'community.noPostsFound': 'No se encontraron publicaciones',
    'community.adjustFilters': 'Intenta ajustar tu búsqueda o filtros',
    
    // Wallet
    'wallet.title': 'Billetera Fetchly',
    'wallet.subtitle': 'Pagos seguros y convenientes',
    'wallet.balance': 'Saldo Disponible',
    'wallet.totalAdded': 'Total Agregado',
    'wallet.transactions': 'Transacciones',
    'wallet.addFunds': 'Agregar Fondos',
    'wallet.history': 'Historial',
    'wallet.quickPay': 'Pago Rápido',
    'wallet.quickAdd': 'Agregar Rápido',
    'wallet.securedBy': 'Protegido por Stripe',
    
    // Services
    'services.veterinary': 'Veterinaria',
    'services.grooming': 'Peluquería',
    'services.training': 'Entrenamiento',
    'services.daycare': 'Guardería',
    'services.hotels': 'Hoteles para Mascotas',
    'services.findServices': 'Encontrar Servicios',
    'services.bookNow': 'Reservar Ahora',
    
    // Profile
    'profile.title': 'Perfil',
    'profile.editProfile': 'Editar Perfil',
    'profile.myPets': 'Mis Mascotas',
    'profile.addPet': 'Agregar Mascota',
    'profile.settings': 'Configuración',
    'profile.privacy': 'Privacidad',
    'profile.petOwner': 'Dueño de Mascota',
    'profile.serviceProvider': 'Proveedor de Servicios',
    'profile.admin': 'Administrador',
    
    // Language
    'language.english': 'English',
    'language.spanish': 'Español',
    'language.selectLanguage': 'Seleccionar Idioma',

    // Homepage
    'home.hero.title': 'Encuentra Servicios de Cuidado de Mascotas Confiables',
    'home.hero.subtitle': 'Conéctate con profesionales verificados del cuidado de mascotas en tu área',
    'home.hero.cta': 'Encontrar Servicios',
    'home.features.title': '¿Por Qué Elegir Fetchly?',
    'home.features.trusted': 'Proveedores Confiables',
    'home.features.trustedDesc': 'Todos los proveedores están verificados y evaluados',
    'home.features.convenient': 'Reserva Conveniente',
    'home.features.convenientDesc': 'Reserva servicios con solo unos clics',
    'home.features.secure': 'Pagos Seguros',
    'home.features.secureDesc': 'Procesamiento de pagos seguro y protegido',

    // Chat System
    'chat.title': 'Mensajes',
    'chat.startNewChat': 'Iniciar Nuevo Chat',
    'chat.contactSupport': 'Contactar Soporte',
    'chat.searchConversations': 'Buscar conversaciones...',
    'chat.typeMessage': 'Escribe un mensaje...',
    'chat.sendMessage': 'Enviar Mensaje',
    'chat.online': 'En Línea',
    'chat.offline': 'Desconectado',
    'chat.typing': 'escribiendo...',
    'chat.selectUsers': 'Selecciona usuarios para chatear',
    'chat.createChat': 'Crear Chat',
    'chat.groupName': 'Nombre del Grupo (opcional)',
    'chat.supportCategory': 'Categoría de Soporte',
    'chat.subject': 'Asunto',
    'chat.message': 'Mensaje',
    'chat.startSupportChat': 'Iniciar Chat de Soporte',

    // Newsletter
    'newsletter.title': 'Mantente Actualizado',
    'newsletter.description': 'Recibe los últimos consejos de cuidado de mascotas y actualizaciones en tu bandeja de entrada.',
    'newsletter.placeholder': 'Ingresa tu dirección de correo electrónico',
    'newsletter.subscribe': 'Suscribirse',
    'newsletter.subscribing': 'Suscribiendo...',
    'newsletter.success': '¡Suscripción Exitosa!',
    'newsletter.successMessage': 'Gracias por unirte a nuestro boletín.',
    'newsletter.privacy': 'Respetamos tu privacidad. Cancela la suscripción en cualquier momento.',

    // Community
    'community.title': 'Comunidad de Mascotas',
    'community.createPost': 'Crear Publicación',
    'community.whatsOnMind': '¿Qué tienes en mente?',
    'community.sharePhoto': 'Compartir una foto',
    'community.createStory': 'Crear Historia',
    'community.viewStories': 'Ver Historias',
    'community.like': 'Me Gusta',
    'community.comment': 'Comentar',
    'community.share': 'Compartir',
    'community.follow': 'Seguir',
    'community.following': 'Siguiendo',

    // Booking
    'booking.title': 'Reservar Servicio',
    'booking.selectDate': 'Seleccionar Fecha',
    'booking.selectTime': 'Seleccionar Hora',
    'booking.petDetails': 'Detalles de la Mascota',
    'booking.specialRequests': 'Solicitudes Especiales',
    'booking.confirmBooking': 'Confirmar Reserva',
    'booking.totalCost': 'Costo Total',
    'booking.bookNow': 'Reservar Ahora',

    // Provider
    'provider.dashboard': 'Panel del Proveedor',
    'provider.bookings': 'Mis Reservas',
    'provider.services': 'Mis Servicios',
    'provider.earnings': 'Ganancias',
    'provider.customers': 'Clientes',
    'provider.analytics': 'Analíticas',
    'provider.settings': 'Configuración',
    'provider.addService': 'Agregar Servicio',
    'provider.editService': 'Editar Servicio',

    // Footer
    'footer.company': 'Empresa',
    'footer.services': 'Servicios',
    'footer.support': 'Soporte',
    'footer.legal': 'Legal',
    'footer.followUs': 'Síguenos',
    'footer.allRightsReserved': 'Todos los derechos reservados.',
    'footer.privacyPolicy': 'Política de Privacidad',
    'footer.termsOfService': 'Términos de Servicio',
    'footer.cookiePolicy': 'Política de Cookies',
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>('en');

  // Load language from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('fetchly-language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'es')) {
      setLanguageState(savedLanguage);
    }
  }, []);

  // Save language to localStorage when changed
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('fetchly-language', lang);
  };

  // Translation function
  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
