'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'es';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Translation keys and values
const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.services': 'Services',
    'nav.providers': 'Providers',
    'nav.community': 'Community',
    'nav.dashboard': 'Dashboard',
    'nav.profile': 'Profile',
    'nav.wallet': 'Wallet',
    'nav.messages': 'Messages',
    'nav.settings': 'Settings',
    'nav.help': 'Help',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'nav.signin': 'Sign In',
    'nav.signup': 'Sign Up',
    'nav.signout': 'Sign Out',
    
    // Common
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.close': 'Close',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.submit': 'Submit',
    'common.confirm': 'Confirm',
    'common.yes': 'Yes',
    'common.no': 'No',
    
    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.welcome': 'Welcome back',
    'dashboard.overview': 'Overview',
    'dashboard.bookings': 'Bookings',
    'dashboard.earnings': 'Earnings',
    'dashboard.pets': 'My Pets',
    'dashboard.recent': 'Recent Activity',
    
    // Community
    'community.title': 'Pet Community',
    'community.subtitle': 'Connect with pet lovers worldwide',
    'community.search': 'Search posts, people, topics...',
    'community.createPost': 'Create Post',
    'community.stories': 'Stories',
    'community.trending': 'Trending',
    'community.members': 'Members',
    'community.posts': 'Posts',
    'community.likes': 'Likes',
    'community.noPostsFound': 'No posts found',
    'community.adjustFilters': 'Try adjusting your search or filters',
    
    // Wallet
    'wallet.title': 'Fetchly Wallet',
    'wallet.subtitle': 'Secure and convenient payments',
    'wallet.balance': 'Available Balance',
    'wallet.totalAdded': 'Total Added',
    'wallet.transactions': 'Transactions',
    'wallet.addFunds': 'Add Funds',
    'wallet.history': 'History',
    'wallet.quickPay': 'Quick Pay',
    'wallet.quickAdd': 'Quick Add',
    'wallet.securedBy': 'Secured by Stripe',
    
    // Services
    'services.veterinary': 'Veterinary',
    'services.grooming': 'Grooming',
    'services.training': 'Training',
    'services.daycare': 'Daycare',
    'services.hotels': 'Pet Hotels',
    'services.findServices': 'Find Services',
    'services.bookNow': 'Book Now',
    
    // Profile
    'profile.title': 'Profile',
    'profile.editProfile': 'Edit Profile',
    'profile.myPets': 'My Pets',
    'profile.addPet': 'Add Pet',
    'profile.settings': 'Settings',
    'profile.privacy': 'Privacy',
    
    // Language
    'language.english': 'English',
    'language.spanish': 'Español',
    'language.selectLanguage': 'Select Language',
  },
  es: {
    // Navigation
    'nav.home': 'Inicio',
    'nav.services': 'Servicios',
    'nav.providers': 'Proveedores',
    'nav.community': 'Comunidad',
    'nav.dashboard': 'Panel',
    'nav.profile': 'Perfil',
    'nav.wallet': 'Billetera',
    'nav.messages': 'Mensajes',
    'nav.settings': 'Configuración',
    'nav.help': 'Ayuda',
    'nav.about': 'Acerca de',
    'nav.contact': 'Contacto',
    'nav.signin': 'Iniciar Sesión',
    'nav.signup': 'Registrarse',
    'nav.signout': 'Cerrar Sesión',
    
    // Common
    'common.loading': 'Cargando...',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar',
    'common.delete': 'Eliminar',
    'common.edit': 'Editar',
    'common.view': 'Ver',
    'common.search': 'Buscar',
    'common.filter': 'Filtrar',
    'common.close': 'Cerrar',
    'common.back': 'Atrás',
    'common.next': 'Siguiente',
    'common.previous': 'Anterior',
    'common.submit': 'Enviar',
    'common.confirm': 'Confirmar',
    'common.yes': 'Sí',
    'common.no': 'No',
    
    // Dashboard
    'dashboard.title': 'Panel de Control',
    'dashboard.welcome': 'Bienvenido de nuevo',
    'dashboard.overview': 'Resumen',
    'dashboard.bookings': 'Reservas',
    'dashboard.earnings': 'Ganancias',
    'dashboard.pets': 'Mis Mascotas',
    'dashboard.recent': 'Actividad Reciente',
    
    // Community
    'community.title': 'Comunidad de Mascotas',
    'community.subtitle': 'Conecta con amantes de las mascotas en todo el mundo',
    'community.search': 'Buscar publicaciones, personas, temas...',
    'community.createPost': 'Crear Publicación',
    'community.stories': 'Historias',
    'community.trending': 'Tendencias',
    'community.members': 'Miembros',
    'community.posts': 'Publicaciones',
    'community.likes': 'Me Gusta',
    'community.noPostsFound': 'No se encontraron publicaciones',
    'community.adjustFilters': 'Intenta ajustar tu búsqueda o filtros',
    
    // Wallet
    'wallet.title': 'Billetera Fetchly',
    'wallet.subtitle': 'Pagos seguros y convenientes',
    'wallet.balance': 'Saldo Disponible',
    'wallet.totalAdded': 'Total Agregado',
    'wallet.transactions': 'Transacciones',
    'wallet.addFunds': 'Agregar Fondos',
    'wallet.history': 'Historial',
    'wallet.quickPay': 'Pago Rápido',
    'wallet.quickAdd': 'Agregar Rápido',
    'wallet.securedBy': 'Protegido por Stripe',
    
    // Services
    'services.veterinary': 'Veterinaria',
    'services.grooming': 'Peluquería',
    'services.training': 'Entrenamiento',
    'services.daycare': 'Guardería',
    'services.hotels': 'Hoteles para Mascotas',
    'services.findServices': 'Encontrar Servicios',
    'services.bookNow': 'Reservar Ahora',
    
    // Profile
    'profile.title': 'Perfil',
    'profile.editProfile': 'Editar Perfil',
    'profile.myPets': 'Mis Mascotas',
    'profile.addPet': 'Agregar Mascota',
    'profile.settings': 'Configuración',
    'profile.privacy': 'Privacidad',
    
    // Language
    'language.english': 'English',
    'language.spanish': 'Español',
    'language.selectLanguage': 'Seleccionar Idioma',
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>('en');

  // Load language from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('fetchly-language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'es')) {
      setLanguageState(savedLanguage);
    }
  }, []);

  // Save language to localStorage when changed
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('fetchly-language', lang);
  };

  // Translation function
  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
