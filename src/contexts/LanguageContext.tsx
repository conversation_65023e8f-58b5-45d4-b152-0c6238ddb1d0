'use client';

import React, { createContext, useContext, ReactNode } from 'react';

interface LanguageContextType {
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// English translations
const translations: Record<string, string> = {
  'nav.home': 'Home',
  'nav.services': 'Services',
  'nav.providers': 'Providers',
  'nav.community': 'Community',
  'nav.dashboard': 'Dashboard',
  'nav.profile': 'Profile',
  'common.loading': 'Loading...',
  'common.save': 'Save',
  'common.cancel': 'Cancel',
  'community.title': 'Community',
  'community.subtitle': 'Connect with pet lovers around the world',
  'community.search': 'Search community posts...',
  'community.createPost': 'Create Post',
  'community.filters.allPosts': 'All Posts',
  'community.filters.following': 'Following',
  'community.filters.trending': 'Trending',
  'community.filters.photos': 'Photos',
  'community.filters.videos': 'Videos',
  'community.explore': 'Explore',
  'community.trending': 'Trending',
  'community.noTrendingTopics': 'No trending topics yet',
  'community.startUsingHashtags': 'Start using hashtags in your posts!',
  'community.onlineNow': 'Online Now',
};

export function LanguageProvider({ children }: { children: ReactNode }) {
  // Set HTML lang to English
  if (typeof document !== 'undefined') {
    document.documentElement.lang = 'en';
  }

  const t = (key: string): string => {
    return translations[key] || key;
  };

  return (
    <LanguageContext.Provider value={{ t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

export default LanguageContext;
