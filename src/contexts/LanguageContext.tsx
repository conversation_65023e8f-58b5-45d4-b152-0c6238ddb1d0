'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'es';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Basic translations - we'll move the rest to separate files later
interface Translations {
  [key: string]: string;
}

interface TranslationsMap {
  en: Translations;
  es: Translations;
  [key: string]: Translations;
}

const translations: TranslationsMap = {
  en: {
    'nav.home': 'Home',
    'nav.services': 'Services',
    'nav.providers': 'Providers',
    'nav.community': 'Community',
    'nav.dashboard': 'Dashboard',
    'nav.profile': 'Profile',
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'language.select': 'Select Language',
    'community.title': 'Community',
    'community.subtitle': 'Connect with pet lovers around the world',
    'community.search': 'Search community posts...',
    'community.createPost': 'Create Post',
    'community.filters.allPosts': 'All Posts',
    'community.filters.following': 'Following',
    'community.filters.trending': 'Trending',
    'community.filters.photos': 'Photos',
    'community.filters.videos': 'Videos',
    'community.explore': 'Explore',
    'community.trending': 'Trending',
    'community.noTrendingTopics': 'No trending topics yet',
    'community.startUsingHashtags': 'Start using hashtags in your posts!',
    'community.onlineNow': 'Online Now',
  },
  es: {
    'nav.home': 'Inicio',
    'nav.services': 'Servicios',
    'nav.providers': 'Proveedores',
    'nav.community': 'Comunidad',
    'nav.dashboard': 'Panel',
    'nav.profile': 'Perfil',
    'common.loading': 'Cargando...',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar',
    'language.select': 'Seleccionar Idioma',
    'community.title': 'Comunidad',
    'community.subtitle': 'Conéctate con amantes de las mascotas de todo el mundo',
    'community.search': 'Buscar publicaciones...',
    'community.createPost': 'Crear Publicación',
    'community.filters.allPosts': 'Todas las Publicaciones',
    'community.filters.following': 'Siguiendo',
    'community.filters.trending': 'Tendencias',
    'community.filters.photos': 'Fotos',
    'community.filters.videos': 'Videos',
    'community.explore': 'Explorar',
    'community.trending': 'Tendencias',
    'community.noTrendingTopics': 'Aún no hay temas populares',
    'community.startUsingHashtags': '¡Usa hashtags en tus publicaciones!',
    'community.onlineNow': 'En Línea',
  }
};

export function LanguageProvider({ children }: { children: ReactNode }) {
  // Force English language only
  const [language, setLanguageState] = useState<Language>('en');

  // Update HTML lang attribute when language changes
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.lang = language;
    }
  }, [language]);

  // Load language from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedLanguage = localStorage.getItem('fetchly-language') as Language;
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'es')) {
          setLanguageState(savedLanguage);
        }
      } catch (error) {
        console.error('Error loading language from localStorage:', error);
      }
    }
  }, []);

  const setLanguage = (lang: Language) => {
    // Force English only - ignore language changes
    console.log('Language switching disabled - staying in English');
    return;
  };

  // Translation function with fallback to English for missing keys
  const t = (key: string): string => {
    // Try to get the translation for the current language
    const translation = translations[language]?.[key];
    
    // If translation exists, return it
    if (translation) return translation;
    
    // If not found in current language, try English as fallback
    if (language !== 'en') {
      const enTranslation = translations.en?.[key];
      if (enTranslation) return enTranslation;
    }
    
    // If not found in either language, return the key in development for easier debugging
    if (process.env.NODE_ENV === 'development') {
      console.warn(`Missing translation for key: ${key}`);
    }
    
    return key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

export default LanguageContext;
