'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'es';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Basic translations - we'll move the rest to separate files later
interface Translations {
  [key: string]: string;
}

interface TranslationsMap {
  en: Translations;
  es: Translations;
  [key: string]: Translations;
}

const translations: TranslationsMap = {
  en: {
    'nav.home': 'Home',
    'nav.services': 'Services',
    'nav.providers': 'Providers',
    'nav.community': 'Community',
    'nav.dashboard': 'Dashboard',
    'nav.profile': 'Profile',
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'language.select': 'Select Language',
  },
  es: {
    'nav.home': 'Inicio',
    'nav.services': 'Servicios',
    'nav.providers': 'Proveedores',
    'nav.community': 'Comunidad',
    'nav.dashboard': 'Panel',
    'nav.profile': 'Perfil',
    'common.loading': 'Cargando...',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar',
    'language.select': 'Seleccionar Idioma',
  }
};

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguageState] = useState<Language>('en');

  // Update HTML lang attribute when language changes
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.lang = language;
    }
  }, [language]);

  // Load language from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedLanguage = localStorage.getItem('fetchly-language') as Language;
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'es')) {
          setLanguageState(savedLanguage);
        }
      } catch (error) {
        console.error('Error loading language from localStorage:', error);
      }
    }
  }, []);

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem('fetchly-language', lang);
      }
    } catch (error) {
      console.error('Error saving language to localStorage:', error);
    }
  };

  // Translation function with fallback to English for missing keys
  const t = (key: string): string => {
    // Try to get the translation for the current language
    const translation = translations[language]?.[key];
    
    // If translation exists, return it
    if (translation) return translation;
    
    // If not found in current language, try English as fallback
    if (language !== 'en') {
      const enTranslation = translations.en?.[key];
      if (enTranslation) return enTranslation;
    }
    
    // If not found in either language, return the key in development for easier debugging
    if (process.env.NODE_ENV === 'development') {
      console.warn(`Missing translation for key: ${key}`);
    }
    
    return key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

export default LanguageContext;
