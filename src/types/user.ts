import { Timestamp } from 'firebase/firestore';

export type UserRole = 'pet_owner' | 'provider';
export type MembershipStatus = 'free' | 'pro';
export type BookingStatus = 'pending' | 'confirmed' | 'completed' | 'cancelled';
export type TransactionType = 'credit' | 'debit' | 'deposit' | 'payment' | 'refund' | 'reward_redemption';
export type PaymentMethod = 'credit_card' | 'apple_pay' | 'google_pay' | 'fetchly_balance';

// Enhanced User Profile
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  phone?: string;
  address?: string;
  location?: string;
  verified?: boolean;
  joinedDate: Timestamp;
  lastLoginDate?: Timestamp;
  
  // Fetchly-specific fields
  fetchlyBalance: number;
  membershipStatus: MembershipStatus;
  membershipStartDate?: Timestamp;
  membershipRenewalDate?: Timestamp;
  rewardPoints: number;
  
  // Preferences
  notificationPreferences: NotificationPreferences;
  savedProviders: string[]; // Array of provider IDs
  
  // Emergency contact
  emergencyContact?: EmergencyContact;
}

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  marketing: boolean;
  bookingReminders: boolean;
  promotions: boolean;
}

export interface EmergencyContact {
  name: string;
  phone: string;
  relationship: string;
}

// Pet Profile
export interface Pet {
  id: string;
  userId: string;
  name: string;
  type: string; // dog, cat, bird, etc.
  breed: string;
  dateOfBirth: Timestamp;
  weight: number; // in pounds
  color: string;
  gender: 'male' | 'female';
  microchipId?: string;
  photo?: string;
  
  // Medical information
  vaccinations: Vaccination[];
  allergies: string[];
  medications: Medication[];
  medicalNotes: string;
  
  // Veterinarian information
  vetInfo: VetInfo;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Vaccination {
  name: string;
  date: Timestamp;
  expirationDate?: Timestamp;
  veterinarian: string;
  notes?: string;
}

export interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  startDate: Timestamp;
  endDate?: Timestamp;
  prescribedBy: string;
  notes?: string;
}

export interface VetInfo {
  name: string;
  phone: string;
  address: string;
  email?: string;
}

// Booking & Appointments
export interface Booking {
  id: string;
  userId: string;
  petId: string;
  providerId: string;
  serviceId: string;
  
  // Booking details
  serviceName: string;
  providerName: string;
  petName: string;
  
  // Scheduling
  scheduledDate: Timestamp;
  scheduledTime: string;
  duration: number; // in minutes
  
  // Status and pricing
  status: BookingStatus;
  totalPrice: number;
  paidAmount: number;
  paymentMethod: PaymentMethod;
  
  // Additional information
  notes?: string;
  specialRequests?: string;
  
  // Ratings and reviews
  rating?: number;
  review?: string;
  reviewDate?: Timestamp;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  completedAt?: Timestamp;
  cancelledAt?: Timestamp;
  cancellationReason?: string;
}

// Transaction History
export interface Transaction {
  id: string;
  userId: string;
  type: TransactionType;
  amount: number;
  description: string;
  
  // Related entities
  bookingId?: string;
  rewardRedemptionId?: string;
  
  // Payment information
  paymentMethod?: PaymentMethod;
  stripePaymentIntentId?: string;
  
  // Balance tracking
  balanceBefore: number;
  balanceAfter: number;
  
  // Timestamps
  createdAt: Timestamp;
  processedAt?: Timestamp;
  
  // Status
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  failureReason?: string;
}

// Rewards System
export interface RewardTransaction {
  id: string;
  userId: string;
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  
  // Related entities
  bookingId?: string;
  referralId?: string;
  
  // Redemption details
  rewardItemId?: string;
  rewardItemName?: string;
  
  // Timestamps
  createdAt: Timestamp;
  expiresAt?: Timestamp;
}

export interface RewardItem {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  category: 'service_discount' | 'free_service' | 'merchandise' | 'cashback';
  discountAmount?: number; // for discounts
  serviceId?: string; // for service-related rewards
  isActive: boolean;
  expirationDays?: number;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Membership Features
export interface MembershipFeatures {
  maxPets: number;
  priorityBooking: boolean;
  discountPercentage: number;
  freeMonthlyGrooming: boolean;
  emergencySupport: boolean;
  rewardPointsMultiplier: number;
  freeDelivery: boolean;
  exclusiveProviders: boolean;
}

export const MEMBERSHIP_FEATURES: Record<MembershipStatus, MembershipFeatures> = {
  free: {
    maxPets: 2,
    priorityBooking: false,
    discountPercentage: 0,
    freeMonthlyGrooming: false,
    emergencySupport: false,
    rewardPointsMultiplier: 1,
    freeDelivery: false,
    exclusiveProviders: false,
  },
  pro: {
    maxPets: 10,
    priorityBooking: true,
    discountPercentage: 15,
    freeMonthlyGrooming: true,
    emergencySupport: true,
    rewardPointsMultiplier: 2,
    freeDelivery: true,
    exclusiveProviders: true,
  },
};

// Service Provider (for reference)
export interface ServiceProvider {
  id: string;
  name: string;
  type: string;
  rating: number;
  reviewCount: number;
  location: string;
  services: Service[];
  verified: boolean;
  featured: boolean;
  responseTime: string;
  completionRate: number;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  duration: number;
  basePrice: number;
  category: string;
}
