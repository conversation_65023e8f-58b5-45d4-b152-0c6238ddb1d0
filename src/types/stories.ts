export interface Story {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  mediaUrl: string;
  type: 'image' | 'video';
  content?: string; // Optional text content
  createdAt: Date;
  expiresAt: Date;
  isPublic: boolean;
  views?: number;
  viewedBy?: string[]; // Array of user IDs who viewed this story
}

export interface UserStories {
  userId: string;
  userName: string;
  userAvatar: string;
  stories: Story[];
  hasUnviewed: boolean;
  latestStory: Story;
}

export interface StoryViewerProps {
  userStories: UserStories[];
  initialUserIndex: number;
  initialStoryIndex: number;
  onClose: () => void;
  onStoryView?: (storyId: string, userId: string) => void;
}

export interface StoryProgressProps {
  segments: number;
  currentSegment: number;
  progress: number;
  duration: number;
}

export interface StoryControlsProps {
  isPlaying: boolean;
  onPlayPause: () => void;
  onNext: () => void;
  onPrevious: () => void;
  onClose: () => void;
}

export interface StoryMediaProps {
  story: Story;
  isActive: boolean;
  onLoadComplete?: () => void;
  onError?: (error: Error) => void;
}

// Story configuration
export const STORY_CONFIG = {
  DEFAULT_DURATION: 5000, // 5 seconds
  VIDEO_MAX_DURATION: 15000, // 15 seconds max for videos
  PROGRESS_UPDATE_INTERVAL: 50, // Update progress every 50ms
  HOLD_THRESHOLD: 200, // Minimum hold time to pause (ms)
  EXPIRY_HOURS: 24, // Stories expire after 24 hours
  MAX_STORIES_PER_USER: 10, // Maximum stories per user
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  SUPPORTED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB max file size
} as const;

// Story utilities
export const isStoryExpired = (createdAt: Date): boolean => {
  const now = new Date();
  const expiryTime = new Date(createdAt.getTime() + (STORY_CONFIG.EXPIRY_HOURS * 60 * 60 * 1000));
  return now > expiryTime;
};

export const getStoryDuration = (story: Story): number => {
  if (story.type === 'video') {
    // For videos, we'll use the actual video duration or max duration
    return Math.min(STORY_CONFIG.VIDEO_MAX_DURATION, STORY_CONFIG.DEFAULT_DURATION);
  }
  return STORY_CONFIG.DEFAULT_DURATION;
};

export const formatStoryTime = (date: Date): string => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffMs / (1000 * 60));

  if (diffHours >= 1) {
    return `${diffHours}h`;
  } else if (diffMinutes >= 1) {
    return `${diffMinutes}m`;
  } else {
    return 'now';
  }
};

export const groupStoriesByUser = (stories: Story[]): UserStories[] => {
  const userStoriesMap = new Map<string, UserStories>();

  stories.forEach(story => {
    if (!userStoriesMap.has(story.userId)) {
      userStoriesMap.set(story.userId, {
        userId: story.userId,
        userName: story.userName,
        userAvatar: story.userAvatar,
        stories: [],
        hasUnviewed: false,
        latestStory: story
      });
    }

    const userStories = userStoriesMap.get(story.userId)!;
    userStories.stories.push(story);
    
    // Update latest story if this one is newer
    if (story.createdAt > userStories.latestStory.createdAt) {
      userStories.latestStory = story;
    }
  });

  // Sort stories within each user by creation date
  userStoriesMap.forEach(userStories => {
    userStories.stories.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  });

  // Convert to array and sort by latest story time
  return Array.from(userStoriesMap.values()).sort((a, b) => 
    b.latestStory.createdAt.getTime() - a.latestStory.createdAt.getTime()
  );
};
