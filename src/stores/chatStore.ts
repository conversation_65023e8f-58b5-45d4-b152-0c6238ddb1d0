import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { 
  Chat, 
  Message, 
  ChatState as ChatStateType, 
  CreateChatRequest, 
  SendMessageRequest,
  TypingIndicator,
  ChatNotification
} from '@/types/chat';
import { chatService } from '@/lib/services/chat-service';
import { useAuth } from '@/contexts/AuthContext';

interface ChatStore extends Omit<ChatStateType, 'typingUsers' | 'isTyping'> {
  // Actions
  loadUserChats: (userId: string) => void;
  loadChatMessages: (chatId: string) => void;
  setActiveChat: (chat: Chat | null) => void;
  sendMessage: (request: SendMessageRequest, senderId: string) => Promise<void>;
  createChat: (request: CreateChatRequest) => Promise<string>;
  getChatById: (chatId: string) => Promise<Chat | undefined>;
  markChatAsRead: (chatId: string, userId: string) => Promise<void>;
  setTyping: (chatId: string, userId: string, isTyping: boolean) => Promise<void>;
  deleteMessage: (messageId: string, userId: string) => Promise<void>;
  createSupportChat: (userId: string, subject: string, message: string) => Promise<string>;

  // Cleanup
  cleanup: () => void;

  // Internal state
  unsubscribers: (() => void)[];
  typingUsers: Record<string, string[]>; // chatId -> userIds[]
  isTyping: Record<string, string[]>; // chatId -> userIds[]
}

interface ChatState {
  chats: Chat[];
  activeChat: Chat | null;
  messages: Record<string, Message[]>;
  unreadCounts: Record<string, number>;
  typingUsers: Record<string, string[]>;
  isLoading: boolean;
  error: string | null;
  isTyping: Record<string, boolean>;
  notifications: any[];
  onlineUsers: any[];
  unsubscribers: (() => void)[];
}

export const useChatStore = create<ChatStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    chats: [],
    activeChat: null,
    messages: {},
    unreadCounts: {},
    typingUsers: {},
    isLoading: false,
    error: null,
    isTyping: {},
    notifications: [],
    onlineUsers: [],
    unsubscribers: [],

    // Actions
    loadUserChats: (userId: string) => {
      set({ isLoading: true });
      
      // Unsubscribe from previous chat listener
      const { unsubscribers } = get();
      unsubscribers.forEach(unsub => unsub());
      
      // Subscribe to user's chats
      const unsubscribe = chatService.getUserChats(userId, (chats) => {
        set({ 
          chats, 
          isLoading: false,
          unreadCounts: chats.reduce((acc, chat) => ({
            ...acc,
            [chat.id]: chat.unreadCount[userId] || 0
          }), {})
        });
      });
      
      set({ unsubscribers: [unsubscribe] });
    },

    loadChatMessages: (chatId: string) => {
      const { unsubscribers } = get();
      
      // Check if already subscribed to this chat
      const existingMessages = get().messages[chatId];
      if (existingMessages) return;
      
      // Subscribe to chat messages
      const unsubscribe = chatService.getChatMessages(chatId, (messages) => {
        set(state => ({
          messages: {
            ...state.messages,
            [chatId]: messages
          }
        }));
      });
      
      set({ unsubscribers: [...unsubscribers, unsubscribe] });
    },

    setActiveChat: (chat: Chat | null) => {
      set({ activeChat: chat });
      
      // Load messages for the active chat
      if (chat) {
        get().loadChatMessages(chat.id);
      }
    },

    sendMessage: async (request: SendMessageRequest, senderId: string) => {
      try {
        await chatService.sendMessage(request, senderId);
        // Message will be updated via real-time listener
      } catch (error) {
        console.error('Error sending message:', error);
        throw error;
      }
    },

    createChat: async (request: CreateChatRequest) => {
      try {
        const chatId = await chatService.createOrGetChat(request);
        return chatId;
      } catch (error) {
        console.error('Error creating chat:', error);
        throw error;
      }
    },

    getChatById: async (chatId: string): Promise<Chat | undefined> => {
      try {
        const chat = await chatService.getChatById(chatId);
        return chat || undefined;
      } catch (error) {
        console.error('Error getting chat by ID:', error);
        return undefined;
      }
    },

    markChatAsRead: async (chatId: string, userId: string) => {
      try {
        await chatService.markChatAsRead(chatId, userId);
        
        // Update local unread count
        set(state => ({
          unreadCounts: {
            ...state.unreadCounts,
            [chatId]: 0
          }
        }));
      } catch (error) {
        console.error('Error marking chat as read:', error);
      }
    },

    setTyping: async (chatId: string, userId: string, isUserTyping: boolean) => {
      try {
        await chatService.setTyping(chatId, userId, isUserTyping);

        // Update local typing state
        set((state) => {
          const currentTyping = state.typingUsers[chatId] || [];
          const updatedTyping = isUserTyping
            ? [...currentTyping, userId].filter((id, index, arr) => arr.indexOf(id) === index)
            : currentTyping.filter((id: string) => id !== userId);

          return {
            ...state,
            typingUsers: {
              ...state.typingUsers,
              [chatId]: updatedTyping,
            },
            isTyping: {
              ...state.isTyping,
              [chatId]: updatedTyping,
            },
          };
        });
      } catch (error) {
        console.error('Error setting typing indicator:', error);
      }
    },

    deleteMessage: async (messageId: string, userId: string) => {
      try {
        await chatService.deleteMessage(messageId, userId);
        // Message will be updated via real-time listener
      } catch (error) {
        console.error('Error deleting message:', error);
        throw error;
      }
    },

    createSupportChat: async (userId: string, subject: string, message: string) => {
      try {
        const chatId = await chatService.createSupportChat(userId, subject, message);
        return chatId;
      } catch (error) {
        console.error('Error creating support chat:', error);
        throw error;
      }
    },

    cleanup: () => {
      const { unsubscribers } = get();
      unsubscribers.forEach(unsub => unsub());
      set({ 
        unsubscribers: [],
        chats: [],
        activeChat: null,
        messages: {},
        isLoading: false,
        isTyping: {},
        unreadCounts: {},
        notifications: [],
        onlineUsers: []
      });
    }
  }))
);

// Helper hooks
export const useActiveChat = () => useChatStore(state => state.activeChat);
export const useChats = () => useChatStore(state => state.chats);

// Create stable empty arrays to prevent infinite re-renders
const EMPTY_MESSAGES: any[] = [];
const EMPTY_TYPING: string[] = [];

export const useChatMessages = (chatId: string) => useChatStore(state => {
  const messages = state.messages[chatId];
  return messages || EMPTY_MESSAGES;
});

export const useUnreadCount = (chatId: string) => useChatStore(state => state.unreadCounts[chatId] || 0);

export const useIsTyping = (chatId: string) => useChatStore(state => {
  const typing = state.isTyping[chatId];
  return typing || EMPTY_TYPING;
});

export const useTotalUnreadCount = () => useChatStore(state =>
  Object.values(state.unreadCounts).reduce<number>((total: number, count: number) => total + count, 0)
);


