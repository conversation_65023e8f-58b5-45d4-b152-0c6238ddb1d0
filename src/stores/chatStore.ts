import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Chat, Message, ChatState, CreateChatRequest, SendMessageRequest } from '@/types/chat';
import { chatService } from '@/lib/services/chat-service';
import { useAuth } from '@/contexts/AuthContext';

interface ChatStore extends ChatState {
  // Actions
  loadUserChats: (userId: string) => void;
  loadChatMessages: (chatId: string) => void;
  setActiveChat: (chat: Chat | null) => void;
  sendMessage: (request: SendMessageRequest, senderId: string) => Promise<void>;
  createChat: (request: CreateChatRequest) => Promise<string>;
  markChatAsRead: (chatId: string, userId: string) => Promise<void>;
  setTyping: (chatId: string, userId: string, isTyping: boolean) => Promise<void>;
  deleteMessage: (messageId: string, userId: string) => Promise<void>;
  createSupportChat: (userId: string, subject: string, message: string) => Promise<string>;
  
  // Cleanup
  cleanup: () => void;
  
  // Internal state
  unsubscribers: (() => void)[];
}

export const useChatStore = create<ChatStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    chats: [],
    activeChat: null,
    messages: {},
    isLoading: false,
    isTyping: {},
    unreadCounts: {},
    notifications: [],
    onlineUsers: [],
    unsubscribers: [],

    // Actions
    loadUserChats: (userId: string) => {
      set({ isLoading: true });
      
      // Unsubscribe from previous chat listener
      const { unsubscribers } = get();
      unsubscribers.forEach(unsub => unsub());
      
      // Subscribe to user's chats
      const unsubscribe = chatService.getUserChats(userId, (chats) => {
        set({ 
          chats, 
          isLoading: false,
          unreadCounts: chats.reduce((acc, chat) => ({
            ...acc,
            [chat.id]: chat.unreadCount[userId] || 0
          }), {})
        });
      });
      
      set({ unsubscribers: [unsubscribe] });
    },

    loadChatMessages: (chatId: string) => {
      const { unsubscribers } = get();
      
      // Check if already subscribed to this chat
      const existingMessages = get().messages[chatId];
      if (existingMessages) return;
      
      // Subscribe to chat messages
      const unsubscribe = chatService.getChatMessages(chatId, (messages) => {
        set(state => ({
          messages: {
            ...state.messages,
            [chatId]: messages
          }
        }));
      });
      
      set({ unsubscribers: [...unsubscribers, unsubscribe] });
    },

    setActiveChat: (chat: Chat | null) => {
      set({ activeChat: chat });
      
      // Load messages for the active chat
      if (chat) {
        get().loadChatMessages(chat.id);
      }
    },

    sendMessage: async (request: SendMessageRequest, senderId: string) => {
      try {
        await chatService.sendMessage(request, senderId);
        // Message will be updated via real-time listener
      } catch (error) {
        console.error('Error sending message:', error);
        throw error;
      }
    },

    createChat: async (request: CreateChatRequest) => {
      try {
        const chatId = await chatService.createOrGetChat(request);
        return chatId;
      } catch (error) {
        console.error('Error creating chat:', error);
        throw error;
      }
    },

    getChatById: async (chatId: string) => {
      try {
        const chat = await chatService.getChatById(chatId);
        return chat;
      } catch (error) {
        console.error('Error getting chat by ID:', error);
        return null;
      }
    },

    markChatAsRead: async (chatId: string, userId: string) => {
      try {
        await chatService.markChatAsRead(chatId, userId);
        
        // Update local unread count
        set(state => ({
          unreadCounts: {
            ...state.unreadCounts,
            [chatId]: 0
          }
        }));
      } catch (error) {
        console.error('Error marking chat as read:', error);
      }
    },

    setTyping: async (chatId: string, userId: string, isTyping: boolean) => {
      try {
        await chatService.setTyping(chatId, userId, isTyping);
        
        // Update local typing state
        set(state => ({
          isTyping: {
            ...state.isTyping,
            [chatId]: isTyping 
              ? [...(state.isTyping[chatId] || []), userId].filter((id, index, arr) => arr.indexOf(id) === index)
              : (state.isTyping[chatId] || []).filter(id => id !== userId)
          }
        }));
      } catch (error) {
        console.error('Error setting typing indicator:', error);
      }
    },

    deleteMessage: async (messageId: string, userId: string) => {
      try {
        await chatService.deleteMessage(messageId, userId);
        // Message will be updated via real-time listener
      } catch (error) {
        console.error('Error deleting message:', error);
        throw error;
      }
    },

    createSupportChat: async (userId: string, subject: string, message: string) => {
      try {
        const chatId = await chatService.createSupportChat(userId, subject, message);
        return chatId;
      } catch (error) {
        console.error('Error creating support chat:', error);
        throw error;
      }
    },

    cleanup: () => {
      const { unsubscribers } = get();
      unsubscribers.forEach(unsub => unsub());
      set({ 
        unsubscribers: [],
        chats: [],
        activeChat: null,
        messages: {},
        isLoading: false,
        isTyping: {},
        unreadCounts: {},
        notifications: [],
        onlineUsers: []
      });
    }
  }))
);

// Helper hooks
export const useActiveChat = () => useChatStore(state => state.activeChat);
export const useChats = () => useChatStore(state => state.chats);
export const useChatMessages = (chatId: string) => useChatStore(state => state.messages[chatId] || []);
export const useUnreadCount = (chatId: string) => useChatStore(state => state.unreadCounts[chatId] || 0);
export const useIsTyping = (chatId: string) => useChatStore(state => state.isTyping[chatId] || []);
export const useTotalUnreadCount = () => useChatStore(state => 
  Object.values(state.unreadCounts).reduce((total, count) => total + count, 0)
);

// Auto-cleanup when user logs out
if (typeof window !== 'undefined') {
  // Listen for auth state changes and cleanup when user logs out
  let previousUserId: string | null = null;
  
  const checkAuthState = () => {
    // This would need to be connected to your auth context
    // For now, we'll handle cleanup manually in components
  };
  
  setInterval(checkAuthState, 1000);
}
