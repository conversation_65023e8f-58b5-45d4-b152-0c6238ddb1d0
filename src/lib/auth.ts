import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from './firebase/config';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: 'pet_owner' | 'provider' | 'admin';
  avatar?: string;
  phone?: string;
  location?: string;
  verified: boolean;
  joinedDate: string;
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
  return await bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}

// Generate JWT token
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined');
  }
  
  return jwt.sign(payload, secret, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  });
}

// Generate refresh token
export function generateRefreshToken(userId: string): string {
  const secret = process.env.REFRESH_TOKEN_SECRET;
  if (!secret) {
    throw new Error('REFRESH_TOKEN_SECRET is not defined');
  }
  
  return jwt.sign({ userId }, secret, {
    expiresIn: '30d',
  });
}

// Verify JWT token
export function verifyToken(token: string): JWTPayload {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined');
  }
  
  try {
    return jwt.verify(token, secret) as JWTPayload;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}

// Verify refresh token
export function verifyRefreshToken(token: string): { userId: string } {
  const secret = process.env.REFRESH_TOKEN_SECRET;
  if (!secret) {
    throw new Error('REFRESH_TOKEN_SECRET is not defined');
  }
  
  try {
    return jwt.verify(token, secret) as { userId: string };
  } catch (error) {
    throw new Error('Invalid or expired refresh token');
  }
}

// Create user account
export async function createUser(userData: {
  email: string;
  password: string;
  name: string;
  role?: 'pet_owner' | 'provider' | 'admin';
  phone?: string;
  location?: string;
}): Promise<AuthUser> {
  const { email, password, name, role = 'pet_owner', phone, location } = userData;
  
  // Check if user already exists
  const existingUser = await query(
    'SELECT id FROM users WHERE email = $1',
    [email.toLowerCase()]
  );
  
  if (existingUser.rows.length > 0) {
    throw new Error('User with this email already exists');
  }
  
  // Hash password
  const passwordHash = await hashPassword(password);
  
  // Generate email verification token
  const emailVerificationToken = uuidv4();
  
  // Insert user
  const result = await query(`
    INSERT INTO users (
      email, password_hash, name, role, phone, location, 
      email_verification_token, created_at
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
    RETURNING id, email, name, role, phone, location, verified, created_at
  `, [email.toLowerCase(), passwordHash, name, role, phone, location, emailVerificationToken]);
  
  const user = result.rows[0];
  
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    phone: user.phone,
    location: user.location,
    verified: user.verified,
    joinedDate: user.created_at.toISOString(),
  };
}

// Authenticate user
export async function authenticateUser(email: string, password: string): Promise<{
  user: AuthUser;
  token: string;
  refreshToken: string;
}> {
  // Get user by email
  const result = await query(`
    SELECT id, email, password_hash, name, role, avatar, phone, location, 
           verified, failed_login_attempts, locked_until, created_at
    FROM users 
    WHERE email = $1
  `, [email.toLowerCase()]);
  
  if (result.rows.length === 0) {
    throw new Error('Invalid email or password');
  }
  
  const user = result.rows[0];
  
  // Check if account is locked
  if (user.locked_until && new Date() < user.locked_until) {
    throw new Error('Account is temporarily locked due to too many failed login attempts');
  }
  
  // Verify password
  const isValidPassword = await verifyPassword(password, user.password_hash);
  
  if (!isValidPassword) {
    // Increment failed login attempts
    const failedAttempts = (user.failed_login_attempts || 0) + 1;
    const lockUntil = failedAttempts >= 5 ? new Date(Date.now() + 15 * 60 * 1000) : null; // Lock for 15 minutes after 5 failed attempts
    
    await query(`
      UPDATE users 
      SET failed_login_attempts = $1, locked_until = $2
      WHERE id = $3
    `, [failedAttempts, lockUntil, user.id]);
    
    throw new Error('Invalid email or password');
  }
  
  // Reset failed login attempts and update last login
  await query(`
    UPDATE users 
    SET failed_login_attempts = 0, locked_until = NULL, last_login_at = CURRENT_TIMESTAMP
    WHERE id = $1
  `, [user.id]);
  
  // Generate tokens
  const token = generateToken({
    userId: user.id,
    email: user.email,
    role: user.role,
  });
  
  const refreshToken = generateRefreshToken(user.id);
  
  const authUser: AuthUser = {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    avatar: user.avatar,
    phone: user.phone,
    location: user.location,
    verified: user.verified,
    joinedDate: user.created_at.toISOString(),
  };
  
  return {
    user: authUser,
    token,
    refreshToken,
  };
}

// Get user by ID
export async function getUserById(userId: string): Promise<AuthUser | null> {
  const result = await query(`
    SELECT id, email, name, role, avatar, phone, location, verified, created_at
    FROM users 
    WHERE id = $1
  `, [userId]);
  
  if (result.rows.length === 0) {
    return null;
  }
  
  const user = result.rows[0];
  
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    avatar: user.avatar,
    phone: user.phone,
    location: user.location,
    verified: user.verified,
    joinedDate: user.created_at.toISOString(),
  };
}

// Generate password reset token
export async function generatePasswordResetToken(email: string): Promise<string> {
  const token = uuidv4();
  const expires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
  
  await query(`
    UPDATE users 
    SET password_reset_token = $1, password_reset_expires = $2
    WHERE email = $3
  `, [token, expires, email.toLowerCase()]);
  
  return token;
}

// Reset password
export async function resetPassword(token: string, newPassword: string): Promise<boolean> {
  const result = await query(`
    SELECT id FROM users 
    WHERE password_reset_token = $1 AND password_reset_expires > CURRENT_TIMESTAMP
  `, [token]);
  
  if (result.rows.length === 0) {
    throw new Error('Invalid or expired reset token');
  }
  
  const userId = result.rows[0].id;
  const passwordHash = await hashPassword(newPassword);
  
  await query(`
    UPDATE users 
    SET password_hash = $1, password_reset_token = NULL, password_reset_expires = NULL
    WHERE id = $2
  `, [passwordHash, userId]);
  
  return true;
}
