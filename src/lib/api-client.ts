'use client';

import { auth } from '@/lib/firebase/config';

/**
 * API Client with automatic authentication
 * Handles JWT token attachment and error handling
 */
class ApiClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_APP_URL || '';
  }

  /**
   * Get the current user's ID token
   */
  private async getAuthToken(): Promise<string | null> {
    try {
      const user = auth.currentUser;
      if (!user) {
        console.warn('⚠️ No authenticated user found');
        return null;
      }

      const token = await user.getIdToken(true); // Force refresh
      console.log('✅ Got fresh ID token for API request');
      return token;
    } catch (error) {
      console.error('❌ Error getting auth token:', error);
      return null;
    }
  }

  /**
   * Make authenticated API request
   */
  private async makeRequest(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const token = await this.getAuthToken();
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
    
    console.log('🔔 Making API request:', {
      method: options.method || 'GET',
      url,
      hasToken: !!token
    });

    const response = await fetch(url, {
      ...options,
      headers,
    });

    // Handle authentication errors
    if (response.status === 401) {
      console.error('❌ Authentication failed - redirecting to login');
      // Optionally redirect to login or refresh token
      throw new Error('Authentication required');
    }

    return response;
  }

  /**
   * GET request
   */
  async get(endpoint: string): Promise<any> {
    try {
      const response = await this.makeRequest(endpoint, {
        method: 'GET',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('❌ GET request failed:', error);
      throw error;
    }
  }

  /**
   * POST request
   */
  async post(endpoint: string, data?: any): Promise<any> {
    try {
      const response = await this.makeRequest(endpoint, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('❌ POST request failed:', error);
      throw error;
    }
  }

  /**
   * PUT request
   */
  async put(endpoint: string, data?: any): Promise<any> {
    try {
      const response = await this.makeRequest(endpoint, {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('❌ PUT request failed:', error);
      throw error;
    }
  }

  /**
   * DELETE request
   */
  async delete(endpoint: string): Promise<any> {
    try {
      const response = await this.makeRequest(endpoint, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('❌ DELETE request failed:', error);
      throw error;
    }
  }

  /**
   * Upload file with authentication
   */
  async uploadFile(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<any> {
    try {
      const token = await this.getAuthToken();
      
      const formData = new FormData();
      formData.append('file', file);
      
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, typeof value === 'string' ? value : JSON.stringify(value));
        });
      }

      const headers: HeadersInit = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('❌ File upload failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export class for testing
export { ApiClient };
