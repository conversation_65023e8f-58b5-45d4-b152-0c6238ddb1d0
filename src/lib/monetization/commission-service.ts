import { stripe } from '@/lib/stripe/config';
import { doc, updateDoc, addDoc, collection, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

export interface BookingPricing {
  servicePrice: number;
  addOns: Array<{
    id: string;
    name: string;
    price: number;
  }>;
  tip?: number;
  expressBookingFee?: number;
  cancellationFee?: number;
  subtotal: number;
  platformFee: number;
  total: number;
  providerEarnings: number;
}

export interface CommissionBreakdown {
  serviceAmount: number;
  addOnAmount: number;
  tipAmount: number;
  expressBookingFee: number;
  cancellationFee: number;
  subtotal: number;
  platformCommission: number; // 10% of service + add-ons
  tipCommission: number; // 5% of tips
  totalPlatformFee: number;
  providerEarnings: number;
  total: number;
}

export class CommissionService {
  // Platform commission rates
  static readonly PLATFORM_COMMISSION_RATE = 0.10; // 10%
  static readonly TIP_COMMISSION_RATE = 0.05; // 5%
  static readonly EXPRESS_BOOKING_FEE = 5.00; // $5 for bookings <3 hours
  static readonly CANCELLATION_FEE_RATE = 0.10; // 10% for cancellations <12 hours

  /**
   * Calculate comprehensive booking pricing with all fees
   */
  static calculateBookingPricing(
    servicePrice: number,
    addOns: Array<{ id: string; name: string; price: number }> = [],
    tip: number = 0,
    isExpressBooking: boolean = false,
    cancellationFee: number = 0
  ): CommissionBreakdown {
    // Calculate base amounts
    const serviceAmount = servicePrice;
    const addOnAmount = addOns.reduce((sum, addOn) => sum + addOn.price, 0);
    const tipAmount = tip;
    const expressBookingFee = isExpressBooking ? this.EXPRESS_BOOKING_FEE : 0;

    // Calculate subtotal (service + add-ons + express fee + cancellation fee)
    const subtotal = serviceAmount + addOnAmount + expressBookingFee + cancellationFee;

    // Calculate platform commissions
    const platformCommission = (serviceAmount + addOnAmount) * this.PLATFORM_COMMISSION_RATE;
    const tipCommission = tipAmount * this.TIP_COMMISSION_RATE;
    const totalPlatformFee = platformCommission + tipCommission;

    // Calculate provider earnings
    const providerEarnings = subtotal + tipAmount - totalPlatformFee;

    // Calculate total customer pays
    const total = subtotal + tipAmount;

    return {
      serviceAmount,
      addOnAmount,
      tipAmount,
      expressBookingFee,
      cancellationFee,
      subtotal,
      platformCommission,
      tipCommission,
      totalPlatformFee,
      providerEarnings,
      total,
    };
  }

  /**
   * Calculate cancellation fee based on timing
   */
  static calculateCancellationFee(
    bookingAmount: number,
    hoursUntilBooking: number
  ): number {
    if (hoursUntilBooking < 12) {
      return bookingAmount * this.CANCELLATION_FEE_RATE;
    }
    return 0;
  }

  /**
   * Check if booking qualifies for express fee
   */
  static isExpressBooking(hoursUntilBooking: number): boolean {
    return hoursUntilBooking < 3;
  }

  /**
   * Create payment intent with commission splitting
   */
  static async createBookingPaymentIntent(
    providerId: string,
    customerId: string,
    pricing: CommissionBreakdown,
    bookingId: string,
    description: string
  ) {
    try {
      // Get provider's Stripe account ID
      const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, providerId));
      if (!providerDoc.exists()) {
        throw new Error('Provider not found');
      }

      const provider = providerDoc.data();
      if (!provider.stripeAccountId) {
        throw new Error('Provider has not completed Stripe onboarding');
      }

      // Convert to cents
      const totalInCents = Math.round(pricing.total * 100);
      const platformFeeInCents = Math.round(pricing.totalPlatformFee * 100);

      const paymentIntent = await stripe.paymentIntents.create({
        amount: totalInCents,
        currency: 'usd',
        customer: customerId,
        payment_method_types: ['card'],
        application_fee_amount: platformFeeInCents,
        transfer_data: {
          destination: provider.stripeAccountId,
        },
        metadata: {
          bookingId,
          providerId,
          type: 'booking_payment',
          serviceAmount: pricing.serviceAmount.toString(),
          addOnAmount: pricing.addOnAmount.toString(),
          tipAmount: pricing.tipAmount.toString(),
          platformFee: pricing.totalPlatformFee.toString(),
        },
        description,
      });

      // Log transaction in Firestore
      await addDoc(collection(db, COLLECTIONS.TRANSACTIONS), {
        type: 'booking_payment',
        status: 'pending',
        bookingId,
        providerId,
        customerId,
        amount: pricing.total,
        serviceAmount: pricing.serviceAmount,
        addOnAmount: pricing.addOnAmount,
        tipAmount: pricing.tipAmount,
        expressBookingFee: pricing.expressBookingFee,
        cancellationFee: pricing.cancellationFee,
        platformFee: pricing.totalPlatformFee,
        providerEarnings: pricing.providerEarnings,
        stripePaymentIntentId: paymentIntent.id,
        description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret,
      };
    } catch (error: any) {
      console.error('Error creating booking payment intent:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Process tip payment after booking completion
   */
  static async processTipPayment(
    providerId: string,
    customerId: string,
    bookingId: string,
    tipAmount: number,
    description: string
  ) {
    try {
      // Get provider's Stripe account ID
      const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, providerId));
      if (!providerDoc.exists()) {
        throw new Error('Provider not found');
      }

      const provider = providerDoc.data();
      if (!provider.stripeAccountId) {
        throw new Error('Provider has not completed Stripe onboarding');
      }

      const tipCommission = tipAmount * this.TIP_COMMISSION_RATE;
      const providerTipEarnings = tipAmount - tipCommission;

      // Convert to cents
      const tipInCents = Math.round(tipAmount * 100);
      const tipCommissionInCents = Math.round(tipCommission * 100);

      const paymentIntent = await stripe.paymentIntents.create({
        amount: tipInCents,
        currency: 'usd',
        customer: customerId,
        payment_method_types: ['card'],
        application_fee_amount: tipCommissionInCents,
        transfer_data: {
          destination: provider.stripeAccountId,
        },
        metadata: {
          bookingId,
          providerId,
          type: 'tip_payment',
          tipAmount: tipAmount.toString(),
          tipCommission: tipCommission.toString(),
        },
        description,
      });

      // Log tip transaction
      await addDoc(collection(db, COLLECTIONS.TRANSACTIONS), {
        type: 'tip_payment',
        status: 'pending',
        bookingId,
        providerId,
        customerId,
        amount: tipAmount,
        platformFee: tipCommission,
        providerEarnings: providerTipEarnings,
        stripePaymentIntentId: paymentIntent.id,
        description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret,
      };
    } catch (error: any) {
      console.error('Error processing tip payment:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Process cancellation fee
   */
  static async processCancellationFee(
    providerId: string,
    customerId: string,
    bookingId: string,
    originalAmount: number,
    hoursUntilBooking: number,
    description: string
  ) {
    try {
      const cancellationFee = this.calculateCancellationFee(originalAmount, hoursUntilBooking);
      
      if (cancellationFee === 0) {
        return { success: true, fee: 0 };
      }

      // For cancellation fees, platform keeps 100%
      const feeInCents = Math.round(cancellationFee * 100);

      const paymentIntent = await stripe.paymentIntents.create({
        amount: feeInCents,
        currency: 'usd',
        customer: customerId,
        payment_method_types: ['card'],
        metadata: {
          bookingId,
          providerId,
          type: 'cancellation_fee',
          originalAmount: originalAmount.toString(),
          hoursUntilBooking: hoursUntilBooking.toString(),
        },
        description,
      });

      // Log cancellation fee transaction
      await addDoc(collection(db, COLLECTIONS.TRANSACTIONS), {
        type: 'cancellation_fee',
        status: 'pending',
        bookingId,
        providerId,
        customerId,
        amount: cancellationFee,
        platformFee: cancellationFee, // Platform keeps 100% of cancellation fees
        providerEarnings: 0,
        stripePaymentIntentId: paymentIntent.id,
        description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret,
        fee: cancellationFee,
      };
    } catch (error: any) {
      console.error('Error processing cancellation fee:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get pricing breakdown for display
   */
  static getPricingBreakdown(
    servicePrice: number,
    addOns: Array<{ id: string; name: string; price: number }> = [],
    tip: number = 0,
    isExpressBooking: boolean = false,
    cancellationFee: number = 0
  ): {
    items: Array<{ label: string; amount: number; type: 'service' | 'addon' | 'fee' | 'tip' }>;
    subtotal: number;
    platformFee: number;
    total: number;
  } {
    const pricing = this.calculateBookingPricing(
      servicePrice,
      addOns,
      tip,
      isExpressBooking,
      cancellationFee
    );

    const items = [
      { label: 'Service', amount: pricing.serviceAmount, type: 'service' as const },
      ...addOns.map(addOn => ({
        label: addOn.name,
        amount: addOn.price,
        type: 'addon' as const,
      })),
    ];

    if (pricing.expressBookingFee > 0) {
      items.push({
        label: 'Express Booking Fee',
        amount: pricing.expressBookingFee,
        type: 'fee' as const,
      });
    }

    if (pricing.cancellationFee > 0) {
      items.push({
        label: 'Cancellation Fee',
        amount: pricing.cancellationFee,
        type: 'fee' as const,
      });
    }

    if (pricing.tipAmount > 0) {
      items.push({
        label: 'Tip',
        amount: pricing.tipAmount,
        type: 'tip' as const,
      });
    }

    return {
      items,
      subtotal: pricing.subtotal,
      platformFee: pricing.totalPlatformFee,
      total: pricing.total,
    };
  }
}
