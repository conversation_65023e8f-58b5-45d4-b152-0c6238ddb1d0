import { stripe } from '@/lib/stripe/config';
import { doc, updateDoc, addDoc, collection, getDoc, setDoc, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

export interface BoostType {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number; // in hours
  stripePriceId: string;
  features: string[];
}

export const BOOST_TYPES: Record<string, BoostType> = {
  topInSearch: {
    id: 'topInSearch',
    name: 'Top in Search',
    description: 'Appear at the top of search results for 7 days',
    price: 4.99,
    duration: 168, // 7 days in hours
    stripePriceId: process.env.NEXT_PUBLIC_STRIPE_TOP_SEARCH_PRICE_ID || '',
    features: [
      'Top position in search results',
      'Priority placement for 7 days',
      'Increased visibility to pet owners',
      'Analytics tracking included'
    ],
  },
  featuredToday: {
    id: 'featuredToday',
    name: 'Featured Today',
    description: 'Featured placement on homepage for 24 hours',
    price: 1.99,
    duration: 24, // 24 hours
    stripePriceId: process.env.NEXT_PUBLIC_STRIPE_FEATURED_TODAY_PRICE_ID || '',
    features: [
      'Homepage featured section',
      'Premium badge display',
      '24-hour featured placement',
      'Enhanced profile visibility'
    ],
  },
};

export interface ProviderBoost {
  id: string;
  providerId: string;
  boostType: string;
  status: 'active' | 'expired' | 'pending';
  startDate: string;
  endDate: string;
  price: number;
  stripePaymentIntentId?: string;
  impressions: number;
  clicks: number;
  createdAt: string;
  updatedAt: string;
}

export class BoostService {
  /**
   * Create boost purchase payment intent
   */
  static async createBoostPaymentIntent(
    providerId: string,
    boostType: string,
    customerId: string
  ) {
    try {
      const boost = BOOST_TYPES[boostType];
      if (!boost) {
        throw new Error('Invalid boost type');
      }

      // Check if provider already has an active boost of this type
      const existingBoostQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('providerId', '==', providerId),
        where('boostType', '==', boostType),
        where('status', '==', 'active')
      );
      const existingBoosts = await getDocs(existingBoostQuery);

      if (!existingBoosts.empty) {
        throw new Error(`You already have an active ${boost.name} boost`);
      }

      const amountInCents = Math.round(boost.price * 100);

      const paymentIntent = await stripe.paymentIntents.create({
        amount: amountInCents,
        currency: 'usd',
        customer: customerId,
        payment_method_types: ['card'],
        metadata: {
          providerId,
          boostType,
          type: 'boost_purchase',
          duration: boost.duration.toString(),
        },
        description: `${boost.name} - ${boost.description}`,
      });

      // Create pending boost record
      const boostId = `${providerId}_${boostType}_${Date.now()}`;
      const boostRecord: ProviderBoost = {
        id: boostId,
        providerId,
        boostType,
        status: 'pending',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + boost.duration * 60 * 60 * 1000).toISOString(),
        price: boost.price,
        stripePaymentIntentId: paymentIntent.id,
        impressions: 0,
        clicks: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await setDoc(doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId), boostRecord);

      // Log transaction
      await addDoc(collection(db, COLLECTIONS.TRANSACTIONS), {
        type: 'boost_purchase',
        status: 'pending',
        providerId,
        amount: boost.price,
        platformFee: boost.price, // Platform keeps 100% of boost fees
        providerEarnings: 0,
        stripePaymentIntentId: paymentIntent.id,
        description: `${boost.name} boost purchase`,
        metadata: {
          boostType,
          boostId,
          duration: boost.duration,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret,
        boostId,
      };
    } catch (error: any) {
      console.error('Error creating boost payment intent:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Activate boost after successful payment
   */
  static async activateBoost(boostId: string) {
    try {
      const boostRef = doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId);
      await updateDoc(boostRef, {
        status: 'active',
        updatedAt: new Date().toISOString(),
      });

      return { success: true };
    } catch (error: any) {
      console.error('Error activating boost:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get active boosts for a provider
   */
  static async getActiveBoosts(providerId: string): Promise<ProviderBoost[]> {
    try {
      const boostsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('providerId', '==', providerId),
        where('status', '==', 'active')
      );
      const boostsSnapshot = await getDocs(boostsQuery);
      
      const boosts = boostsSnapshot.docs.map(doc => doc.data() as ProviderBoost);
      
      // Check if any boosts have expired
      const now = new Date();
      for (const boost of boosts) {
        if (new Date(boost.endDate) < now) {
          await this.expireBoost(boost.id);
        }
      }

      // Return only still-active boosts
      return boosts.filter(boost => new Date(boost.endDate) >= now);
    } catch (error) {
      console.error('Error getting active boosts:', error);
      return [];
    }
  }

  /**
   * Expire a boost
   */
  static async expireBoost(boostId: string) {
    try {
      const boostRef = doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId);
      await updateDoc(boostRef, {
        status: 'expired',
        updatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error expiring boost:', error);
    }
  }

  /**
   * Track boost impression
   */
  static async trackImpression(boostId: string) {
    try {
      const boostRef = doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId);
      const boostDoc = await getDoc(boostRef);
      
      if (boostDoc.exists()) {
        const boost = boostDoc.data() as ProviderBoost;
        await updateDoc(boostRef, {
          impressions: boost.impressions + 1,
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error tracking impression:', error);
    }
  }

  /**
   * Track boost click
   */
  static async trackClick(boostId: string) {
    try {
      const boostRef = doc(db, COLLECTIONS.PROVIDER_BOOSTS, boostId);
      const boostDoc = await getDoc(boostRef);
      
      if (boostDoc.exists()) {
        const boost = boostDoc.data() as ProviderBoost;
        await updateDoc(boostRef, {
          clicks: boost.clicks + 1,
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error tracking click:', error);
    }
  }

  /**
   * Get boosted providers for search results
   */
  static async getBoostedProvidersForSearch(): Promise<string[]> {
    try {
      const boostsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('boostType', '==', 'topInSearch'),
        where('status', '==', 'active')
      );
      const boostsSnapshot = await getDocs(boostsQuery);
      
      const boostedProviders: string[] = [];
      const now = new Date();

      for (const doc of boostsSnapshot.docs) {
        const boost = doc.data() as ProviderBoost;
        if (new Date(boost.endDate) >= now) {
          boostedProviders.push(boost.providerId);
          // Track impression
          await this.trackImpression(boost.id);
        } else {
          // Expire the boost
          await this.expireBoost(boost.id);
        }
      }

      return boostedProviders;
    } catch (error) {
      console.error('Error getting boosted providers for search:', error);
      return [];
    }
  }

  /**
   * Get featured providers for homepage
   */
  static async getFeaturedProvidersForHomepage(): Promise<string[]> {
    try {
      const boostsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('boostType', '==', 'featuredToday'),
        where('status', '==', 'active')
      );
      const boostsSnapshot = await getDocs(boostsQuery);
      
      const featuredProviders: string[] = [];
      const now = new Date();

      for (const doc of boostsSnapshot.docs) {
        const boost = doc.data() as ProviderBoost;
        if (new Date(boost.endDate) >= now) {
          featuredProviders.push(boost.providerId);
          // Track impression
          await this.trackImpression(boost.id);
        } else {
          // Expire the boost
          await this.expireBoost(boost.id);
        }
      }

      return featuredProviders;
    } catch (error) {
      console.error('Error getting featured providers for homepage:', error);
      return [];
    }
  }

  /**
   * Get boost analytics for provider
   */
  static async getBoostAnalytics(providerId: string) {
    try {
      const boostsQuery = query(
        collection(db, COLLECTIONS.PROVIDER_BOOSTS),
        where('providerId', '==', providerId)
      );
      const boostsSnapshot = await getDocs(boostsQuery);
      
      const boosts = boostsSnapshot.docs.map(doc => doc.data() as ProviderBoost);
      
      const analytics = {
        totalSpent: boosts.reduce((sum, boost) => sum + boost.price, 0),
        totalImpressions: boosts.reduce((sum, boost) => sum + boost.impressions, 0),
        totalClicks: boosts.reduce((sum, boost) => sum + boost.clicks, 0),
        activeBoosts: boosts.filter(boost => boost.status === 'active').length,
        boostHistory: boosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()),
      };

      return { success: true, analytics };
    } catch (error: any) {
      console.error('Error getting boost analytics:', error);
      return { success: false, error: error.message };
    }
  }
}
