import { stripe, STRIPE_CONFIG } from './config';
import { doc, updateDoc, addDoc, collection, getDoc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

export interface SubscriptionTier {
  id: string;
  name: string;
  price: number; // in dollars
  stripePriceId: string;
  features: {
    bookingLimit: number | null; // null = unlimited
    analytics: 'basic' | 'advanced';
    calendarSync: boolean;
    prioritySearch: boolean;
    featuredListing: boolean;
    smsAlerts: boolean;
    customUrl: boolean;
    weeklyBoosts: number;
  };
}

export const SUBSCRIPTION_TIERS: Record<string, SubscriptionTier> = {
  free: {
    id: 'free',
    name: 'Free',
    price: 0,
    stripePriceId: '', // No Stripe price for free tier
    features: {
      bookingLimit: 5,
      analytics: 'basic',
      calendarSync: false,
      prioritySearch: false,
      featuredListing: false,
      smsAlerts: false,
      customUrl: false,
      weeklyBoosts: 0,
    },
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    price: 9.99,
    stripePriceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID || '',
    features: {
      bookingLimit: null, // unlimited
      analytics: 'advanced',
      calendarSync: true,
      prioritySearch: true,
      featuredListing: false,
      smsAlerts: false,
      customUrl: false,
      weeklyBoosts: 0,
    },
  },
  premium: {
    id: 'premium',
    name: 'Premium',
    price: 29.99,
    stripePriceId: process.env.NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID || '',
    features: {
      bookingLimit: null, // unlimited
      analytics: 'advanced',
      calendarSync: true,
      prioritySearch: true,
      featuredListing: true,
      smsAlerts: true,
      customUrl: true,
      weeklyBoosts: 1,
    },
  },
};

export interface ProviderSubscription {
  id: string;
  providerId: string;
  tier: 'free' | 'pro' | 'premium';
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  bookingsThisMonth: number;
  createdAt: string;
  updatedAt: string;
}

export class SubscriptionService {
  /**
   * Create Stripe customer for provider
   */
  static async createStripeCustomer(providerId: string, email: string, name: string) {
    try {
      const customer = await stripe.customers.create({
        email,
        name,
        metadata: {
          providerId,
          platform: 'fetchly',
        },
      });

      // Update provider document with Stripe customer ID
      await updateDoc(doc(db, COLLECTIONS.PROVIDERS, providerId), {
        stripeCustomerId: customer.id,
        updatedAt: new Date().toISOString(),
      });

      return {
        success: true,
        customerId: customer.id,
      };
    } catch (error: any) {
      console.error('Error creating Stripe customer:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create subscription checkout session
   */
  static async createSubscriptionCheckout(
    providerId: string,
    tier: 'pro' | 'premium',
    successUrl: string,
    cancelUrl: string
  ) {
    try {
      // Get provider data
      const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, providerId));
      if (!providerDoc.exists()) {
        throw new Error('Provider not found');
      }

      const provider = providerDoc.data();
      let customerId = provider.stripeCustomerId;

      // Create Stripe customer if doesn't exist
      if (!customerId) {
        const customerResult = await this.createStripeCustomer(
          providerId,
          provider.email,
          provider.businessName || provider.ownerName
        );
        if (!customerResult.success) {
          throw new Error(customerResult.error);
        }
        customerId = customerResult.customerId;
      }

      const tierConfig = SUBSCRIPTION_TIERS[tier];
      if (!tierConfig.stripePriceId) {
        throw new Error(`Stripe price ID not configured for ${tier} tier`);
      }

      // Create checkout session
      const session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: tierConfig.stripePriceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: {
          providerId,
          tier,
          type: 'subscription',
        },
        subscription_data: {
          metadata: {
            providerId,
            tier,
          },
        },
      });

      return {
        success: true,
        sessionId: session.id,
        url: session.url,
      };
    } catch (error: any) {
      console.error('Error creating subscription checkout:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Handle successful subscription creation
   */
  static async handleSubscriptionCreated(stripeSubscription: any) {
    try {
      const providerId = stripeSubscription.metadata.providerId;
      const tier = stripeSubscription.metadata.tier;

      const subscription: ProviderSubscription = {
        id: providerId,
        providerId,
        tier,
        status: 'active',
        stripeCustomerId: stripeSubscription.customer,
        stripeSubscriptionId: stripeSubscription.id,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000).toISOString(),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
        cancelAtPeriodEnd: false,
        bookingsThisMonth: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Save subscription to Firestore
      await setDoc(doc(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS, providerId), subscription);

      // Update provider document
      await updateDoc(doc(db, COLLECTIONS.PROVIDERS, providerId), {
        membershipTier: tier,
        subscriptionStatus: 'active',
        updatedAt: new Date().toISOString(),
      });

      return { success: true };
    } catch (error: any) {
      console.error('Error handling subscription created:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(providerId: string, cancelAtPeriodEnd: boolean = true) {
    try {
      // Get subscription data
      const subscriptionDoc = await getDoc(doc(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS, providerId));
      if (!subscriptionDoc.exists()) {
        throw new Error('Subscription not found');
      }

      const subscription = subscriptionDoc.data() as ProviderSubscription;
      if (!subscription.stripeSubscriptionId) {
        throw new Error('No Stripe subscription found');
      }

      // Cancel in Stripe
      if (cancelAtPeriodEnd) {
        await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
          cancel_at_period_end: true,
        });
      } else {
        await stripe.subscriptions.cancel(subscription.stripeSubscriptionId);
      }

      // Update Firestore
      await updateDoc(doc(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS, providerId), {
        cancelAtPeriodEnd,
        status: cancelAtPeriodEnd ? 'active' : 'cancelled',
        updatedAt: new Date().toISOString(),
      });

      if (!cancelAtPeriodEnd) {
        // Immediately downgrade to free tier
        await updateDoc(doc(db, COLLECTIONS.PROVIDERS, providerId), {
          membershipTier: 'free',
          subscriptionStatus: 'cancelled',
          updatedAt: new Date().toISOString(),
        });
      }

      return { success: true };
    } catch (error: any) {
      console.error('Error cancelling subscription:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get provider subscription status
   */
  static async getSubscription(providerId: string): Promise<ProviderSubscription | null> {
    try {
      const subscriptionDoc = await getDoc(doc(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS, providerId));
      if (!subscriptionDoc.exists()) {
        return null;
      }
      return subscriptionDoc.data() as ProviderSubscription;
    } catch (error) {
      console.error('Error getting subscription:', error);
      return null;
    }
  }

  /**
   * Check if provider can create more bookings
   */
  static async canCreateBooking(providerId: string): Promise<boolean> {
    try {
      const subscription = await this.getSubscription(providerId);
      if (!subscription) {
        // No subscription = free tier
        return false; // Will be checked against free tier limits
      }

      const tierConfig = SUBSCRIPTION_TIERS[subscription.tier];
      if (tierConfig.features.bookingLimit === null) {
        return true; // Unlimited
      }

      return subscription.bookingsThisMonth < tierConfig.features.bookingLimit;
    } catch (error) {
      console.error('Error checking booking limit:', error);
      return false;
    }
  }

  /**
   * Increment booking count for the month
   */
  static async incrementBookingCount(providerId: string) {
    try {
      const subscriptionRef = doc(db, COLLECTIONS.PROVIDER_SUBSCRIPTIONS, providerId);
      const subscriptionDoc = await getDoc(subscriptionRef);
      
      if (subscriptionDoc.exists()) {
        const subscription = subscriptionDoc.data() as ProviderSubscription;
        await updateDoc(subscriptionRef, {
          bookingsThisMonth: subscription.bookingsThisMonth + 1,
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error incrementing booking count:', error);
    }
  }
}
