// TEMPORARY STUB IMPLEMENTATION FOR REWARD SERVICE
// This prevents import errors while booking system works
// TODO: Implement proper Firestore-based reward system

export interface RewardItem {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  category: 'service_discount' | 'free_service' | 'merchandise' | 'cashback';
  discountAmount?: number;
  serviceId?: string;
  isActive: boolean;
  expirationDays?: number;
  image?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface RewardTransaction {
  id: string;
  userId: string;
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  bookingId?: string;
  referralId?: string;
  rewardItemId?: string;
  rewardItemName?: string;
  createdAt: string;
  expiresAt?: string;
}

export class RewardService {
  // STUB METHODS - All return empty/default values to prevent errors

  static async getActiveRewardItems(): Promise<RewardItem[]> {
    return [];
  }

  static async getRewardItemsByCategory(category: string): Promise<RewardItem[]> {
    return [];
  }

  static async getRewardItemById(itemId: string): Promise<RewardItem | null> {
    return null;
  }

  static async redeemReward(userId: string, itemId: string): Promise<{
    success: boolean;
    message: string;
    newBalance?: number;
  }> {
    return { success: false, message: 'Reward system not implemented yet' };
  }

  static async awardPoints(userId: string, points: number, description: string, bookingId?: string): Promise<number> {
    console.log(`Would award ${points} points to user ${userId} for: ${description}`);
    return 0; // Return 0 points for now
  }

  static async getUserRewardHistory(userId: string, limit: number = 50, offset: number = 0): Promise<RewardTransaction[]> {
    return [];
  }

  static async getUserActiveRewards(userId: string): Promise<RewardTransaction[]> {
    return [];
  }

  static async processReferralReward(referrerId: string, referredUserId: string): Promise<number> {
    return 0;
  }
}

  // Calculate points for booking completion
  static calculateBookingPoints(bookingAmount: number): number {
    // Award 1 point per dollar spent, with bonus for larger amounts
    let points = Math.floor(bookingAmount);
    
    // Bonus points for larger bookings
    if (bookingAmount >= 100) {
      points += Math.floor(bookingAmount * 0.1); // 10% bonus
    } else if (bookingAmount >= 50) {
      points += Math.floor(bookingAmount * 0.05); // 5% bonus
    }

    return points;
  }

  // Award points for completed booking
  static async awardBookingPoints(userId: string, bookingId: string, bookingAmount: number): Promise<number> {
    try {
      const points = this.calculateBookingPoints(bookingAmount);
      const description = `Points earned from booking completion ($${bookingAmount.toFixed(2)})`;
      
      return await this.awardPoints(userId, points, description, bookingId);
    } catch (error) {
      console.error('Error awarding booking points:', error);
      throw new Error('Failed to award booking points');
    }
  }

  // Award referral points
  static async awardReferralPoints(referrerId: string, referredUserId: string): Promise<number> {
    try {
      const points = 100; // Fixed referral bonus
      const description = `Referral bonus for inviting a new user`;
      
      const newBalance = await this.awardPoints(referrerId, points, description);

      // Record the referral relationship
      await query(`
        INSERT INTO reward_transactions (
          user_id, type, points, description, referral_id
        ) VALUES ($1, 'earned', $2, $3, $4)
      `, [referrerId, points, description, referredUserId]);

      return newBalance;
    } catch (error) {
      console.error('Error awarding referral points:', error);
      throw new Error('Failed to award referral points');
    }
  }

  // Helper methods
  private static mapDatabaseRewardItemToRewardItem(dbItem: any): RewardItem {
    return {
      id: dbItem.id,
      name: dbItem.name,
      description: dbItem.description,
      pointsCost: dbItem.points_cost,
      category: dbItem.category,
      discountAmount: parseFloat(dbItem.discount_amount) || undefined,
      serviceId: dbItem.service_id,
      isActive: dbItem.is_active,
      expirationDays: dbItem.expiration_days,
      image: dbItem.image,
      createdAt: dbItem.created_at.toISOString(),
      updatedAt: dbItem.updated_at?.toISOString()
    };
  }

  private static mapDatabaseRewardTransactionToRewardTransaction(dbTransaction: any): RewardTransaction {
    return {
      id: dbTransaction.id,
      userId: dbTransaction.user_id,
      type: dbTransaction.type,
      points: dbTransaction.points,
      description: dbTransaction.description,
      bookingId: dbTransaction.booking_id,
      referralId: dbTransaction.referral_id,
      rewardItemId: dbTransaction.reward_item_id,
      rewardItemName: dbTransaction.reward_item_name,
      createdAt: dbTransaction.created_at.toISOString(),
      expiresAt: dbTransaction.expires_at?.toISOString()
    };
  }
}
