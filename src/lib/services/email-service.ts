// Email notification service for Fetchly
// Handles all email <NAME_EMAIL>

interface EmailNotification {
  type: 'signup' | 'purchase' | 'booking' | 'provider_registration' | 'payment' | 'error' | 'general';
  subject: string;
  data: any;
  timestamp: Date;
}

interface UserSignupData {
  userId: string;
  email: string;
  name: string;
  role: 'provider' | 'petowner';
  timestamp: Date;
}

interface PurchaseData {
  userId: string;
  userEmail: string;
  userName: string;
  amount: number;
  currency: string;
  productType: string;
  transactionId: string;
  timestamp: Date;
}

interface BookingData {
  bookingId: string;
  clientId: string;
  clientEmail: string;
  clientName: string;
  providerId: string;
  providerEmail: string;
  providerName: string;
  serviceType: string;
  amount: number;
  bookingDate: Date;
  timestamp: Date;
}

class EmailNotificationService {
  private readonly adminEmail = '<EMAIL>';
  
  // For production, you'll need to configure this with your email service
  // Options: SendGrid, AWS SES, Nodemailer with SMTP, etc.
  
  /**
   * Send notification for new user signup
   */
  async notifyUserSignup(userData: UserSignupData): Promise<void> {
    const emailData: EmailNotification = {
      type: 'signup',
      subject: `🎉 New ${userData.role === 'provider' ? 'Provider' : 'Pet Owner'} Signup - ${userData.name}`,
      data: userData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send notification for new purchase/payment
   */
  async notifyPurchase(purchaseData: PurchaseData): Promise<void> {
    const emailData: EmailNotification = {
      type: 'purchase',
      subject: `💰 New Purchase - $${purchaseData.amount} from ${purchaseData.userName}`,
      data: purchaseData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send notification for new booking
   */
  async notifyBooking(bookingData: BookingData): Promise<void> {
    const emailData: EmailNotification = {
      type: 'booking',
      subject: `📅 New Booking - ${bookingData.serviceType} ($${bookingData.amount})`,
      data: bookingData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send notification for provider registration
   */
  async notifyProviderRegistration(providerData: any): Promise<void> {
    const emailData: EmailNotification = {
      type: 'provider_registration',
      subject: `🏥 New Provider Registration - ${providerData.businessName}`,
      data: providerData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send general notification
   */
  async notifyGeneral(subject: string, data: any): Promise<void> {
    const emailData: EmailNotification = {
      type: 'general',
      subject: subject,
      data: data,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Core method to send email notifications
   * This is where you'll integrate with your email service
   */
  private async sendNotification(emailData: EmailNotification): Promise<void> {
    try {
      // For now, log to console - replace with actual email service
      console.log('📧 EMAIL NOTIFICATION:', {
        to: this.adminEmail,
        subject: emailData.subject,
        type: emailData.type,
        timestamp: emailData.timestamp,
        data: emailData.data
      });

      // TODO: Replace with actual email service integration
      // Example implementations below:

      // Option 1: SendGrid (Recommended)
      // await this.sendWithSendGrid(emailData);

      // Option 2: AWS SES
      // await this.sendWithAWSSES(emailData);

      // Option 3: Nodemailer with SMTP (for GoDaddy/Outlook 365)
      // await this.sendWithNodemailer(emailData);

      // For now, we'll store in Firestore as backup
      await this.storeNotificationRecord(emailData);

    } catch (error) {
      console.error('Failed to send email notification:', error);
      // Store failed notification for retry
      await this.storeFailedNotification(emailData, error);
    }
  }

  /**
   * Store notification record in Firestore
   */
  private async storeNotificationRecord(emailData: EmailNotification): Promise<void> {
    try {
      // Import Firebase here to avoid circular dependencies
      const { db } = await import('@/lib/firebase');
      const { addDoc, collection } = await import('firebase/firestore');

      await addDoc(collection(db, 'email_notifications'), {
        ...emailData,
        status: 'sent',
        sentAt: new Date()
      });
    } catch (error) {
      console.error('Failed to store notification record:', error);
    }
  }

  /**
   * Store failed notification for retry
   */
  private async storeFailedNotification(emailData: EmailNotification, error: any): Promise<void> {
    try {
      const { db } = await import('@/lib/firebase');
      const { addDoc, collection } = await import('firebase/firestore');

      await addDoc(collection(db, 'failed_notifications'), {
        ...emailData,
        status: 'failed',
        error: error.message,
        failedAt: new Date(),
        retryCount: 0
      });
    } catch (storeError) {
      console.error('Failed to store failed notification:', storeError);
    }
  }

  /**
   * Example SendGrid integration (uncomment and configure when ready)
   */
  /*
  private async sendWithSendGrid(emailData: EmailNotification): Promise<void> {
    const sgMail = require('@sendgrid/mail');
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);

    const msg = {
      to: this.adminEmail,
      from: '<EMAIL>', // Your verified sender
      subject: emailData.subject,
      html: this.formatEmailHTML(emailData),
    };

    await sgMail.send(msg);
  }
  */

  /**
   * Example Nodemailer integration for GoDaddy/Outlook 365
   */
  /*
  private async sendWithNodemailer(emailData: EmailNotification): Promise<void> {
    const nodemailer = require('nodemailer');

    const transporter = nodemailer.createTransporter({
      host: 'smtp.office365.com', // For Outlook 365
      port: 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER, // Your GoDaddy email
        pass: process.env.EMAIL_PASSWORD, // Your email password or app password
      },
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: this.adminEmail,
      subject: emailData.subject,
      html: this.formatEmailHTML(emailData),
    };

    await transporter.sendMail(mailOptions);
  }
  */

  /**
   * Format email content as HTML
   */
  private formatEmailHTML(emailData: EmailNotification): string {
    const { type, data, timestamp } = emailData;
    
    let content = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 20px; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0;">Fetchly Notification</h1>
          <p style="color: #e5f3ff; margin: 5px 0 0 0;">Type: ${type.toUpperCase()}</p>
        </div>
        <div style="background: #f8fafc; padding: 20px; border-radius: 0 0 10px 10px; border: 1px solid #e2e8f0;">
          <p style="color: #64748b; margin: 0 0 20px 0;">Received: ${timestamp.toLocaleString()}</p>
    `;

    // Add type-specific content
    switch (type) {
      case 'signup':
        content += `
          <h3 style="color: #1e293b;">New User Registration</h3>
          <ul style="color: #475569;">
            <li><strong>Name:</strong> ${data.name}</li>
            <li><strong>Email:</strong> ${data.email}</li>
            <li><strong>Role:</strong> ${data.role}</li>
            <li><strong>User ID:</strong> ${data.userId}</li>
          </ul>
        `;
        break;
      
      case 'purchase':
        content += `
          <h3 style="color: #1e293b;">New Purchase</h3>
          <ul style="color: #475569;">
            <li><strong>Customer:</strong> ${data.userName} (${data.userEmail})</li>
            <li><strong>Amount:</strong> $${data.amount} ${data.currency}</li>
            <li><strong>Product:</strong> ${data.productType}</li>
            <li><strong>Transaction ID:</strong> ${data.transactionId}</li>
          </ul>
        `;
        break;
      
      case 'booking':
        content += `
          <h3 style="color: #1e293b;">New Booking</h3>
          <ul style="color: #475569;">
            <li><strong>Service:</strong> ${data.serviceType}</li>
            <li><strong>Client:</strong> ${data.clientName} (${data.clientEmail})</li>
            <li><strong>Provider:</strong> ${data.providerName} (${data.providerEmail})</li>
            <li><strong>Amount:</strong> $${data.amount}</li>
            <li><strong>Booking Date:</strong> ${data.bookingDate.toLocaleDateString()}</li>
            <li><strong>Booking ID:</strong> ${data.bookingId}</li>
          </ul>
        `;
        break;
      
      default:
        content += `
          <h3 style="color: #1e293b;">Notification Data</h3>
          <pre style="background: #f1f5f9; padding: 15px; border-radius: 5px; overflow-x: auto; color: #475569;">${JSON.stringify(data, null, 2)}</pre>
        `;
    }

    content += `
        </div>
        <div style="text-align: center; padding: 20px; color: #94a3b8; font-size: 12px;">
          <p>This is an automated notification from Fetchly Platform</p>
          <p>© 2025 Fetchly. All rights reserved.</p>
        </div>
      </div>
    `;

    return content;
  }
}

// Export singleton instance
export const emailNotificationService = new EmailNotificationService();
export default emailNotificationService;
