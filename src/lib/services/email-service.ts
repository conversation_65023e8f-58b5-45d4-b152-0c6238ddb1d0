// Email notification service for Fetchly
// Handles all email <NAME_EMAIL>

interface EmailNotification {
  type: 'signup' | 'purchase' | 'booking' | 'provider_registration' | 'payment' | 'error' | 'general' | 'subscription';
  subject: string;
  data: any;
  timestamp: Date;
}

interface UserSignupData {
  userId: string;
  email: string;
  name: string;
  role: 'provider' | 'petowner';
  timestamp: Date;
}

interface PurchaseData {
  userId: string;
  userEmail: string;
  userName: string;
  amount: number;
  currency: string;
  productType: string;
  transactionId: string;
  timestamp: Date;
}

interface BookingData {
  bookingId: string;
  clientId: string;
  clientEmail: string;
  clientName: string;
  providerId: string;
  providerEmail: string;
  providerName: string;
  serviceType: string;
  amount: number;
  bookingDate: Date;
  timestamp: Date;
}

interface SubscriptionData {
  email: string;
  source: string; // 'newsletter', 'footer', 'popup', etc.
  userAgent?: string;
  ipAddress?: string;
  timestamp: Date;
}

class EmailNotificationService {
  private readonly adminEmail = '<EMAIL>';
  
  // For production, you'll need to configure this with your email service
  // Options: SendGrid, AWS SES, Nodemailer with SMTP, etc.
  
  /**
   * Send notification for new user signup (to admin)
   */
  async notifyUserSignup(userData: UserSignupData): Promise<void> {
    const emailData: EmailNotification = {
      type: 'signup',
      subject: `🎉 New ${userData.role === 'provider' ? 'Provider' : 'Pet Owner'} Signup - ${userData.name}`,
      data: userData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send welcome email to new user
   */
  async sendWelcomeEmail(userData: UserSignupData): Promise<void> {
    // Only run on server side
    if (typeof window !== 'undefined') {
      console.log('Email service: Skipping client-side email send');
      return;
    }

    try {
      const nodemailer = require('nodemailer');

      const transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST || 'smtp.office365.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        secure: false,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD,
        },
        tls: {
          ciphers: 'SSLv3'
        }
      });

      const welcomeHTML = this.formatWelcomeEmailHTML(userData);

      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: userData.email,
        subject: `🎉 Welcome to Fetchly, ${userData.name}!`,
        html: welcomeHTML,
      };

      await transporter.sendMail(mailOptions);
      console.log(`✅ Welcome email sent to ${userData.email}`);
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      // Don't throw error - welcome email failure shouldn't break signup
    }
  }

  /**
   * Send password reset confirmation email to user
   */
  async sendPasswordResetConfirmation(userEmail: string, userName: string): Promise<void> {
    // Only run on server side
    if (typeof window !== 'undefined') {
      console.log('Email service: Skipping client-side email send');
      return;
    }

    try {
      const nodemailer = require('nodemailer');

      const transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST || 'smtp.office365.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        secure: false,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD,
        },
        tls: {
          ciphers: 'SSLv3'
        }
      });

      const confirmationHTML = this.formatPasswordResetConfirmationHTML(userName);

      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: userEmail,
        subject: `🔐 Your Fetchly Password Has Been Reset`,
        html: confirmationHTML,
      };

      await transporter.sendMail(mailOptions);
      console.log(`✅ Password reset confirmation sent to ${userEmail}`);
    } catch (error) {
      console.error('Failed to send password reset confirmation:', error);
    }
  }

  /**
   * Send notification for new purchase/payment
   */
  async notifyPurchase(purchaseData: PurchaseData): Promise<void> {
    const emailData: EmailNotification = {
      type: 'purchase',
      subject: `💰 New Purchase - $${purchaseData.amount} from ${purchaseData.userName}`,
      data: purchaseData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send notification for new booking
   */
  async notifyBooking(bookingData: BookingData): Promise<void> {
    const emailData: EmailNotification = {
      type: 'booking',
      subject: `📅 New Booking - ${bookingData.serviceType} ($${bookingData.amount})`,
      data: bookingData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send notification for provider registration
   */
  async notifyProviderRegistration(providerData: any): Promise<void> {
    const emailData: EmailNotification = {
      type: 'provider_registration',
      subject: `🏥 New Provider Registration - ${providerData.businessName}`,
      data: providerData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send notification for new newsletter subscription
   */
  async notifySubscription(subscriptionData: SubscriptionData): Promise<void> {
    const emailData: EmailNotification = {
      type: 'subscription',
      subject: `📧 New Newsletter Subscription - ${subscriptionData.email}`,
      data: subscriptionData,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Send general notification
   */
  async notifyGeneral(subject: string, data: any): Promise<void> {
    const emailData: EmailNotification = {
      type: 'general',
      subject: subject,
      data: data,
      timestamp: new Date()
    };

    await this.sendNotification(emailData);
  }

  /**
   * Test email function - use this to verify your email setup
   */
  async testEmail(): Promise<void> {
    await this.notifyGeneral('🧪 Test Email from Fetchly Platform', {
      message: 'Congratulations! Your email notification system is working correctly.',
      timestamp: new Date(),
      platform: 'Fetchly',
      environment: process.env.NODE_ENV || 'development',
      testType: 'Email Configuration Test'
    });
  }

  /**
   * Core method to send email notifications
   * This is where you'll integrate with your email service
   */
  private async sendNotification(emailData: EmailNotification): Promise<void> {
    try {
      // For now, log to console - replace with actual email service
      console.log('📧 EMAIL NOTIFICATION:', {
        to: this.adminEmail,
        subject: emailData.subject,
        type: emailData.type,
        timestamp: emailData.timestamp,
        data: emailData.data
      });

      // Send email using Nodemailer with Outlook 365
      await this.sendWithNodemailer(emailData);

      // For now, we'll store in Firestore as backup
      await this.storeNotificationRecord(emailData);

    } catch (error) {
      console.error('Failed to send email notification:', error);
      // Store failed notification for retry
      await this.storeFailedNotification(emailData, error);
    }
  }

  /**
   * Store notification record in Firestore
   */
  private async storeNotificationRecord(emailData: EmailNotification): Promise<void> {
    try {
      // Import Firebase here to avoid circular dependencies
      const { db } = await import('@/lib/firebase');
      const { addDoc, collection } = await import('firebase/firestore');

      await addDoc(collection(db, 'email_notifications'), {
        ...emailData,
        status: 'sent',
        sentAt: new Date()
      });
    } catch (error) {
      console.error('Failed to store notification record:', error);
    }
  }

  /**
   * Store failed notification for retry
   */
  private async storeFailedNotification(emailData: EmailNotification, error: any): Promise<void> {
    try {
      const { db } = await import('@/lib/firebase');
      const { addDoc, collection } = await import('firebase/firestore');

      await addDoc(collection(db, 'failed_notifications'), {
        ...emailData,
        status: 'failed',
        error: error.message,
        failedAt: new Date(),
        retryCount: 0
      });
    } catch (storeError) {
      console.error('Failed to store failed notification:', storeError);
    }
  }

  /**
   * Example SendGrid integration (uncomment and configure when ready)
   */
  /*
  private async sendWithSendGrid(emailData: EmailNotification): Promise<void> {
    const sgMail = require('@sendgrid/mail');
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);

    const msg = {
      to: this.adminEmail,
      from: '<EMAIL>', // Your verified sender
      subject: emailData.subject,
      html: this.formatEmailHTML(emailData),
    };

    await sgMail.send(msg);
  }
  */

  /**
   * Nodemailer integration for GoDaddy/Outlook 365
   */
  private async sendWithNodemailer(emailData: EmailNotification): Promise<void> {
    // Only run on server side
    if (typeof window !== 'undefined') {
      console.log('Email service: Skipping client-side email send');
      return;
    }

    const nodemailer = require('nodemailer');

    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.office365.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
      tls: {
        ciphers: 'SSLv3'
      }
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: this.adminEmail,
      subject: emailData.subject,
      html: this.formatEmailHTML(emailData),
    };

    await transporter.sendMail(mailOptions);
  }

  /**
   * Format email content as HTML
   */
  private formatEmailHTML(emailData: EmailNotification): string {
    const { type, data, timestamp } = emailData;
    
    let content = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 20px; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0;">Fetchly Notification</h1>
          <p style="color: #e5f3ff; margin: 5px 0 0 0;">Type: ${type.toUpperCase()}</p>
        </div>
        <div style="background: #f8fafc; padding: 20px; border-radius: 0 0 10px 10px; border: 1px solid #e2e8f0;">
          <p style="color: #64748b; margin: 0 0 20px 0;">Received: ${timestamp.toLocaleString()}</p>
    `;

    // Add type-specific content
    switch (type) {
      case 'signup':
        content += `
          <h3 style="color: #1e293b;">New User Registration</h3>
          <ul style="color: #475569;">
            <li><strong>Name:</strong> ${data.name}</li>
            <li><strong>Email:</strong> ${data.email}</li>
            <li><strong>Role:</strong> ${data.role}</li>
            <li><strong>User ID:</strong> ${data.userId}</li>
          </ul>
        `;
        break;
      
      case 'purchase':
        content += `
          <h3 style="color: #1e293b;">New Purchase</h3>
          <ul style="color: #475569;">
            <li><strong>Customer:</strong> ${data.userName} (${data.userEmail})</li>
            <li><strong>Amount:</strong> $${data.amount} ${data.currency}</li>
            <li><strong>Product:</strong> ${data.productType}</li>
            <li><strong>Transaction ID:</strong> ${data.transactionId}</li>
          </ul>
        `;
        break;
      
      case 'booking':
        content += `
          <h3 style="color: #1e293b;">New Booking</h3>
          <ul style="color: #475569;">
            <li><strong>Service:</strong> ${data.serviceType}</li>
            <li><strong>Client:</strong> ${data.clientName} (${data.clientEmail})</li>
            <li><strong>Provider:</strong> ${data.providerName} (${data.providerEmail})</li>
            <li><strong>Amount:</strong> $${data.amount}</li>
            <li><strong>Booking Date:</strong> ${data.bookingDate.toLocaleDateString()}</li>
            <li><strong>Booking ID:</strong> ${data.bookingId}</li>
          </ul>
        `;
        break;

      case 'subscription':
        content += `
          <h3 style="color: #1e293b;">New Newsletter Subscription</h3>
          <ul style="color: #475569;">
            <li><strong>Email:</strong> ${data.email}</li>
            <li><strong>Source:</strong> ${data.source}</li>
            <li><strong>Timestamp:</strong> ${data.timestamp.toLocaleString()}</li>
            ${data.userAgent ? `<li><strong>User Agent:</strong> ${data.userAgent}</li>` : ''}
            ${data.ipAddress ? `<li><strong>IP Address:</strong> ${data.ipAddress}</li>` : ''}
          </ul>
        `;
        break;
      
      default:
        content += `
          <h3 style="color: #1e293b;">Notification Data</h3>
          <pre style="background: #f1f5f9; padding: 15px; border-radius: 5px; overflow-x: auto; color: #475569;">${JSON.stringify(data, null, 2)}</pre>
        `;
    }

    content += `
        </div>
        <div style="text-align: center; padding: 20px; color: #94a3b8; font-size: 12px;">
          <p>This is an automated notification from Fetchly Platform</p>
          <p>© 2025 Fetchly. All rights reserved.</p>
        </div>
      </div>
    `;

    return content;
  }

  /**
   * Format welcome email HTML for new users
   */
  private formatWelcomeEmailHTML(userData: UserSignupData): string {
    const isProvider = userData.role === 'provider';

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 30px; border-radius: 15px 15px 0 0; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to Fetchly! 🎉</h1>
          <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 18px;">Your pet care journey starts here</p>
        </div>

        <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 15px 15px; border: 1px solid #e2e8f0;">
          <h2 style="color: #1e293b; margin-top: 0;">Hi ${userData.name}! 👋</h2>

          <p style="color: #475569; line-height: 1.6;">
            Thank you for joining Fetchly as a ${isProvider ? 'Pet Care Provider' : 'Pet Owner'}!
            We're excited to have you as part of our growing community in Puerto Rico.
          </p>

          ${isProvider ? `
            <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 20px; border-radius: 10px; margin: 20px 0;">
              <h3 style="color: white; margin-top: 0;">🏥 Provider Benefits</h3>
              <ul style="color: #e5f3ff; margin: 0;">
                <li>Connect with pet owners in your area</li>
                <li>Manage your services and bookings</li>
                <li>Build your professional reputation</li>
                <li>Grow your pet care business</li>
              </ul>
            </div>
          ` : `
            <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 20px; border-radius: 10px; margin: 20px 0;">
              <h3 style="color: white; margin-top: 0;">🐾 Pet Owner Benefits</h3>
              <ul style="color: #e5f3ff; margin: 0;">
                <li>Find trusted pet care providers</li>
                <li>Book services easily</li>
                <li>Connect with the pet community</li>
                <li>Access pet care resources</li>
              </ul>
            </div>
          `}

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL || 'https://fetchlyapp.herokuapp.com'}/auth/signin"
               style="background: linear-gradient(135deg, #10b981, #3b82f6); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
              Get Started Now
            </a>
          </div>

          <div style="background: #f1f5f9; padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h4 style="color: #1e293b; margin-top: 0;">📞 Need Help?</h4>
            <p style="color: #475569; margin: 0;">
              Our support team is here to help! Contact us at
              <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
            </p>
          </div>
        </div>

        <div style="text-align: center; padding: 20px; color: #94a3b8; font-size: 12px;">
          <p>Welcome to the Fetchly family! 🐕🐱</p>
          <p>© 2025 Fetchly. All rights reserved.</p>
        </div>
      </div>
    `;
  }

  /**
   * Format password reset confirmation email HTML
   */
  private formatPasswordResetConfirmationHTML(userName: string): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #10b981, #3b82f6); padding: 30px; border-radius: 15px 15px 0 0; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Password Reset Successful 🔐</h1>
          <p style="color: #e5f3ff; margin: 10px 0 0 0; font-size: 18px;">Your account is secure</p>
        </div>

        <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 15px 15px; border: 1px solid #e2e8f0;">
          <h2 style="color: #1e293b; margin-top: 0;">Hi ${userName}! 👋</h2>

          <p style="color: #475569; line-height: 1.6;">
            Your Fetchly account password has been successfully reset. You can now sign in with your new password.
          </p>

          <div style="background: #dcfce7; border: 1px solid #bbf7d0; padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3 style="color: #166534; margin-top: 0;">✅ What happened?</h3>
            <ul style="color: #166534; margin: 0;">
              <li>Your password was successfully changed</li>
              <li>Your account remains secure</li>
              <li>You can now sign in with your new password</li>
            </ul>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL || 'https://fetchlyapp.herokuapp.com'}/auth/signin"
               style="background: linear-gradient(135deg, #10b981, #3b82f6); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
              Sign In Now
            </a>
          </div>

          <div style="background: #fef3c7; border: 1px solid #fcd34d; padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h4 style="color: #92400e; margin-top: 0;">🛡️ Security Tip</h4>
            <p style="color: #92400e; margin: 0;">
              If you didn't request this password reset, please contact our support team immediately at
              <a href="mailto:<EMAIL>" style="color: #92400e;"><EMAIL></a>
            </p>
          </div>
        </div>

        <div style="text-align: center; padding: 20px; color: #94a3b8; font-size: 12px;">
          <p>Your security is our priority 🔒</p>
          <p>© 2025 Fetchly. All rights reserved.</p>
        </div>
      </div>
    `;
  }
}

// Export singleton instance
export const emailNotificationService = new EmailNotificationService();
export default emailNotificationService;
