import { db } from '@/lib/firebase';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  query,
  where,
  getDocs,
  getDoc,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';

export interface ChatNotification {
  id?: string;
  userId: string;
  chatId: string;
  messageId: string;
  senderName: string;
  senderAvatar?: string;
  messageText: string;
  chatName?: string;
  isGroupChat: boolean;
  isSupportChat: boolean;
  createdAt: Date;
  isRead: boolean;
  notificationType: 'new_message' | 'new_chat' | 'support_message';
}

class NotificationService {
  private readonly COLLECTIONS = {
    NOTIFICATIONS: 'chat_notifications',
    USERS: 'users'
  };

  /**
   * Create notification for new message
   */
  async createMessageNotification(
    recipientId: string,
    chatId: string,
    messageId: string,
    senderName: string,
    messageText: string,
    chatName?: string,
    isGroupChat: boolean = false,
    isSupportChat: boolean = false,
    senderAvatar?: string
  ): Promise<void> {
    try {
      const notification: Omit<ChatNotification, 'id'> = {
        userId: recipientId,
        chatId,
        messageId,
        senderName,
        senderAvatar,
        messageText: messageText.length > 100 ? messageText.substring(0, 100) + '...' : messageText,
        chatName,
        isGroupChat,
        isSupportChat,
        createdAt: new Date(),
        isRead: false,
        notificationType: isSupportChat ? 'support_message' : 'new_message'
      };

      await addDoc(collection(db, this.COLLECTIONS.NOTIFICATIONS), {
        ...notification,
        createdAt: Timestamp.fromDate(notification.createdAt)
      });

      console.log('Notification created for user:', recipientId);
    } catch (error) {
      console.error('Error creating notification:', error);
    }
  }

  /**
   * Create notifications for all chat participants except sender
   */
  async notifyParticipants(
    participantIds: string[],
    senderId: string,
    chatId: string,
    messageId: string,
    senderName: string,
    messageText: string,
    chatName?: string,
    isGroupChat: boolean = false,
    isSupportChat: boolean = false,
    senderAvatar?: string
  ): Promise<void> {
    const recipients = participantIds.filter(id => id !== senderId);
    
    const promises = recipients.map(recipientId =>
      this.createMessageNotification(
        recipientId,
        chatId,
        messageId,
        senderName,
        messageText,
        chatName,
        isGroupChat,
        isSupportChat,
        senderAvatar
      )
    );

    await Promise.all(promises);
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const notificationRef = doc(db, this.COLLECTIONS.NOTIFICATIONS, notificationId);
      await updateDoc(notificationRef, {
        isRead: true,
        readAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Mark all notifications for a chat as read
   */
  async markChatNotificationsAsRead(userId: string, chatId: string): Promise<void> {
    try {
      const notificationsQuery = query(
        collection(db, this.COLLECTIONS.NOTIFICATIONS),
        where('userId', '==', userId),
        where('chatId', '==', chatId),
        where('isRead', '==', false)
      );

      const snapshot = await getDocs(notificationsQuery);
      const promises = snapshot.docs.map(doc =>
        updateDoc(doc.ref, {
          isRead: true,
          readAt: serverTimestamp()
        })
      );

      await Promise.all(promises);
    } catch (error) {
      console.error('Error marking chat notifications as read:', error);
    }
  }

  /**
   * Get unread notifications for user
   */
  async getUnreadNotifications(userId: string): Promise<ChatNotification[]> {
    try {
      const notificationsQuery = query(
        collection(db, this.COLLECTIONS.NOTIFICATIONS),
        where('userId', '==', userId),
        where('isRead', '==', false),
        orderBy('createdAt', 'desc'),
        limit(50)
      );

      const snapshot = await getDocs(notificationsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate()
      })) as ChatNotification[];
    } catch (error) {
      console.error('Error getting unread notifications:', error);
      return [];
    }
  }

  /**
   * Get notification count for user
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const notificationsQuery = query(
        collection(db, this.COLLECTIONS.NOTIFICATIONS),
        where('userId', '==', userId),
        where('isRead', '==', false)
      );

      const snapshot = await getDocs(notificationsQuery);
      return snapshot.size;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  /**
   * Send browser notification if permission granted
   */
  async sendBrowserNotification(
    title: string,
    body: string,
    icon?: string,
    chatId?: string
  ): Promise<void> {
    if ('Notification' in window && Notification.permission === 'granted') {
      try {
        const notification = new Notification(title, {
          body,
          icon: icon || '/favicon.png',
          badge: '/favicon.png',
          tag: chatId || 'fetchly-chat',
          requireInteraction: false,
          silent: false
        });

        // Auto close after 5 seconds
        setTimeout(() => {
          notification.close();
        }, 5000);

        // Handle click to open chat
        notification.onclick = () => {
          window.focus();
          if (chatId) {
            // You could implement navigation to specific chat here
            window.location.href = `/chat?id=${chatId}`;
          }
          notification.close();
        };
      } catch (error) {
        console.error('Error sending browser notification:', error);
      }
    }
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission;
    }
    return 'denied';
  }
}

export const notificationService = new NotificationService();
export default notificationService;
