import { DatabaseService } from '../database';
import { Booking, BookingStatus } from '@/types/user';
import { Timestamp } from 'firebase/firestore';

export class BookingService {
  // Get all bookings for a user
  static async getUserBookings(userId: string, status?: BookingStatus): Promise<Booking[]> {
    try {
      const conditions = [
        { field: 'userId', operator: '==', value: userId }
      ];

      if (status) {
        conditions.push({ field: 'status', operator: '==', value: status });
      }

      const bookings = await DatabaseService.query(
        'bookings',
        conditions,
        'scheduledDate',
        'desc'
      );

      return bookings.map(booking => this.mapFirestoreBookingToBooking(booking));
    } catch (error) {
      console.error('Error getting user bookings:', error);
      throw new Error('Failed to get bookings');
    }
  }

  // Helper method to map Firestore document to Booking object
  private static mapFirestoreBookingToBooking(doc: any): Booking {
    const data = doc.data ? doc.data() : doc;
    return {
      id: doc.id || '',
      userId: data.userId || data.user_id || '',
      petId: data.petId || data.pet_id || '',
      providerId: data.providerId || data.provider_id || '',
      serviceId: data.serviceId || data.service_id || '',
      serviceName: data.serviceName || data.service_name || '',
      providerName: data.providerName || data.provider_name || '',
      petName: data.petName || data.pet_name || '',
      scheduledDate: data.scheduledDate?.toDate?.() || data.scheduled_date?.toDate?.() || data.scheduledDate || data.scheduled_date,
      scheduledTime: data.scheduledTime || data.scheduled_time || '',
      duration: data.duration || 0,
      status: data.status || 'pending',
      totalPrice: data.totalPrice || data.total_price || 0,
      paidAmount: data.paidAmount || data.paid_amount || 0,
      paymentMethod: data.paymentMethod || data.payment_method || '',
      notes: data.notes || '',
      specialRequests: data.specialRequests || data.special_requests || '',
      rating: data.rating || 0,
      review: data.review || '',
      reviewDate: data.reviewDate?.toDate?.() || data.review_date?.toDate?.() || data.reviewDate || data.review_date,
      createdAt: data.createdAt?.toDate?.() || data.created_at?.toDate?.() || data.createdAt || data.created_at,
      updatedAt: data.updatedAt?.toDate?.() || data.updated_at?.toDate?.() || data.updatedAt || data.updated_at,
      completedAt: data.completedAt?.toDate?.() || data.completed_at?.toDate?.() || data.completedAt || data.completed_at,
      cancelledAt: data.cancelledAt?.toDate?.() || data.cancelled_at?.toDate?.() || data.cancelledAt || data.cancelled_at,
      cancellationReason: data.cancellationReason || data.cancellation_reason || ''
    };
  }

  // Get booking by ID
  static async getBookingById(bookingId: string, userId?: string): Promise<Booking | null> {
    try {
      const conditions = [];
      
      if (userId) {
        conditions.push({ field: 'userId', operator: '==', value: userId });
      }

      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking || (userId && booking.userId !== userId)) {
        return null;
      }

      return this.mapFirestoreBookingToBooking(booking);
    } catch (error) {
      console.error('Error getting booking by ID:', error);
      throw new Error('Failed to get booking');
    }
  }

  // Create new booking
  static async createBooking(bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>): Promise<Booking> {
    try {
      const booking = {
        ...bookingData,
        status: bookingData.status || 'pending',
        paidAmount: bookingData.paidAmount || 0,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const bookingId = await DatabaseService.create('bookings', booking);
      const createdBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!createdBooking) {
        throw new Error('Failed to retrieve created booking');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...createdBooking
      });
    } catch (error) {
      console.error('Error creating booking:', error);
      throw new Error('Failed to create booking');
    }
  }

  // Update booking status
  static async updateBookingStatus(bookingId: string, status: BookingStatus, userId?: string): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (userId && booking.userId !== userId) {
        throw new Error('Not authorized to update this booking');
      }

      const updateData: any = {
        status,
        updatedAt: Timestamp.now()
      };

      if (status === 'completed') {
        updateData.completedAt = Timestamp.now();
      } else if (status === 'cancelled') {
        updateData.cancelledAt = Timestamp.now();
      }

      await DatabaseService.update('bookings', bookingId, updateData);
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!updatedBooking) {
        throw new Error('Failed to retrieve updated booking');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw new Error('Failed to update booking status');
    }
  }

  // Cancel booking
  static async cancelBooking(bookingId: string, userId: string, reason?: string): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (booking.userId !== userId) {
        throw new Error('Not authorized to cancel this booking');
      }

      const updateData = {
        status: 'cancelled' as const,
        cancelledAt: Timestamp.now(),
        cancellationReason: reason || 'No reason provided',
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!updatedBooking) {
        throw new Error('Failed to retrieve cancelled booking');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw new Error('Failed to cancel booking');
    }
  }

  // Reschedule booking
  static async rescheduleBooking(
    bookingId: string, 
    userId: string, 
    newDate: string, 
    newTime: string
  ): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (booking.userId !== userId) {
        throw new Error('Not authorized to reschedule this booking');
      }

      if (!['pending', 'confirmed'].includes(booking.status)) {
        throw new Error('Only pending or confirmed bookings can be rescheduled');
      }

      const updateData = {
        scheduledDate: newDate,
        scheduledTime: newTime,
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!updatedBooking) {
        throw new Error('Failed to retrieve rescheduled booking');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      throw new Error('Failed to reschedule booking');
    }
  }

  // Add review and rating
  static async addReview(
    bookingId: string, 
    userId: string, 
    rating: number, 
    review: string
  ): Promise<Booking> {
    try {
      if (rating < 1 || rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (booking.userId !== userId) {
        throw new Error('Not authorized to review this booking');
      }

      if (booking.status !== 'completed') {
        throw new Error('Only completed bookings can be reviewed');
      }

      const updateData = {
        rating,
        review,
        reviewDate: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      
      if (!updatedBooking) {
        throw new Error('Failed to retrieve booking after review');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error adding review:', error);
      throw new Error('Failed to add review');
    }
  }

  // Process payment for booking
  static async processPayment(
    bookingId: string, 
    userId: string, 
    amount: number, 
    paymentMethod: string
  ): Promise<Booking> {
    try {
      const booking = await DatabaseService.getById('bookings', bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }
      
      if (booking.userId !== userId) {
        throw new Error('Not authorized to process payment for this booking');
      }

      // Update booking with payment
      const updateData = {
        paidAmount: (booking.paidAmount || 0) + amount,
        paymentMethod,
        updatedAt: Timestamp.now()
      };

      await DatabaseService.update('bookings', bookingId, updateData);

      // Create transaction record
      await DatabaseService.create('transactions', {
        userId,
        bookingId,
        type: 'payment',
        amount,
        description: `Payment for booking ${bookingId}`,
        paymentMethod,
        status: 'completed',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });

      // Return updated booking
      const updatedBooking = await DatabaseService.getById('bookings', bookingId);
      if (!updatedBooking) {
        throw new Error('Failed to retrieve booking after payment');
      }

      return this.mapFirestoreBookingToBooking({
        id: bookingId,
        ...updatedBooking
      });
    } catch (error) {
      console.error('Error processing payment:', error);
      throw new Error('Failed to process payment');
    }
  }

  // Get upcoming bookings
  static async getUpcomingBookings(userId: string): Promise<Booking[]> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      const bookings = await DatabaseService.query(
        'bookings',
        [
          { field: 'userId', operator: '==', value: userId },
          { field: 'status', operator: 'in', value: ['pending', 'confirmed'] },
          { field: 'scheduledDate', operator: '>=', value: today }
        ],
        'scheduledDate',
        'asc'
      );

      return bookings.map(booking => this.mapFirestoreBookingToBooking(booking));
    } catch (error) {
      console.error('Error getting upcoming bookings:', error);
      throw new Error('Failed to get upcoming bookings');
    }
  }

  // Helper method to map database booking to Booking interface (kept for backward compatibility)
  private static mapDatabaseBookingToBooking(dbBooking: any): Booking {
    return this.mapFirestoreBookingToBooking(dbBooking);
  }
}
