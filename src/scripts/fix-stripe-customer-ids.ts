/**
 * Migration script to fix Stripe customer ID issues
 * This script will:
 * 1. Find users with invalid Stripe customer IDs (Firebase UIDs instead of proper Stripe IDs)
 * 2. Clear invalid customer IDs from user records
 * 3. Create proper Stripe customers when needed
 * 
 * Run this script to fix existing data where Firebase UIDs were used as Stripe customer IDs
 */

import { db } from '@/lib/firebase/config';
import { collection, getDocs, doc, updateDoc, query, where } from 'firebase/firestore';
import { COLLECTIONS } from '@/lib/database';
import { isValidStripeCustomerId, cleanupOrphanedCustomerReferences } from '@/lib/stripe/customer-utils';

interface UserData {
  id: string;
  email: string;
  name?: string;
  role?: string;
  stripeCustomerId?: string;
}

async function fixStripeCustomerIds() {
  console.log('🔧 Starting Stripe customer ID migration...');
  
  try {
    // Fix pet owners
    await fixUsersInCollection(COLLECTIONS.USERS, 'pet_owner');
    
    // Fix providers
    await fixUsersInCollection(COLLECTIONS.PROVIDERS, 'provider');
    
    console.log('✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

async function fixUsersInCollection(collectionName: string, userType: 'pet_owner' | 'provider') {
  console.log(`\n🔍 Checking ${collectionName} collection...`);
  
  const usersRef = collection(db, collectionName);
  const snapshot = await getDocs(usersRef);
  
  let totalUsers = 0;
  let usersWithInvalidIds = 0;
  let usersFixed = 0;
  
  for (const docSnapshot of snapshot.docs) {
    totalUsers++;
    const userData = docSnapshot.data() as UserData;
    const userId = docSnapshot.id;
    
    // Check if user has a stripeCustomerId
    if (userData.stripeCustomerId) {
      // Check if it's a valid Stripe customer ID
      if (!isValidStripeCustomerId(userData.stripeCustomerId)) {
        usersWithInvalidIds++;
        console.log(`❌ Invalid customer ID found: ${userData.stripeCustomerId} for user ${userId}`);
        
        try {
          // Clean up the invalid reference
          await cleanupOrphanedCustomerReferences(userId, userType);
          usersFixed++;
          console.log(`✅ Cleaned up invalid customer ID for user ${userId}`);
        } catch (error) {
          console.error(`❌ Failed to clean up user ${userId}:`, error);
        }
      } else {
        console.log(`✅ Valid customer ID: ${userData.stripeCustomerId} for user ${userId}`);
      }
    } else {
      console.log(`ℹ️  No customer ID for user ${userId} (will be created when needed)`);
    }
  }
  
  console.log(`\n📊 ${collectionName} Summary:`);
  console.log(`   Total users: ${totalUsers}`);
  console.log(`   Users with invalid IDs: ${usersWithInvalidIds}`);
  console.log(`   Users fixed: ${usersFixed}`);
}

// Additional utility functions for manual fixes

/**
 * Fix a specific user's Stripe customer ID
 */
export async function fixSpecificUser(userId: string, userType: 'pet_owner' | 'provider' = 'pet_owner') {
  console.log(`🔧 Fixing specific user: ${userId}`);
  
  try {
    await cleanupOrphanedCustomerReferences(userId, userType);
    console.log(`✅ Fixed user ${userId}`);
  } catch (error) {
    console.error(`❌ Failed to fix user ${userId}:`, error);
  }
}

/**
 * Check if a specific customer ID is valid
 */
export function checkCustomerId(customerId: string): void {
  console.log(`🔍 Checking customer ID: ${customerId}`);
  
  if (isValidStripeCustomerId(customerId)) {
    console.log('✅ Valid Stripe customer ID');
  } else {
    console.log('❌ Invalid Stripe customer ID (likely a Firebase UID)');
    console.log('   Stripe customer IDs should start with "cus_"');
    console.log('   Firebase UIDs are longer alphanumeric strings');
  }
}

/**
 * Find all users with invalid customer IDs
 */
export async function findInvalidCustomerIds(): Promise<void> {
  console.log('🔍 Finding all users with invalid customer IDs...');
  
  const collections = [
    { name: COLLECTIONS.USERS, type: 'pet_owner' as const },
    { name: COLLECTIONS.PROVIDERS, type: 'provider' as const }
  ];
  
  for (const { name: collectionName, type } of collections) {
    console.log(`\nChecking ${collectionName}...`);
    
    const usersRef = collection(db, collectionName);
    const snapshot = await getDocs(usersRef);
    
    const invalidUsers: Array<{ id: string; customerId: string }> = [];
    
    snapshot.docs.forEach((doc) => {
      const data = doc.data();
      if (data.stripeCustomerId && !isValidStripeCustomerId(data.stripeCustomerId)) {
        invalidUsers.push({
          id: doc.id,
          customerId: data.stripeCustomerId
        });
      }
    });
    
    if (invalidUsers.length > 0) {
      console.log(`❌ Found ${invalidUsers.length} users with invalid customer IDs:`);
      invalidUsers.forEach(({ id, customerId }) => {
        console.log(`   User: ${id}, Invalid ID: ${customerId}`);
      });
    } else {
      console.log('✅ No invalid customer IDs found');
    }
  }
}

// Export the main function for use in other scripts
export { fixStripeCustomerIds };

// If running this script directly
if (require.main === module) {
  fixStripeCustomerIds();
}

// Usage examples:
// 
// 1. Run full migration:
//    npm run fix-stripe-ids
//
// 2. Check a specific customer ID:
//    checkCustomerId('cus_ABC123'); // Valid
//    checkCustomerId('xQFPtz1msBhmDyMsPGxB3Sk3CTf2'); // Invalid (Firebase UID)
//
// 3. Fix a specific user:
//    fixSpecificUser('xQFPtz1msBhmDyMsPGxB3Sk3CTf2', 'pet_owner');
//
// 4. Find all invalid IDs:
//    findInvalidCustomerIds();
